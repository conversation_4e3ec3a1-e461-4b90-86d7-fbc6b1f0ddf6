import 'dart:io';

/// <PERSON><PERSON><PERSON> to automatically apply SizeConstants to all Dart files
/// This script searches for common size patterns and replaces them with SizeConstants
void main() async {
  print('🚀 بدء تطبيق نظام الأحجام الجديد...');
  
  final libDir = Directory('lib');
  if (!libDir.existsSync()) {
    print('❌ مجلد lib غير موجود');
    return;
  }

  // البحث عن جميع ملفات Dart
  final dartFiles = await _findDartFiles(libDir);
  print('📁 تم العثور على ${dartFiles.length} ملف Dart');

  int updatedFiles = 0;
  int totalReplacements = 0;

  for (final file in dartFiles) {
    final result = await _processFile(file);
    if (result > 0) {
      updatedFiles++;
      totalReplacements += result;
      print('✅ تم تحديث ${file.path} - ${result} استبدال');
    }
  }

  print('\n🎉 تم الانتهاء!');
  print('📊 الإحصائيات:');
  print('   - الملفات المحدثة: $updatedFiles');
  print('   - إجمالي الاستبدالات: $totalReplacements');
}

/// البحث عن جميع ملفات Dart في المجلد
Future<List<File>> _findDartFiles(Directory dir) async {
  final files = <File>[];
  
  await for (final entity in dir.list(recursive: true)) {
    if (entity is File && entity.path.endsWith('.dart')) {
      // تجاهل ملفات معينة
      if (!entity.path.contains('generated') && 
          !entity.path.contains('.g.dart') &&
          !entity.path.contains('.freezed.dart')) {
        files.add(entity);
      }
    }
  }
  
  return files;
}

/// معالجة ملف واحد
Future<int> _processFile(File file) async {
  try {
    String content = await file.readAsString();
    final originalContent = content;
    int replacements = 0;

    // التحقق من وجود import للـ SizeConstants
    bool hasSizeConstantsImport = content.contains("import '../../../core/constants/size_constants.dart'") ||
                                  content.contains("import '../../core/constants/size_constants.dart'") ||
                                  content.contains("import '../core/constants/size_constants.dart'");

    // خريطة الاستبدالات
    final replacementMap = <String, String>{
      // Font sizes
      r'fontSize:\s*48': 'fontSize: SizeConstants.font3XL',
      r'fontSize:\s*36': 'fontSize: SizeConstants.font2XL',
      r'fontSize:\s*32': 'fontSize: SizeConstants.fontXL',
      r'fontSize:\s*28': 'fontSize: SizeConstants.fontLG',
      r'fontSize:\s*24': 'fontSize: SizeConstants.fontMD',
      r'fontSize:\s*20': 'fontSize: SizeConstants.fontBase',
      r'fontSize:\s*18': 'fontSize: SizeConstants.fontSM',
      r'fontSize:\s*16': 'fontSize: SizeConstants.fontBase',
      r'fontSize:\s*14': 'fontSize: SizeConstants.fontSM',
      r'fontSize:\s*12': 'fontSize: SizeConstants.fontXS',
      r'fontSize:\s*10': 'fontSize: SizeConstants.fontXS',

      // Padding and margins
      r'EdgeInsets\.all\(48\)': 'EdgeInsets.all(SizeConstants.space3XL)',
      r'EdgeInsets\.all\(40\)': 'EdgeInsets.all(SizeConstants.space2XL)',
      r'EdgeInsets\.all\(32\)': 'EdgeInsets.all(SizeConstants.spaceXL)',
      r'EdgeInsets\.all\(24\)': 'EdgeInsets.all(SizeConstants.spaceLG)',
      r'EdgeInsets\.all\(20\)': 'EdgeInsets.all(SizeConstants.spaceMD)',
      r'EdgeInsets\.all\(16\)': 'EdgeInsets.all(SizeConstants.spaceBase)',
      r'EdgeInsets\.all\(12\)': 'EdgeInsets.all(SizeConstants.spaceSM)',
      r'EdgeInsets\.all\(8\)': 'EdgeInsets.all(SizeConstants.spaceXS)',
      r'EdgeInsets\.all\(4\)': 'EdgeInsets.all(SizeConstants.spaceXS)',

      // Symmetric padding
      r'EdgeInsets\.symmetric\(\s*horizontal:\s*24': 'EdgeInsets.symmetric(horizontal: SizeConstants.spaceLG',
      r'EdgeInsets\.symmetric\(\s*horizontal:\s*20': 'EdgeInsets.symmetric(horizontal: SizeConstants.spaceMD',
      r'EdgeInsets\.symmetric\(\s*horizontal:\s*16': 'EdgeInsets.symmetric(horizontal: SizeConstants.spaceBase',
      r'EdgeInsets\.symmetric\(\s*horizontal:\s*12': 'EdgeInsets.symmetric(horizontal: SizeConstants.spaceSM',
      r'EdgeInsets\.symmetric\(\s*horizontal:\s*8': 'EdgeInsets.symmetric(horizontal: SizeConstants.spaceXS',

      r'EdgeInsets\.symmetric\(\s*vertical:\s*24': 'EdgeInsets.symmetric(vertical: SizeConstants.spaceLG',
      r'EdgeInsets\.symmetric\(\s*vertical:\s*20': 'EdgeInsets.symmetric(vertical: SizeConstants.spaceMD',
      r'EdgeInsets\.symmetric\(\s*vertical:\s*16': 'EdgeInsets.symmetric(vertical: SizeConstants.spaceBase',
      r'EdgeInsets\.symmetric\(\s*vertical:\s*12': 'EdgeInsets.symmetric(vertical: SizeConstants.spaceSM',
      r'EdgeInsets\.symmetric\(\s*vertical:\s*8': 'EdgeInsets.symmetric(vertical: SizeConstants.spaceXS',

      // SizedBox heights and widths
      r'SizedBox\(height:\s*48\)': 'SizedBox(height: SizeConstants.space3XL)',
      r'SizedBox\(height:\s*40\)': 'SizedBox(height: SizeConstants.space2XL)',
      r'SizedBox\(height:\s*32\)': 'SizedBox(height: SizeConstants.spaceXL)',
      r'SizedBox\(height:\s*24\)': 'SizedBox(height: SizeConstants.spaceLG)',
      r'SizedBox\(height:\s*20\)': 'SizedBox(height: SizeConstants.spaceMD)',
      r'SizedBox\(height:\s*16\)': 'SizedBox(height: SizeConstants.spaceBase)',
      r'SizedBox\(height:\s*12\)': 'SizedBox(height: SizeConstants.spaceSM)',
      r'SizedBox\(height:\s*8\)': 'SizedBox(height: SizeConstants.spaceXS)',
      r'SizedBox\(height:\s*4\)': 'SizedBox(height: SizeConstants.spaceXS)',

      r'SizedBox\(width:\s*24\)': 'SizedBox(width: SizeConstants.spaceLG)',
      r'SizedBox\(width:\s*20\)': 'SizedBox(width: SizeConstants.spaceMD)',
      r'SizedBox\(width:\s*16\)': 'SizedBox(width: SizeConstants.spaceBase)',
      r'SizedBox\(width:\s*12\)': 'SizedBox(width: SizeConstants.spaceSM)',
      r'SizedBox\(width:\s*8\)': 'SizedBox(width: SizeConstants.spaceXS)',
      r'SizedBox\(width:\s*4\)': 'SizedBox(width: SizeConstants.spaceXS)',

      // Icon sizes
      r'size:\s*64': 'size: SizeConstants.icon2XL',
      r'size:\s*48': 'size: SizeConstants.iconXL',
      r'size:\s*32': 'size: SizeConstants.iconLG',
      r'size:\s*24': 'size: SizeConstants.iconMD',
      r'size:\s*20': 'size: SizeConstants.iconSM',
      r'size:\s*16': 'size: SizeConstants.iconXS',

      // Button heights
      r'height:\s*56': 'height: SizeConstants.buttonHeightXL',
      r'height:\s*48': 'height: SizeConstants.buttonHeightLG',
      r'height:\s*44': 'height: SizeConstants.buttonHeightLG',
      r'height:\s*40': 'height: SizeConstants.buttonHeightMD',
      r'height:\s*36': 'height: SizeConstants.buttonHeightMD',
      r'height:\s*32': 'height: SizeConstants.buttonHeightSM',

      // Border radius
      r'BorderRadius\.circular\(24\)': 'BorderRadius.circular(SizeConstants.radius2XL)',
      r'BorderRadius\.circular\(20\)': 'BorderRadius.circular(SizeConstants.radiusXL)',
      r'BorderRadius\.circular\(16\)': 'BorderRadius.circular(SizeConstants.radiusLG)',
      r'BorderRadius\.circular\(12\)': 'BorderRadius.circular(SizeConstants.radiusMD)',
      r'BorderRadius\.circular\(8\)': 'BorderRadius.circular(SizeConstants.radiusSM)',
      r'BorderRadius\.circular\(4\)': 'BorderRadius.circular(SizeConstants.radiusXS)',

      // Const replacements
      r'const EdgeInsets\.all\(SizeConstants\.': 'EdgeInsets.all(SizeConstants.',
      r'const SizedBox\(height: SizeConstants\.': 'SizedBox(height: SizeConstants.',
      r'const SizedBox\(width: SizeConstants\.': 'SizedBox(width: SizeConstants.',
    };

    // تطبيق الاستبدالات
    for (final entry in replacementMap.entries) {
      final regex = RegExp(entry.key);
      final matches = regex.allMatches(content);
      if (matches.isNotEmpty) {
        content = content.replaceAll(regex, entry.value);
        replacements += matches.length;
        
        // إضافة import إذا لم يكن موجوداً
        if (!hasSizeConstantsImport) {
          content = _addSizeConstantsImport(content, file.path);
          hasSizeConstantsImport = true;
        }
      }
    }

    // كتابة الملف إذا تم تغييره
    if (content != originalContent) {
      await file.writeAsString(content);
    }

    return replacements;
  } catch (e) {
    print('❌ خطأ في معالجة ${file.path}: $e');
    return 0;
  }
}

/// إضافة import للـ SizeConstants
String _addSizeConstantsImport(String content, String filePath) {
  // تحديد المسار النسبي للـ SizeConstants
  String importPath;
  if (filePath.contains('lib/presentation/pages/')) {
    importPath = "import '../../../core/constants/size_constants.dart';";
  } else if (filePath.contains('lib/presentation/widgets/')) {
    importPath = "import '../../../core/constants/size_constants.dart';";
  } else if (filePath.contains('lib/core/')) {
    importPath = "import '../constants/size_constants.dart';";
  } else {
    importPath = "import 'core/constants/size_constants.dart';";
  }

  // البحث عن آخر import
  final lines = content.split('\n');
  int lastImportIndex = -1;
  
  for (int i = 0; i < lines.length; i++) {
    if (lines[i].startsWith('import ')) {
      lastImportIndex = i;
    }
  }

  if (lastImportIndex != -1) {
    lines.insert(lastImportIndex + 1, importPath);
    return lines.join('\n');
  }

  return content;
}

<svg width="22" height="44" viewBox="0 0 22 44" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_48_711)">
<g filter="url(#filter0_d_48_711)">
<path d="M4.99976 2.39551V23.6042" stroke="white" stroke-width="3" stroke-linecap="round"/>
</g>
<g filter="url(#filter1_d_48_711)">
<path d="M17.1042 14.5V35.7087" stroke="white" stroke-width="3" stroke-linecap="round"/>
</g>
</g>
<defs>
<filter id="filter0_d_48_711" x="0.499756" y="0.895508" width="9" height="30.2087" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.160784 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_48_711"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_48_711" result="shape"/>
</filter>
<filter id="filter1_d_48_711" x="12.6042" y="13" width="9" height="30.2087" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.160784 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_48_711"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_48_711" result="shape"/>
</filter>
<clipPath id="clip0_48_711">
<rect width="22" height="44" fill="white"/>
</clipPath>
</defs>
</svg>

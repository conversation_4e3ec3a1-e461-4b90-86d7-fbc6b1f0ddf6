import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';
import 'package:flutter/services.dart';

/// Accessibility helper class for improving app accessibility
/// Following Single Responsibility Principle by focusing only on accessibility features
class AccessibilityHelper {
  /// Announce a message to screen readers
  static void announceMessage(String message) {
    try {
      SemanticsService.announce(message, TextDirection.rtl);
    } catch (e) {
      // Silently fail if accessibility services are not available
    }
  }

  /// Set focus to a specific widget
  static void setFocus(FocusNode focusNode) {
    try {
      if (focusNode.canRequestFocus) {
        focusNode.requestFocus();
      }
    } catch (e) {
      // Silently fail if focus cannot be set
    }
  }

  /// Create semantic label for buttons
  static String createButtonLabel(String text, {String? hint}) {
    if (hint != null) {
      return '$text. $hint';
    }
    return text;
  }

  /// Create semantic label for text fields
  static String createTextFieldLabel(
    String label, {
    bool isRequired = false,
    String? hint,
  }) {
    String semanticLabel = label;

    if (isRequired) {
      semanticLabel += '. مطلوب';
    }

    if (hint != null) {
      semanticLabel += '. $hint';
    }

    return semanticLabel;
  }

  /// Get semantic properties for interactive elements
  static SemanticsProperties getInteractiveSemantics({
    required String label,
    String? hint,
    bool isButton = false,
    bool isTextField = false,
    bool isEnabled = true,
    VoidCallback? onTap,
  }) {
    return SemanticsProperties(
      label: label,
      hint: hint,
      button: isButton,
      textField: isTextField,
      enabled: isEnabled,
      onTap: onTap,
    );
  }

  /// Wrap widget with accessibility semantics
  static Widget wrapWithSemantics({
    required Widget child,
    required String label,
    String? hint,
    bool excludeSemantics = false,
    bool isButton = false,
    bool isTextField = false,
    bool isEnabled = true,
    VoidCallback? onTap,
  }) {
    if (excludeSemantics) {
      return ExcludeSemantics(child: child);
    }

    return Semantics(
      label: label,
      hint: hint,
      button: isButton,
      textField: isTextField,
      enabled: isEnabled,
      onTap: onTap,
      child: child,
    );
  }

  /// Create accessible button wrapper
  static Widget createAccessibleButton({
    required Widget child,
    required String label,
    String? hint,
    required VoidCallback? onPressed,
    bool isEnabled = true,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      button: true,
      enabled: isEnabled && onPressed != null,
      onTap: onPressed,
      child: child,
    );
  }

  /// Create accessible text field wrapper
  static Widget createAccessibleTextField({
    required Widget child,
    required String label,
    String? hint,
    bool isRequired = false,
    bool isEnabled = true,
  }) {
    final semanticLabel = createTextFieldLabel(
      label,
      isRequired: isRequired,
      hint: hint,
    );

    return Semantics(
      label: semanticLabel,
      textField: true,
      enabled: isEnabled,
      child: child,
    );
  }

  /// Provide haptic feedback
  static void provideFeedback([String type = 'light']) {
    try {
      switch (type) {
        case 'light':
          HapticFeedback.lightImpact();
          break;
        case 'medium':
          HapticFeedback.mediumImpact();
          break;
        case 'heavy':
          HapticFeedback.heavyImpact();
          break;
        case 'selection':
          HapticFeedback.selectionClick();
          break;
        case 'vibrate':
          HapticFeedback.vibrate();
          break;
        default:
          HapticFeedback.lightImpact();
      }
    } catch (e) {
      // Silently fail if haptic feedback is not available
    }
  }

  /// Check if accessibility features are enabled
  static bool get isAccessibilityEnabled {
    try {
      return WidgetsBinding
          .instance
          .platformDispatcher
          .accessibilityFeatures
          .accessibleNavigation;
    } catch (e) {
      return false;
    }
  }

  /// Check if screen reader is enabled
  static bool get isScreenReaderEnabled {
    try {
      return WidgetsBinding
          .instance
          .platformDispatcher
          .accessibilityFeatures
          .accessibleNavigation;
    } catch (e) {
      return false;
    }
  }

  /// Get recommended minimum touch target size
  static Size get minimumTouchTargetSize => const Size(48.0, 48.0);

  /// Check if touch target meets minimum size requirements
  static bool isTouchTargetSizeValid(Size size) {
    return size.width >= minimumTouchTargetSize.width &&
        size.height >= minimumTouchTargetSize.height;
  }

  /// Create accessible card wrapper
  static Widget createAccessibleCard({
    required Widget child,
    required String label,
    String? hint,
    VoidCallback? onTap,
    bool isEnabled = true,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      button: onTap != null,
      enabled: isEnabled,
      onTap: onTap,
      child: child,
    );
  }

  /// Create accessible list item
  static Widget createAccessibleListItem({
    required Widget child,
    required String label,
    String? hint,
    VoidCallback? onTap,
    bool isEnabled = true,
    int? index,
    int? totalItems,
  }) {
    String semanticLabel = label;

    if (index != null && totalItems != null) {
      semanticLabel += '. عنصر ${index + 1} من $totalItems';
    }

    return Semantics(
      label: semanticLabel,
      hint: hint,
      button: onTap != null,
      enabled: isEnabled,
      onTap: onTap,
      child: child,
    );
  }

  /// Announce page change to screen readers
  static void announcePageChange(String pageName) {
    announceMessage('انتقلت إلى صفحة $pageName');
  }

  /// Announce loading state
  static void announceLoading() {
    announceMessage('جاري التحميل');
  }

  /// Announce completion
  static void announceCompletion(String action) {
    announceMessage('تم $action بنجاح');
  }

  /// Announce error
  static void announceError(String error) {
    announceMessage('خطأ: $error');
  }

  /// Create accessible navigation semantics
  static SemanticsProperties getNavigationSemantics(String destination) {
    return SemanticsProperties(
      label: 'الانتقال إلى $destination',
      hint: 'اضغط للانتقال',
      button: true,
    );
  }

  /// Create accessible form field semantics
  static SemanticsProperties getFormFieldSemantics({
    required String label,
    bool isRequired = false,
    String? errorText,
    String? helperText,
  }) {
    String semanticLabel = label;
    String? semanticHint;

    if (isRequired) {
      semanticLabel += ' (مطلوب)';
    }

    if (errorText != null) {
      semanticHint = 'خطأ: $errorText';
    } else if (helperText != null) {
      semanticHint = helperText;
    }

    return SemanticsProperties(
      label: semanticLabel,
      hint: semanticHint,
      textField: true,
    );
  }

  /// Announce form success
  static void announceFormSuccess(String message) {
    announceMessage('نجح: $message');
  }

  /// Announce form error
  static void announceFormError(String error) {
    announceMessage('خطأ في النموذج: $error');
  }

  /// Announce in Arabic
  static void announceArabic(String message) {
    announceMessage(message);
  }

  /// Create validation label
  static String createValidationLabel(String label, {bool isRequired = false}) {
    if (isRequired) {
      return '$label (مطلوب)';
    }
    return label;
  }
}

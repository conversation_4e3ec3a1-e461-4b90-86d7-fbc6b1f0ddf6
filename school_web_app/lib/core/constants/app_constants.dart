/// Application constants
/// Following Single Responsibility Principle by focusing only on constants
class AppConstants {
  // Private constructor to prevent instantiation
  AppConstants._();

  // App information
  static const String appName = 'باصاتي';
  static const String appVersion = '1.0.0';
  static const String appTagline = 'آمن. فعّال. موثوق.';
  static const String appDescription = 'تطبيق إدارة النقل المدرسي';
  static const String appDeveloper = 'Busaty';
  static const String appWebsite = 'https://busaty.com';
  static const String appEmail = '<EMAIL>';
  static const String appPhone = '+966 12 345 6789';

  // API endpoints - Using the same structure as the original SchoolX app
  static const String fcmUrl = "https://fcm.googleapis.com/fcm/send";
  static const String socketUrl = "https://node.busatyapp.com/";
  static const String firebase = "auth/firebase-login";

  // Base URLs
  static const String base = "https://test.busatyapp.com/";
  static const String baseUrl = "${base}api/schools/";
  static const String apiUrl =
      baseUrl; // For backward compatibility with existing code

  // Authentication endpoints
  static const String loginEndpoint = "login";
  static const String registerEndpoint = "register";
  static const String forgotPasswordEndpoint = "forgot-password";
  static const String resetPasswordEndpoint = "reset-password";
  static const String verifyEmailEndpoint = "verify";
  static const String changePasswordEndpoint = "general/password/update";
  static const String logoutEndpoint = "logout";
  static const String refreshTokenEndpoint = "refresh-token";

  // User endpoints
  static const String userEndpoint = "user";
  static const String userProfileEndpoint = "user";
  static const String userUpdateProfileEndpoint = "general/data/update";

  // School endpoints
  static const String schoolEndpoint = ""; // Base URL already includes schools

  // Student endpoints
  static const String studentEndpoint = "students";
  static const String deleteStudentEndpoint = "students/destroy/";
  static const String downloadStudentsExampleFile = "students/download";
  static const String downloadStudentsPDFFile = "buses/export/data/pdf";
  static const String fileNameAndExtension = "students/download/show";

  // Bus endpoints
  static const String busEndpoint = "buses/all";
  static const String deleteBusEndpoint = "buses/destroy/";
  static const String buslocationtrackerEndpoint = "trips/map/show/";
  static const String availableBusInDriver = "buses/show/availableAdd/driver";
  static const String availableBusInDriverSupervisor =
      "buses/show/availableAdd/admins";

  // Driver endpoints
  static const String driverEndpoint = "attendants/all/driver";
  static const String deleteSupervisorAndDriverEndpoint = "attendants/destroy";

  // Supervisor endpoints
  static const String supervisorEndpoint = "attendants/all/admins";

  // Trip endpoints
  static const String tripEndpoint = "trips";
  static const String tripTrackEndpoint = "trips/map/show";
  static const String schoolCurrentTrips = "trips/current";
  static const String schoolPreviousTrips = "trips/previous/index";
  static const String tripRouteDetails = "general/trips/routes/";
  static const String tripAbsentStudents = "general/trips/absents/";
  static const String tripAttendantStudents = "general/trips/attendants/";

  // Attendance endpoints
  static const String attendanceEndpoint = "absences/index";
  static const String temporaryAddressesEndpoint = "temporary-addresses";
  static const String changeAddressRequestsEndpoint = "addresses/index";
  static const String acceptChangeAddressRequestsEndpoint =
      "addresses/accepted/";
  static const String refuseChangeAddressRequestsEndpoint =
      "addresses/unaccepted/";

  // Notification endpoints
  static const String notificationEndpoint = "notifications";
  static const String notificationsEndpoint = "notifications";
  static const String fcmTokensEndpoint = "fcm-tokens";
  static const String allParentsFcmTokensEndpoint =
      "notifications/allParentFirebaseTokens";
  static const String parentFcmTokensEndpoint =
      "notifications/parentFirebaseTokens";
  static const String showNotificationsEndpoint = "parents/notifications";
  static const String supervisorsFcmTokensEndpoint =
      "notifications/attendantFirebaseTokens";
  static const String storeBusNotificationEndpoint =
      "notifications/storeBusNotification";

  // Parent endpoints
  static const String parentEndpoint = "parents/index";
  static const String parentShowEndpoint = "parents/show";

  // Settings and other endpoints
  static const String settingsEndpoint = "settings";
  static const String gradeEndpoint = "grades/all";
  static const String religionEndpoint = "general/religion";
  static const String genderEndpoint = "general/gender";
  static const String typeBloodEndpoint = "general/type/blood";
  static const String couponEndpoint = "subscriptions/coupon";
  static const String adsEndpoint = "general/ades";
  static const String completeProfileEndpoint = "data/complete";
  static const String questionHelpEndpoint = "question";

  // Storage keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user';
  static const String userIdKey = 'user_id';
  static const String userNameKey = 'user_name';
  static const String userEmailKey = 'user_email';
  static const String rememberMeKey = 'remember_me';
  static const String darkModeKey = 'dark_mode';
  static const String languageKey = 'language';
  static const String firstRunKey = 'first_run';
  static const String notificationsKey = 'notifications';

  // Default values
  static const String defaultLanguage = 'ar';
  static const bool defaultThemeMode = false; // false = light, true = dark
  static const bool defaultNotifications = true;
  static const bool defaultRememberMe = false;

  // Development flags
  static const bool useMockData = false; // Using real APIs instead of mock data
  static const bool fallbackToMockData =
      false; // Do NOT fallback to mock data if API fails

  // Pagination
  static const int defaultPageSize = 10;
  static const int defaultPage = 1;

  // Timeouts
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds

  // Animation durations
  static const int splashDuration = 2000; // 2 seconds
  static const int animationDuration = 300; // 300 milliseconds

  // Validation constants
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 20;
  static const int minNameLength = 3;
  static const int maxNameLength = 50;
  static const int minPhoneLength = 9;
  static const int maxPhoneLength = 15;
  static const String phoneRegex = r'^\+?[0-9]{9,15}$';
  static const String emailRegex = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
}

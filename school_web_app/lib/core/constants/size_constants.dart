import 'package:flutter/material.dart';

/// Size constants optimized for web applications
/// Following design system principles with smaller, web-appropriate sizes
class SizeConstants {
  // Private constructor to prevent instantiation
  SizeConstants._();

  // =============================================================================
  // FONT SIZES - Optimized for web readability
  // =============================================================================
  
  /// Extra small text (captions, labels)
  static const double fontXS = 10.0;
  
  /// Small text (secondary text, descriptions)
  static const double fontSM = 12.0;
  
  /// Base text size (body text, default)
  static const double fontBase = 14.0;
  
  /// Medium text (subheadings, important text)
  static const double fontMD = 16.0;
  
  /// Large text (headings, titles)
  static const double fontLG = 18.0;
  
  /// Extra large text (main headings)
  static const double fontXL = 20.0;
  
  /// 2X large text (page titles)
  static const double font2XL = 24.0;
  
  /// 3X large text (hero titles)
  static const double font3XL = 28.0;

  // =============================================================================
  // SPACING - Consistent spacing system
  // =============================================================================
  
  /// Extra small spacing
  static const double spaceXS = 4.0;
  
  /// Small spacing
  static const double spaceSM = 8.0;
  
  /// Base spacing
  static const double spaceBase = 12.0;
  
  /// Medium spacing
  static const double spaceMD = 16.0;
  
  /// Large spacing
  static const double spaceLG = 20.0;
  
  /// Extra large spacing
  static const double spaceXL = 24.0;
  
  /// 2X large spacing
  static const double space2XL = 32.0;
  
  /// 3X large spacing
  static const double space3XL = 40.0;

  // =============================================================================
  // COMPONENT SIZES - Optimized for web interfaces
  // =============================================================================
  
  /// Button heights
  static const double buttonHeightSM = 32.0;
  static const double buttonHeightMD = 36.0;
  static const double buttonHeightLG = 40.0;
  static const double buttonHeightXL = 44.0;
  
  /// Input field heights
  static const double inputHeightSM = 32.0;
  static const double inputHeightMD = 36.0;
  static const double inputHeightLG = 40.0;
  
  /// Icon sizes
  static const double iconXS = 12.0;
  static const double iconSM = 16.0;
  static const double iconMD = 20.0;
  static const double iconLG = 24.0;
  static const double iconXL = 28.0;
  static const double icon2XL = 32.0;
  
  /// Avatar sizes
  static const double avatarSM = 24.0;
  static const double avatarMD = 32.0;
  static const double avatarLG = 40.0;
  static const double avatarXL = 48.0;
  
  /// Card padding
  static const double cardPaddingSM = 12.0;
  static const double cardPaddingMD = 16.0;
  static const double cardPaddingLG = 20.0;
  
  /// Border radius
  static const double radiusXS = 2.0;
  static const double radiusSM = 4.0;
  static const double radiusMD = 6.0;
  static const double radiusLG = 8.0;
  static const double radiusXL = 12.0;
  static const double radius2XL = 16.0;

  // =============================================================================
  // LAYOUT SIZES - Container and layout dimensions
  // =============================================================================
  
  /// Sidebar width
  static const double sidebarWidth = 240.0;
  static const double sidebarWidthCollapsed = 60.0;
  
  /// Header height
  static const double headerHeight = 56.0;
  
  /// Table row height
  static const double tableRowHeight = 40.0;
  
  /// Card minimum height
  static const double cardMinHeight = 80.0;
  
  /// Modal widths
  static const double modalWidthSM = 400.0;
  static const double modalWidthMD = 500.0;
  static const double modalWidthLG = 600.0;
  static const double modalWidthXL = 800.0;

  // =============================================================================
  // RESPONSIVE BREAKPOINTS
  // =============================================================================
  
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;

  // =============================================================================
  // HELPER METHODS - Dynamic sizing based on screen size
  // =============================================================================
  
  /// Get responsive font size based on screen width
  static double getResponsiveFontSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < mobileBreakpoint) {
      return baseSize * 0.9; // 10% smaller on mobile
    } else if (screenWidth < tabletBreakpoint) {
      return baseSize; // Base size on tablet
    } else {
      return baseSize * 1.1; // 10% larger on desktop
    }
  }
  
  /// Get responsive spacing based on screen width
  static double getResponsiveSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < mobileBreakpoint) {
      return baseSpacing * 0.8; // 20% smaller on mobile
    } else if (screenWidth < tabletBreakpoint) {
      return baseSpacing; // Base spacing on tablet
    } else {
      return baseSpacing * 1.2; // 20% larger on desktop
    }
  }
  
  /// Get responsive icon size based on screen width
  static double getResponsiveIconSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < mobileBreakpoint) {
      return baseSize * 0.85; // 15% smaller on mobile
    } else if (screenWidth < tabletBreakpoint) {
      return baseSize; // Base size on tablet
    } else {
      return baseSize * 1.15; // 15% larger on desktop
    }
  }

  // =============================================================================
  // TEXT STYLES - Pre-defined text styles with web-optimized sizes
  // =============================================================================
  
  static TextStyle get headingLarge => const TextStyle(
    fontSize: font3XL,
    fontWeight: FontWeight.bold,
    height: 1.2,
  );
  
  static TextStyle get headingMedium => const TextStyle(
    fontSize: font2XL,
    fontWeight: FontWeight.w600,
    height: 1.3,
  );
  
  static TextStyle get headingSmall => const TextStyle(
    fontSize: fontXL,
    fontWeight: FontWeight.w600,
    height: 1.4,
  );
  
  static TextStyle get bodyLarge => const TextStyle(
    fontSize: fontMD,
    fontWeight: FontWeight.normal,
    height: 1.5,
  );
  
  static TextStyle get bodyMedium => const TextStyle(
    fontSize: fontBase,
    fontWeight: FontWeight.normal,
    height: 1.5,
  );
  
  static TextStyle get bodySmall => const TextStyle(
    fontSize: fontSM,
    fontWeight: FontWeight.normal,
    height: 1.4,
  );
  
  static TextStyle get labelLarge => const TextStyle(
    fontSize: fontBase,
    fontWeight: FontWeight.w500,
    height: 1.4,
  );
  
  static TextStyle get labelMedium => const TextStyle(
    fontSize: fontSM,
    fontWeight: FontWeight.w500,
    height: 1.3,
  );
  
  static TextStyle get labelSmall => const TextStyle(
    fontSize: fontXS,
    fontWeight: FontWeight.w500,
    height: 1.2,
  );

  // =============================================================================
  // BUTTON STYLES - Pre-defined button configurations
  // =============================================================================
  
  static ButtonStyle get buttonStyleSmall => ElevatedButton.styleFrom(
    minimumSize: const Size(80, buttonHeightSM),
    padding: const EdgeInsets.symmetric(horizontal: spaceSM),
    textStyle: const TextStyle(fontSize: fontSM),
  );
  
  static ButtonStyle get buttonStyleMedium => ElevatedButton.styleFrom(
    minimumSize: const Size(100, buttonHeightMD),
    padding: const EdgeInsets.symmetric(horizontal: spaceMD),
    textStyle: const TextStyle(fontSize: fontBase),
  );
  
  static ButtonStyle get buttonStyleLarge => ElevatedButton.styleFrom(
    minimumSize: const Size(120, buttonHeightLG),
    padding: const EdgeInsets.symmetric(horizontal: spaceLG),
    textStyle: const TextStyle(fontSize: fontMD),
  );

  // =============================================================================
  // LEGACY COMPATIBILITY - Mapping old sizes to new system
  // =============================================================================
  
  /// Legacy font sizes (mapped to new system)
  @Deprecated('Use SizeConstants.fontBase instead')
  static const double defaultFontSize = fontBase;
  
  @Deprecated('Use SizeConstants.fontLG instead')
  static const double largeFontSize = fontLG;
  
  @Deprecated('Use SizeConstants.fontSM instead')
  static const double smallFontSize = fontSM;
  
  /// Legacy spacing (mapped to new system)
  @Deprecated('Use SizeConstants.spaceMD instead')
  static const double defaultPadding = spaceMD;
  
  @Deprecated('Use SizeConstants.spaceLG instead')
  static const double largePadding = spaceLG;
  
  @Deprecated('Use SizeConstants.spaceSM instead')
  static const double smallPadding = spaceSM;
}

import 'package:equatable/equatable.dart';

/// Base failure class for the application
/// Following Liskov Substitution Principle by ensuring all failures can be used interchangeably
abstract class Failure extends Equatable {
  final String message;

  const Failure({required this.message});

  @override
  List<Object> get props => [message];
}

/// Server failure
class ServerFailure extends Failure {
  const ServerFailure({required super.message});
}

/// Cache failure
class CacheFailure extends Failure {
  const CacheFailure({required super.message});
}

/// Network failure
class NetworkFailure extends Failure {
  const NetworkFailure({required super.message});
}

/// Authentication failure
class AuthFailure extends Failure {
  const AuthFailure({required super.message});
}

/// Validation failure
class ValidationFailure extends Failure {
  final Map<String, dynamic>? errors;

  const ValidationFailure({required super.message, this.errors});

  @override
  List<Object> get props => [message, if (errors != null) errors!];
}

/// Not found failure
class NotFoundFailure extends Failure {
  const NotFoundFailure({required super.message});
}

/// Timeout failure
class TimeoutFailure extends Failure {
  const TimeoutFailure({required super.message});
}

/// Forbidden failure
class ForbiddenFailure extends Failure {
  const ForbiddenFailure({required super.message});
}

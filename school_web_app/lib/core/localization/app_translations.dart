import 'package:get/get.dart';

/// AppTranslations class for managing translations
/// This class implements the translations for the application using GetX
class AppTranslations extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {'en': en, 'ar': ar};

  // English translations
  Map<String, String> get en => {
    'BusatySchool': 'باصاتي - Busaty',
    'checkPassword': 'Check your Password',
    'checkEmail':
        'Email Created Successfully now check your email for verification code',
    'home': 'Home',
    'username': 'Username',
    'code': 'Code',
    'line': 'line',
    'birthdate': 'birthdate',
    'classrooms': 'classrooms',
    'classroom': 'classroom',
    'add_classroom': 'add_classroom',
    'classroom_name': 'classroom_name',
    'options': 'options',
    'children': 'children',
    'login_with_google': 'login with Google',
    'check_connection': 'Please check your internet connection',
    'account_disabled': 'This account has been disabled',
    'login_failed': '<PERSON><PERSON> failed. Please try again',
    'new_address': 'new_address',
    'add_student_to_bus': 'Add Students to Bus',
    'bus_name': 'Bus name',
    'login': 'Login',
    'notes': 'Notes',
    'email': 'Email',
    'password': 'Password',
    'notHaveAccount': 'Not Have Account ? ',
    'createAccount': 'Create Account',
    'forgetPassword': 'Forget Password ? ',
    'remember': 'Remember',
    'signup': 'Signup',
    'name': 'Name',
    'schoolName': 'School Name',
    'phoneNumber': 'Phone Number',
    'confirmPassword': 'Confirm Password',
    'haveAccount': 'Have Account ? ',
    'sendCodeAgain': 'send code again',
    'sendCode': 'Send Code',
    'forget': 'Forget Password',
    // Error pages
    'pageNotFound': 'Page Not Found',
    'pageNotFoundDescription':
        'The page you are looking for does not exist or has been moved.',
    'goHome': 'Go Home',
    'goBack': 'Go Back',
    'quickLinks': 'Quick Links',
    'sendCodeRegister':
        'code will be sent to the registered email to recover the account',
    'newPassword': 'New Password',
    'againPassword': 'Write Again Password',
    'passwordChangeSuccess': 'Password Change Success',
    'next': 'Next',
    'searchStudentHint': 'search the student',
    'searchDriverHint': 'search the driver',
    'addByHand': 'Add student ',
    'addByFile': 'Add students file',
    'studentName': 'student name',
    'bloodType': 'blood type',
    'dateOfBirth': 'Date of birth',
    'educationalLevel': 'educational level',
    'city': 'city',
    'addBus': 'add bus',
    'add': 'Add',
    'addFile': 'Add File',
    'address': 'Address',
    'nationalId': 'National ID',
    'allDrivers': 'All Drivers',
    'allSupervisors': 'All Supervisors',
    'searchSupervisorHint': 'Search Supervisor',
    'addStudent': 'Add Student',
    'editStudent': 'Edit Student Data',
    'addStudentFile': 'Add Student File',
    'studentData': 'Student Data',
    'students': 'Students',
    'DriveData': 'Driver Data',
    'SupervisorData': 'Supervisor Data',
    'showStudentData': 'Student Data',
    'addDriver': 'Add Driver',
    'editDriver': 'Edit Driver Data',
    'addSupervisor': 'Add Supervisor',
    'editSupervisor': 'Edit Supervisor Data',
    'itinerary': 'ITinerary',
    'busNumber': 'Bus Number',
    'supervisor': 'Supervisor',
    'driver': 'Driver',
    'allBus': 'All Bus',
    'searchBusHint': 'Search Bus',
    'addStudentBusFile': 'Add Student to Bus From File',
    'show': 'Show',
    'edit': 'Edit',
    'delete': 'Delete',
    'addStudentManually': 'Add Student Manually',
    'addStudentFromFile': 'Add Student From File',
    'showStudent': 'Show Student',
    'addStudentToBusFromFile': 'Add Student To Bus From File',
    'addStudentToBusManually': 'Add Student To Bus Manually',
    'search': 'Search',
    'setting': 'Setting',
    'languages': 'Languages',
    'help': 'Help',
    'profile': 'Profile',
    'english': 'English',
    'arabic': 'العربية',
    'updateProfile': 'Update Profile',
    'changePassword': 'Change Password',
    'parent': 'Parent',
    'searchParentHint': 'Search Parent',
    'showParent': 'Show Parent',
    'parentData': 'Parent Data',
    'oldPassword': 'Old Password',
    'save': 'Save',

    // New translations
    'Account Information': 'Account Information',
    'Account Actions': 'Account Actions',
    'Not Available': 'Not Available',
    'Logout': 'Logout',
    'Profile updated successfully': 'Profile updated successfully',
    'Error': 'Error',
    'Please enter your name': 'Please enter your name',
    'Please enter a valid phone number': 'Please enter a valid phone number',
    'Please enter your current password': 'Please enter your current password',
    'Please enter a new password': 'Please enter a new password',
    'Password must be at least 6 characters':
        'Password must be at least 6 characters',
    'Please confirm your new password': 'Please confirm your new password',
    'Passwords do not match': 'Passwords do not match',
    'Feature coming soon': 'Feature coming soon',

    // Students page
    'search_students': 'Search students',
    'no_students_found': 'No students found',
    'student_info': 'Student Information',
    'parents_info': 'Parents Information',
    'school_info': 'School Information',
    'phone': 'Phone',
    'date_of_birth': 'Date of Birth',
    'gender': 'Gender',
    'religion': 'Religion',
    'blood_type': 'Blood Type',
    'school': 'School',
    'grade': 'Grade',
    'bus': 'Bus',
    'bus_number': 'Bus Number',
    'trip_type': 'Trip Type',
    'photo': 'Photo',
    'id': 'ID',
    'actions': 'Actions',
    'view': 'View',
    'close': 'Close',
  };

  // Arabic translations
  Map<String, String> get ar => {
    'BusatySchool': 'باصاتي - المدرسة',
    'checkPassword': 'كلمة المرور ليست متطابقة',
    'checkEmail': 'من فضلك راجع البريد الإلكتروني للتحقق من الكود',
    'home': 'الرئيسية',
    'username': 'اسم المستخدم',
    'code': 'كود',
    'line': 'خط',
    'birthdate': 'تاريخ الميلاد',
    'classrooms': 'الصفوف',
    'classroom': 'الصف',
    'add_classroom': 'إضافة صف جديد',
    'classroom_name': 'اسم الصف',
    'options': 'خيارات',
    'children': 'الأولاد',
    'new_address': 'العنوان الجديد',
    'add_student_to_bus': 'أضف طلاب إلى الباص',
    'login': 'تسجيل الدخول',
    'email': 'البريد الالكتروني',
    'password': 'كلمة المرور',
    'notHaveAccount': 'ليس لديك حساب؟ ',
    'createAccount': 'إنشاء حساب',
    'bus_name': 'اسم الباص',
    'forgetPassword': 'نسيت كلمة المرور؟ ',
    'remember': 'تذكر',
    'notes': 'ملاحظات',
    'signup': 'تسجيل',
    'name': 'الاسم',
    'schoolName': 'اسم المدرسة',
    'phoneNumber': 'رقم الهاتف',
    'haveAccount': 'لديك حساب؟ ',
    'sendCodeAgain': 'إرسال الكود مرة أخرى',
    'sendCode': 'إرسال الكود',
    'forget': 'نسيت كلمة المرور',
    'sendCodeRegister':
        'سيتم ارسال كود الي البريد الالكتروني المسجل به لإستعادة الحساب',
    'againPassword': 'إعادة كلمة المرور',
    // Error pages
    'pageNotFound': 'الصفحة غير موجودة',
    'pageNotFoundDescription': 'الصفحة التي تبحث عنها غير موجودة أو تم نقلها.',
    'goHome': 'الذهاب للرئيسية',
    'goBack': 'العودة',
    'quickLinks': 'روابط سريعة',
    'changePassword': 'تغيير كلمة المرور',
    'passwordChangeSuccess': 'لقد تم تغير كلمة المرور بنجاح',
    'next': 'متابعة',
    'searchStudentHint': 'البحث عن طالب',
    'searchDriverHint': 'البحث عن سائق',
    'addByHand': 'إضافة طالب يدوي ',
    'addByFile': 'إضافة طلاب من ملف',
    'studentName': 'اسم الطالب',
    'bloodType': 'فصيلة الدم',
    'dateOfBirth': 'تاريخ الميلاد',
    'educationalLevel': 'المرحلة الدراسية',
    'city': 'المدينة',
    'addBus': 'أضف باص',
    'add': 'إضافة',
    'addFile': 'إضافة ملف',
    'address': 'العنوان',
    'nationalId': 'الرقم القومي',
    'allDrivers': 'جميع السائقين',
    'allSupervisors': 'جميع المشرفين',
    'searchSupervisorHint': 'البحث عن مشرف',
    'addStudent': 'إضافة طالب',
    'editStudent': 'تعديل بيانات الطالب',
    'addStudentFile': 'إضافة مجموعة طلاب',
    'studentData': 'بيانات الطالب',
    'DriveData': 'بيانات السائق',
    'SupervisorData': 'بيانات المشرف',
    'showStudentData': 'عرض بيانات الطالب',
    'addDriver': 'إضافة سائق',
    'editDriver': 'تعديل بيانات السائق',
    'addSupervisor': 'إضافة مشرف',
    'editSupervisor': 'تعديل بيانات المشرف',
    'itinerary': 'خط سير',
    'busNumber': 'رقم الباص',
    'supervisor': 'المشرف',
    'driver': 'السائق',
    'allBus': 'جميع الباصات',
    'searchBusHint': 'البحث عن باص',
    'addStudentBusFile': 'إضافة طلاب للباص من ملف',
    'show': 'عرض',
    'delete': 'حذف',
    'edit': 'تعديل',
    'addStudentManually': 'إضافة طالب يدويا',
    'addStudentFromFile': 'إضافة طلاب من ملف',
    'showStudent': 'عرض الطلاب',
    'addStudentToBusFromFile': 'إضافة طلاب للباص من ملف',
    'addStudentToBusManually': 'إضافة طلاب للباص يدوي',
    'search': 'البحث',
    'setting': 'الإعدادات',
    'languages': 'اللغة',
    'help': 'مساعدة',
    'profile': 'الملف الشخصي',
    'english': 'English',
    'arabic': 'العربية',
    'updateProfile': 'تعديل الملف الشخصي',
    'parent': 'ولي الأمر',
    'searchParentHint': 'البحث عن ولي أمر',
    'showParent': 'عرض بيانات ولي الأمر',
    'parentData': 'بيانات ولي الأمر',
    'newPassword': 'كلمة المرور الجديدة',
    'confirmPassword': 'تأكيد كلمة المرور',
    'oldPassword': 'كلمة المرور القديمة',
    'save': 'حفظ',

    // New translations
    'Account Information': 'معلومات الحساب',
    'Account Actions': 'إجراءات الحساب',
    'Not Available': 'غير متوفر',
    'Logout': 'تسجيل الخروج',
    'Profile updated successfully': 'تم تحديث الملف الشخصي بنجاح',
    'Error': 'خطأ',
    'Please enter your name': 'الرجاء إدخال الاسم',
    'Please enter a valid phone number': 'الرجاء إدخال رقم هاتف صحيح',
    'Please enter your current password': 'الرجاء إدخال كلمة المرور الحالية',
    'Please enter a new password': 'الرجاء إدخال كلمة مرور جديدة',
    'Password must be at least 6 characters':
        'يجب أن تكون كلمة المرور 6 أحرف على الأقل',
    'Please confirm your new password': 'الرجاء تأكيد كلمة المرور الجديدة',
    'Passwords do not match': 'كلمات المرور غير متطابقة',
    'Feature coming soon': 'هذه الميزة قادمة قريبًا',

    // Students page
    'search_students': 'البحث عن الطلاب',
    'no_students_found': 'لم يتم العثور على طلاب',
    'student_info': 'معلومات الطالب',
    'parents_info': 'معلومات أولياء الأمور',
    'school_info': 'معلومات المدرسة',
    'phone': 'الهاتف',
    'date_of_birth': 'تاريخ الميلاد',
    'gender': 'الجنس',
    'religion': 'الديانة',
    'blood_type': 'فصيلة الدم',
    'school': 'المدرسة',
    'grade': 'الصف',
    'bus': 'الحافلة',
    'bus_number': 'رقم الحافلة',
    'trip_type': 'نوع الرحلة',
    'photo': 'الصورة',
    'id': 'الرقم',
    'actions': 'الإجراءات',
    'view': 'عرض',
    'close': 'إغلاق',
  };
}

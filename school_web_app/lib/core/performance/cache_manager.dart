import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';

/// Cache manager for handling application caching
/// Following Single Responsibility Principle by focusing only on cache management
class CacheManager {
  static CacheManager? _instance;
  static SharedPreferences? _prefs;
  
  // Cache configuration
  static const String _cachePrefix = 'cache_';
  static const String _timestampSuffix = '_timestamp';
  static const Duration _defaultExpiration = Duration(hours: 24);
  static const int _maxCacheSize = 100; // Maximum number of cache entries

  /// Private constructor
  CacheManager._();

  /// Get singleton instance
  static CacheManager get instance {
    _instance ??= CacheManager._();
    return _instance!;
  }

  /// Initialize cache manager
  static Future<void> init() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      if (kDebugMode) {
        AppLogger.info('Cache manager initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('Error initializing cache manager: $e');
      }
      rethrow;
    }
  }

  /// Check if cache is initialized
  static bool get isInitialized => _prefs != null;

  /// Store data in cache with expiration
  Future<bool> store(
    String key, 
    dynamic data, {
    Duration? expiration,
  }) async {
    try {
      if (_prefs == null) {
        if (kDebugMode) {
          AppLogger.warning('Cache manager not initialized');
        }
        return false;
      }

      final cacheKey = _cachePrefix + key;
      final timestampKey = cacheKey + _timestampSuffix;
      final expirationTime = DateTime.now().add(expiration ?? _defaultExpiration);

      // Convert data to JSON string
      final jsonData = jsonEncode(data);

      // Store data and timestamp
      final dataStored = await _prefs!.setString(cacheKey, jsonData);
      final timestampStored = await _prefs!.setInt(
        timestampKey, 
        expirationTime.millisecondsSinceEpoch,
      );

      if (dataStored && timestampStored) {
        if (kDebugMode) {
          AppLogger.info('Data cached with key: $key');
        }
        
        // Check cache size and clean if necessary
        await _cleanupIfNeeded();
        return true;
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('Error storing cache for key $key: $e');
      }
      return false;
    }
  }

  /// Retrieve data from cache
  Future<T?> retrieve<T>(String key) async {
    try {
      if (_prefs == null) {
        if (kDebugMode) {
          AppLogger.warning('Cache manager not initialized');
        }
        return null;
      }

      final cacheKey = _cachePrefix + key;
      final timestampKey = cacheKey + _timestampSuffix;

      // Check if data exists
      if (!_prefs!.containsKey(cacheKey) || !_prefs!.containsKey(timestampKey)) {
        return null;
      }

      // Check if data is expired
      final expirationTime = _prefs!.getInt(timestampKey);
      if (expirationTime == null || 
          DateTime.now().millisecondsSinceEpoch > expirationTime) {
        // Remove expired data
        await remove(key);
        if (kDebugMode) {
          AppLogger.info('Expired cache removed for key: $key');
        }
        return null;
      }

      // Retrieve and decode data
      final jsonData = _prefs!.getString(cacheKey);
      if (jsonData == null) return null;

      final data = jsonDecode(jsonData);
      if (kDebugMode) {
        AppLogger.info('Cache hit for key: $key');
      }

      return data as T?;
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('Error retrieving cache for key $key: $e');
      }
      return null;
    }
  }

  /// Remove specific cache entry
  Future<bool> remove(String key) async {
    try {
      if (_prefs == null) return false;

      final cacheKey = _cachePrefix + key;
      final timestampKey = cacheKey + _timestampSuffix;

      final dataRemoved = await _prefs!.remove(cacheKey);
      final timestampRemoved = await _prefs!.remove(timestampKey);

      if (dataRemoved || timestampRemoved) {
        if (kDebugMode) {
          AppLogger.info('Cache removed for key: $key');
        }
      }

      return dataRemoved || timestampRemoved;
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('Error removing cache for key $key: $e');
      }
      return false;
    }
  }

  /// Clear all expired cache entries
  Future<void> clearExpired() async {
    try {
      if (_prefs == null) return;

      final keys = _prefs!.getKeys();
      final now = DateTime.now().millisecondsSinceEpoch;
      int removedCount = 0;

      for (final key in keys) {
        if (key.startsWith(_cachePrefix) && key.endsWith(_timestampSuffix)) {
          final expirationTime = _prefs!.getInt(key);
          if (expirationTime != null && now > expirationTime) {
            // Remove both data and timestamp
            final dataKey = key.replaceAll(_timestampSuffix, '');
            await _prefs!.remove(dataKey);
            await _prefs!.remove(key);
            removedCount++;
          }
        }
      }

      if (kDebugMode && removedCount > 0) {
        AppLogger.info('Cleared $removedCount expired cache entries');
      }
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('Error clearing expired cache: $e');
      }
    }
  }

  /// Clear all cache entries
  Future<void> clearAll() async {
    try {
      if (_prefs == null) return;

      final keys = _prefs!.getKeys();
      int removedCount = 0;

      for (final key in keys) {
        if (key.startsWith(_cachePrefix)) {
          await _prefs!.remove(key);
          removedCount++;
        }
      }

      if (kDebugMode) {
        AppLogger.info('Cleared all cache entries: $removedCount');
      }
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('Error clearing all cache: $e');
      }
    }
  }

  /// Get cache size (number of entries)
  int getCacheSize() {
    try {
      if (_prefs == null) return 0;

      final keys = _prefs!.getKeys();
      return keys.where((key) => 
        key.startsWith(_cachePrefix) && !key.endsWith(_timestampSuffix)
      ).length;
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('Error getting cache size: $e');
      }
      return 0;
    }
  }

  /// Check if cache entry exists and is valid
  Future<bool> exists(String key) async {
    try {
      if (_prefs == null) return false;

      final cacheKey = _cachePrefix + key;
      final timestampKey = cacheKey + _timestampSuffix;

      if (!_prefs!.containsKey(cacheKey) || !_prefs!.containsKey(timestampKey)) {
        return false;
      }

      // Check if expired
      final expirationTime = _prefs!.getInt(timestampKey);
      if (expirationTime == null || 
          DateTime.now().millisecondsSinceEpoch > expirationTime) {
        return false;
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('Error checking cache existence for key $key: $e');
      }
      return false;
    }
  }

  /// Cleanup cache if it exceeds maximum size
  Future<void> _cleanupIfNeeded() async {
    try {
      final currentSize = getCacheSize();
      if (currentSize <= _maxCacheSize) return;

      // Get all cache entries with timestamps
      final keys = _prefs!.getKeys();
      final cacheEntries = <String, int>{};

      for (final key in keys) {
        if (key.startsWith(_cachePrefix) && key.endsWith(_timestampSuffix)) {
          final timestamp = _prefs!.getInt(key);
          if (timestamp != null) {
            final dataKey = key.replaceAll(_timestampSuffix, '');
            cacheEntries[dataKey] = timestamp;
          }
        }
      }

      // Sort by timestamp (oldest first)
      final sortedEntries = cacheEntries.entries.toList()
        ..sort((a, b) => a.value.compareTo(b.value));

      // Remove oldest entries
      final entriesToRemove = currentSize - _maxCacheSize + 10; // Remove extra for buffer
      for (int i = 0; i < entriesToRemove && i < sortedEntries.length; i++) {
        final dataKey = sortedEntries[i].key;
        final timestampKey = dataKey + _timestampSuffix;
        await _prefs!.remove(dataKey);
        await _prefs!.remove(timestampKey);
      }

      if (kDebugMode) {
        AppLogger.info('Cache cleanup completed. Removed $entriesToRemove entries');
      }
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('Error during cache cleanup: $e');
      }
    }
  }

  /// Get cache statistics
  Map<String, dynamic> getStats() {
    try {
      final size = getCacheSize();
      final keys = _prefs?.getKeys() ?? <String>{};
      final cacheKeys = keys.where((key) => key.startsWith(_cachePrefix)).toList();
      
      return {
        'size': size,
        'maxSize': _maxCacheSize,
        'totalKeys': cacheKeys.length,
        'isInitialized': isInitialized,
      };
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('Error getting cache stats: $e');
      }
      return {
        'size': 0,
        'maxSize': _maxCacheSize,
        'totalKeys': 0,
        'isInitialized': isInitialized,
      };
    }
  }
}

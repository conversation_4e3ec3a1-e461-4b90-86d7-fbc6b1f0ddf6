import 'package:flutter/material.dart';
import '../utils/logger.dart';

/// Lazy loader widget for performance optimization
/// Following Single Responsibility Principle by focusing only on lazy loading
class LazyLoader extends StatefulWidget {
  final Widget Function() builder;
  final Widget? placeholder;
  final Duration delay;
  final bool shouldLoad;

  const LazyLoader({
    super.key,
    required this.builder,
    this.placeholder,
    this.delay = const Duration(milliseconds: 100),
    this.shouldLoad = true,
  });

  @override
  State<LazyLoader> createState() => _LazyLoaderState();
}

class _LazyLoaderState extends State<LazyLoader> {
  bool _isLoaded = false;
  Widget? _cachedWidget;

  @override
  void initState() {
    super.initState();
    if (widget.shouldLoad) {
      _loadWidget();
    }
  }

  @override
  void didUpdateWidget(LazyLoader oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.shouldLoad && !_isLoaded) {
      _loadWidget();
    }
  }

  void _loadWidget() {
    if (_isLoaded) return;

    Future.delayed(widget.delay, () {
      if (mounted) {
        try {
          _cachedWidget = widget.builder();
          setState(() {
            _isLoaded = true;
          });
        } catch (e) {
          AppLogger.error('Error in LazyLoader builder: $e');
          setState(() {
            _isLoaded = true;
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.shouldLoad) {
      return widget.placeholder ?? const SizedBox.shrink();
    }

    if (_isLoaded && _cachedWidget != null) {
      return _cachedWidget!;
    }

    return widget.placeholder ?? 
      const Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      );
  }
}

/// Lazy list builder for performance optimization
class LazyListBuilder extends StatefulWidget {
  final int itemCount;
  final Widget Function(BuildContext context, int index) itemBuilder;
  final Widget? placeholder;
  final int initialLoadCount;
  final int loadMoreThreshold;
  final ScrollController? scrollController;
  final Axis scrollDirection;
  final EdgeInsetsGeometry? padding;

  const LazyListBuilder({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.placeholder,
    this.initialLoadCount = 10,
    this.loadMoreThreshold = 5,
    this.scrollController,
    this.scrollDirection = Axis.vertical,
    this.padding,
  });

  @override
  State<LazyListBuilder> createState() => _LazyListBuilderState();
}

class _LazyListBuilderState extends State<LazyListBuilder> {
  late ScrollController _scrollController;
  int _loadedItemCount = 0;
  final Map<int, Widget> _cachedWidgets = {};

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    _loadedItemCount = widget.initialLoadCount.clamp(0, widget.itemCount);
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_onScroll);
    }
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreItems();
    }
  }

  void _loadMoreItems() {
    if (_loadedItemCount < widget.itemCount) {
      setState(() {
        _loadedItemCount = (_loadedItemCount + widget.loadMoreThreshold)
            .clamp(0, widget.itemCount);
      });
    }
  }

  Widget _buildItem(BuildContext context, int index) {
    if (_cachedWidgets.containsKey(index)) {
      return _cachedWidgets[index]!;
    }

    try {
      final widget = this.widget.itemBuilder(context, index);
      _cachedWidgets[index] = widget;
      return widget;
    } catch (e) {
      AppLogger.error('Error building item at index $index: $e');
      return const SizedBox.shrink();
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: _scrollController,
      scrollDirection: widget.scrollDirection,
      padding: widget.padding,
      itemCount: _loadedItemCount,
      itemBuilder: _buildItem,
    );
  }
}

/// Lazy grid builder for performance optimization
class LazyGridBuilder extends StatefulWidget {
  final int itemCount;
  final Widget Function(BuildContext context, int index) itemBuilder;
  final SliverGridDelegate gridDelegate;
  final Widget? placeholder;
  final int initialLoadCount;
  final int loadMoreThreshold;
  final ScrollController? scrollController;
  final Axis scrollDirection;
  final EdgeInsetsGeometry? padding;

  const LazyGridBuilder({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    required this.gridDelegate,
    this.placeholder,
    this.initialLoadCount = 20,
    this.loadMoreThreshold = 10,
    this.scrollController,
    this.scrollDirection = Axis.vertical,
    this.padding,
  });

  @override
  State<LazyGridBuilder> createState() => _LazyGridBuilderState();
}

class _LazyGridBuilderState extends State<LazyGridBuilder> {
  late ScrollController _scrollController;
  int _loadedItemCount = 0;
  final Map<int, Widget> _cachedWidgets = {};

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    _loadedItemCount = widget.initialLoadCount.clamp(0, widget.itemCount);
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_onScroll);
    }
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreItems();
    }
  }

  void _loadMoreItems() {
    if (_loadedItemCount < widget.itemCount) {
      setState(() {
        _loadedItemCount = (_loadedItemCount + widget.loadMoreThreshold)
            .clamp(0, widget.itemCount);
      });
    }
  }

  Widget _buildItem(BuildContext context, int index) {
    if (_cachedWidgets.containsKey(index)) {
      return _cachedWidgets[index]!;
    }

    try {
      final widget = this.widget.itemBuilder(context, index);
      _cachedWidgets[index] = widget;
      return widget;
    } catch (e) {
      AppLogger.error('Error building grid item at index $index: $e');
      return const SizedBox.shrink();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      controller: _scrollController,
      scrollDirection: widget.scrollDirection,
      padding: widget.padding,
      gridDelegate: widget.gridDelegate,
      itemCount: _loadedItemCount,
      itemBuilder: _buildItem,
    );
  }
}

/// Lazy image loader for performance optimization
class LazyImage extends StatefulWidget {
  final String imageUrl;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BoxFit? fit;
  final double? width;
  final double? height;
  final Duration fadeInDuration;

  const LazyImage({
    super.key,
    required this.imageUrl,
    this.placeholder,
    this.errorWidget,
    this.fit,
    this.width,
    this.height,
    this.fadeInDuration = const Duration(milliseconds: 300),
  });

  @override
  State<LazyImage> createState() => _LazyImageState();
}

class _LazyImageState extends State<LazyImage> {
  bool _isLoaded = false;
  bool _hasError = false;

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      return widget.errorWidget ?? 
        const Icon(Icons.error, color: Colors.grey);
    }

    return Image.network(
      widget.imageUrl,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) {
          _isLoaded = true;
          return AnimatedOpacity(
            opacity: _isLoaded ? 1.0 : 0.0,
            duration: widget.fadeInDuration,
            child: child,
          );
        }
        return widget.placeholder ?? 
          const Center(child: CircularProgressIndicator());
      },
      errorBuilder: (context, error, stackTrace) {
        _hasError = true;
        AppLogger.error('Error loading image: ${widget.imageUrl}', error: error);
        return widget.errorWidget ?? 
          const Icon(Icons.error, color: Colors.grey);
      },
    );
  }
}

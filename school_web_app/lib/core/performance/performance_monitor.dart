import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import '../utils/logger.dart';

/// Performance monitoring utility class
/// Following Single Responsibility Principle by focusing only on performance tracking
class PerformanceMonitor {
  static final Map<String, DateTime> _timers = {};
  static final Map<String, Duration> _completedTimers = {};
  static Timer? _frameRateTimer;
  static int _frameCount = 0;
  static DateTime? _lastFrameTime;
  static double _currentFps = 0.0;
  static bool _isMonitoring = false;

  /// Start a performance timer
  static void startTimer(String name) {
    try {
      _timers[name] = DateTime.now();
      if (kDebugMode) {
        AppLogger.info('Performance timer started: $name');
      }
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('Error starting timer $name: $e');
      }
    }
  }

  /// End a performance timer and log the duration
  static Duration? endTimer(String name) {
    try {
      final startTime = _timers[name];
      if (startTime == null) {
        if (kDebugMode) {
          AppLogger.warning('Timer $name was not started');
        }
        return null;
      }

      final duration = DateTime.now().difference(startTime);
      _completedTimers[name] = duration;
      _timers.remove(name);

      if (kDebugMode) {
        AppLogger.info(
          'Performance timer completed: $name - ${duration.inMilliseconds}ms',
        );
      }

      return duration;
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('Error ending timer $name: $e');
      }
      return null;
    }
  }

  /// Get the duration of a completed timer
  static Duration? getTimerDuration(String name) {
    return _completedTimers[name];
  }

  /// Start monitoring frame rate
  static void startFrameRateMonitoring() {
    if (_isMonitoring) return;

    try {
      _isMonitoring = true;
      _frameCount = 0;
      _lastFrameTime = DateTime.now();

      // Monitor frame rate using SchedulerBinding
      SchedulerBinding.instance.addPersistentFrameCallback(_onFrame);

      // Log FPS every 5 seconds
      _frameRateTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
        if (kDebugMode && _currentFps > 0) {
          AppLogger.info('Current FPS: ${_currentFps.toStringAsFixed(1)}');
        }
      });

      if (kDebugMode) {
        AppLogger.info('Frame rate monitoring started');
      }
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('Error starting frame rate monitoring: $e');
      }
    }
  }

  /// Stop monitoring frame rate
  static void stopFrameRateMonitoring() {
    try {
      _isMonitoring = false;
      _frameRateTimer?.cancel();
      _frameRateTimer = null;
      // Note: There's no removeFrameCallback method, so we just stop monitoring

      if (kDebugMode) {
        AppLogger.info('Frame rate monitoring stopped');
      }
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('Error stopping frame rate monitoring: $e');
      }
    }
  }

  /// Frame callback for FPS calculation
  static void _onFrame(Duration timestamp) {
    try {
      final now = DateTime.now();
      if (_lastFrameTime != null) {
        final timeDiff = now.difference(_lastFrameTime!);
        if (timeDiff.inMilliseconds > 0) {
          _currentFps = 1000.0 / timeDiff.inMilliseconds;
        }
      }
      _lastFrameTime = now;
      _frameCount++;
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('Error in frame callback: $e');
      }
    }
  }

  /// Get current FPS
  static double getCurrentFps() {
    return _currentFps;
  }

  /// Get frame count
  static int getFrameCount() {
    return _frameCount;
  }

  /// Check if monitoring is active
  static bool get isMonitoring => _isMonitoring;

  /// Get all completed timers
  static Map<String, Duration> getCompletedTimers() {
    return Map.unmodifiable(_completedTimers);
  }

  /// Get all active timers
  static Map<String, DateTime> getActiveTimers() {
    return Map.unmodifiable(_timers);
  }

  /// Clear all timers and reset monitoring
  static void reset() {
    try {
      _timers.clear();
      _completedTimers.clear();
      stopFrameRateMonitoring();
      _frameCount = 0;
      _currentFps = 0.0;
      _lastFrameTime = null;

      if (kDebugMode) {
        AppLogger.info('Performance monitor reset');
      }
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('Error resetting performance monitor: $e');
      }
    }
  }

  /// Log performance summary
  static void logSummary() {
    if (!kDebugMode) return;

    try {
      AppLogger.info('=== Performance Summary ===');
      AppLogger.info('Current FPS: ${_currentFps.toStringAsFixed(1)}');
      AppLogger.info('Total Frames: $_frameCount');
      AppLogger.info('Active Timers: ${_timers.length}');
      AppLogger.info('Completed Timers: ${_completedTimers.length}');

      for (final entry in _completedTimers.entries) {
        AppLogger.info('${entry.key}: ${entry.value.inMilliseconds}ms');
      }

      AppLogger.info('=== End Summary ===');
    } catch (e) {
      AppLogger.error('Error logging performance summary: $e');
    }
  }

  /// Measure execution time of a function
  static Future<T> measureAsync<T>(
    String name,
    Future<T> Function() function,
  ) async {
    startTimer(name);
    try {
      final result = await function();
      endTimer(name);
      return result;
    } catch (e) {
      endTimer(name);
      rethrow;
    }
  }

  /// Measure execution time of a synchronous function
  static T measureSync<T>(String name, T Function() function) {
    startTimer(name);
    try {
      final result = function();
      endTimer(name);
      return result;
    } catch (e) {
      endTimer(name);
      rethrow;
    }
  }

  /// Monitor widget build performance
  static T monitorWidgetBuild<T>(
    String widgetName,
    T Function() buildFunction,
  ) {
    if (kDebugMode) {
      startTimer('Widget Build: $widgetName');
    }

    try {
      final result = buildFunction();
      if (kDebugMode) {
        // End timer after a short delay to capture build time
        Future.delayed(Duration.zero, () {
          endTimer('Widget Build: $widgetName');
        });
      }
      return result;
    } catch (e) {
      if (kDebugMode) {
        endTimer('Widget Build: $widgetName');
      }
      rethrow;
    }
  }
}

import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import '../../domain/entities/notification.dart';
import '../../data/models/notification_model.dart';

/// Service for handling notifications and FCM
class NotificationService extends GetxService {
  // Observable variables
  final _notifications = <NotificationEntity>[].obs;
  final _unreadCount = 0.obs;
  final _isInitialized = false.obs;

  // Getters
  List<NotificationEntity> get notifications => _notifications;
  int get unreadCount => _unreadCount.value;
  bool get isInitialized => _isInitialized.value;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeNotifications();
  }

  /// Initialize notification service
  Future<void> _initializeNotifications() async {
    try {
      if (kIsWeb) {
        // Web-specific initialization
        await _initializeWebNotifications();
      } else {
        // Mobile-specific initialization would go here
        // This would typically involve Firebase Cloud Messaging setup
      }

      _isInitialized.value = true;
    } catch (e) {
      if (kDebugMode) {
        print('Failed to initialize notifications: $e');
      }
    }
  }

  /// Initialize web notifications
  Future<void> _initializeWebNotifications() async {
    // For web, we'll use browser notifications API
    // This is a simplified implementation
    if (kIsWeb) {
      try {
        // Request permission for web notifications
        // Note: This would need proper web notification implementation
        if (kDebugMode) {
          print('Web notifications initialized');
        }
      } catch (e) {
        if (kDebugMode) {
          print('Failed to initialize web notifications: $e');
        }
      }
    }
  }

  /// Add notification to local list
  void addNotification(NotificationEntity notification) {
    _notifications.insert(0, notification);

    if (notification.isRead != true) {
      _unreadCount.value++;
    }

    // Show local notification if app is in foreground
    _showLocalNotification(notification);
  }

  /// Mark notification as read
  void markAsRead(int notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      final notification = _notifications[index];
      if (notification.isRead != true) {
        _notifications[index] = notification.copyWith(isRead: true);
        _unreadCount.value =
            (_unreadCount.value - 1).clamp(0, double.infinity).toInt();
      }
    }
  }

  /// Mark all notifications as read
  void markAllAsRead() {
    for (int i = 0; i < _notifications.length; i++) {
      if (_notifications[i].isRead != true) {
        _notifications[i] = _notifications[i].copyWith(isRead: true);
      }
    }
    _unreadCount.value = 0;
  }

  /// Remove notification
  void removeNotification(int notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      final notification = _notifications[index];
      if (notification.isRead != true) {
        _unreadCount.value =
            (_unreadCount.value - 1).clamp(0, double.infinity).toInt();
      }
      _notifications.removeAt(index);
    }
  }

  /// Clear all notifications
  void clearAllNotifications() {
    _notifications.clear();
    _unreadCount.value = 0;
  }

  /// Update notifications list
  void updateNotifications(List<NotificationEntity> notifications) {
    _notifications.assignAll(notifications);
    _updateUnreadCount();
  }

  /// Update unread count
  void _updateUnreadCount() {
    final count = _notifications.where((n) => n.isRead != true).length;
    _unreadCount.value = count;
  }

  /// Show local notification
  void _showLocalNotification(NotificationEntity notification) {
    // This would show a local notification
    // For web, this could be a browser notification
    // For mobile, this could be a local push notification

    if (kIsWeb) {
      _showWebNotification(notification);
    } else {
      _showMobileNotification(notification);
    }
  }

  /// Show web notification
  void _showWebNotification(NotificationEntity notification) {
    // Web notification implementation
    // This is a placeholder - actual implementation would use browser APIs
    if (kDebugMode) {
      print('Web notification: ${notification.title} - ${notification.body}');
    }
  }

  /// Show mobile notification
  void _showMobileNotification(NotificationEntity notification) {
    // Mobile notification implementation
    // This would typically use flutter_local_notifications
    if (kDebugMode) {
      print(
        'Mobile notification: ${notification.title} - ${notification.body}',
      );
    }
  }

  /// Handle FCM message
  void handleFCMMessage(Map<String, dynamic> message) {
    try {
      final notification = NotificationModel.fromFCMMessage(message);
      addNotification(notification);
    } catch (e) {
      if (kDebugMode) {
        print('Failed to handle FCM message: $e');
      }
    }
  }

  /// Get notification type icon
  String getNotificationTypeIcon(String? type) {
    if (type == null) return 'notifications';

    final notificationType = NotificationTypeExtension.fromString(type);
    return notificationType.icon;
  }

  /// Get notification type display name
  String getNotificationTypeDisplayName(String? type) {
    if (type == null) return 'إشعار عام';

    final notificationType = NotificationTypeExtension.fromString(type);
    return notificationType.displayName;
  }

  /// Format notification date
  String formatNotificationDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return '';

    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inMinutes < 1) {
        return 'الآن';
      } else if (difference.inMinutes < 60) {
        return 'منذ ${difference.inMinutes} دقيقة';
      } else if (difference.inHours < 24) {
        return 'منذ ${difference.inHours} ساعة';
      } else if (difference.inDays < 7) {
        return 'منذ ${difference.inDays} يوم';
      } else {
        return '${date.day}/${date.month}/${date.year}';
      }
    } catch (e) {
      return dateString;
    }
  }

  /// Check if notification is recent (within last hour)
  bool isRecentNotification(String? dateString) {
    if (dateString == null || dateString.isEmpty) return false;

    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();
      final difference = now.difference(date);

      return difference.inHours < 1;
    } catch (e) {
      return false;
    }
  }

  /// Get notifications by type
  List<NotificationEntity> getNotificationsByType(String type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  /// Get unread notifications
  List<NotificationEntity> getUnreadNotifications() {
    return _notifications.where((n) => n.isRead != true).toList();
  }

  /// Get read notifications
  List<NotificationEntity> getReadNotifications() {
    return _notifications.where((n) => n.isRead == true).toList();
  }
}

import 'package:get/get.dart';

/// Service for handling advanced search functionality
class SearchService extends GetxService {
  // Observable variables for search state
  final _isSearching = false.obs;
  final _searchQuery = ''.obs;
  final _searchResults = <SearchResult>[].obs;
  final _searchFilters = <String, dynamic>{}.obs;
  final _searchHistory = <String>[].obs;

  // Getters
  bool get isSearching => _isSearching.value;
  String get searchQuery => _searchQuery.value;
  List<SearchResult> get searchResults => _searchResults;
  Map<String, dynamic> get searchFilters => _searchFilters;
  List<String> get searchHistory => _searchHistory;

  @override
  void onInit() {
    super.onInit();
    _loadSearchHistory();
  }

  /// Perform global search across all entities
  Future<void> performGlobalSearch(
    String query, {
    List<SearchCategory>? categories,
    Map<String, dynamic>? filters,
  }) async {
    if (query.trim().isEmpty) {
      _searchResults.clear();
      return;
    }

    _isSearching.value = true;
    _searchQuery.value = query;

    try {
      final results = <SearchResult>[];

      // Add to search history
      _addToSearchHistory(query);

      // Apply filters if provided
      if (filters != null) {
        _searchFilters.assignAll(filters);
      }

      // Determine which categories to search
      final searchCategories = categories ?? SearchCategory.values;

      // Search in each category
      for (final category in searchCategories) {
        final categoryResults = await _searchInCategory(
          category,
          query,
          filters,
        );
        results.addAll(categoryResults);
      }

      // Sort results by relevance
      results.sort((a, b) => b.relevance.compareTo(a.relevance));

      _searchResults.assignAll(results);
    } catch (e) {
      // Handle error
      _searchResults.clear();
    } finally {
      _isSearching.value = false;
    }
  }

  /// Search within a specific category
  Future<List<SearchResult>> _searchInCategory(
    SearchCategory category,
    String query,
    Map<String, dynamic>? filters,
  ) async {
    final results = <SearchResult>[];

    switch (category) {
      case SearchCategory.students:
        // This would typically call the student repository
        // For now, we'll return mock results
        break;
      case SearchCategory.parents:
        // This would typically call the parent repository
        break;
      case SearchCategory.drivers:
        // This would typically call the driver repository
        break;
      case SearchCategory.supervisors:
        // This would typically call the supervisor repository
        break;
      case SearchCategory.buses:
        // This would typically call the bus repository
        break;
      case SearchCategory.trips:
        // This would typically call the trip repository
        break;
    }

    return results;
  }

  /// Add search query to history
  void _addToSearchHistory(String query) {
    if (query.trim().isEmpty) return;

    // Remove if already exists
    _searchHistory.remove(query);

    // Add to beginning
    _searchHistory.insert(0, query);

    // Keep only last 10 searches
    if (_searchHistory.length > 10) {
      _searchHistory.removeRange(10, _searchHistory.length);
    }

    _saveSearchHistory();
  }

  /// Load search history from storage
  void _loadSearchHistory() {
    // This would typically load from SharedPreferences
    // For now, we'll use mock data
    _searchHistory.assignAll([
      'أحمد محمد',
      'حافلة رقم 1',
      'المدرسة الابتدائية',
    ]);
  }

  /// Save search history to storage
  void _saveSearchHistory() {
    // This would typically save to SharedPreferences
  }

  /// Clear search results
  void clearSearch() {
    _searchQuery.value = '';
    _searchResults.clear();
    _searchFilters.clear();
  }

  /// Clear search history
  void clearSearchHistory() {
    _searchHistory.clear();
    _saveSearchHistory();
  }

  /// Apply search filters
  void applyFilters(Map<String, dynamic> filters) {
    _searchFilters.assignAll(filters);
    if (_searchQuery.value.isNotEmpty) {
      performGlobalSearch(_searchQuery.value, filters: filters);
    }
  }

  /// Remove specific filter
  void removeFilter(String key) {
    _searchFilters.remove(key);
    if (_searchQuery.value.isNotEmpty) {
      performGlobalSearch(_searchQuery.value, filters: _searchFilters);
    }
  }

  /// Get search suggestions based on query
  List<String> getSearchSuggestions(String query) {
    if (query.trim().isEmpty) return _searchHistory;

    final suggestions = <String>[];

    // Add matching history items
    for (final historyItem in _searchHistory) {
      if (historyItem.toLowerCase().contains(query.toLowerCase())) {
        suggestions.add(historyItem);
      }
    }

    // Add predefined suggestions
    final predefinedSuggestions = [
      'الطلاب',
      'أولياء الأمور',
      'السائقين',
      'المشرفين',
      'الحافلات',
      'الرحلات',
    ];

    for (final suggestion in predefinedSuggestions) {
      if (suggestion.toLowerCase().contains(query.toLowerCase()) &&
          !suggestions.contains(suggestion)) {
        suggestions.add(suggestion);
      }
    }

    return suggestions.take(5).toList();
  }
}

/// Enum for search categories
enum SearchCategory { students, parents, drivers, supervisors, buses, trips }

/// Extension for search category display names
extension SearchCategoryExtension on SearchCategory {
  String get displayName {
    switch (this) {
      case SearchCategory.students:
        return 'الطلاب';
      case SearchCategory.parents:
        return 'أولياء الأمور';
      case SearchCategory.drivers:
        return 'السائقين';
      case SearchCategory.supervisors:
        return 'المشرفين';
      case SearchCategory.buses:
        return 'الحافلات';
      case SearchCategory.trips:
        return 'الرحلات';
    }
  }

  String get icon {
    switch (this) {
      case SearchCategory.students:
        return 'person';
      case SearchCategory.parents:
        return 'family_restroom';
      case SearchCategory.drivers:
        return 'drive_eta';
      case SearchCategory.supervisors:
        return 'supervisor_account';
      case SearchCategory.buses:
        return 'directions_bus';
      case SearchCategory.trips:
        return 'route';
    }
  }
}

/// Class for search results
class SearchResult {
  final String id;
  final String title;
  final String subtitle;
  final SearchCategory category;
  final double relevance;
  final Map<String, dynamic> data;
  final String? imageUrl;

  const SearchResult({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.category,
    required this.relevance,
    required this.data,
    this.imageUrl,
  });
}

import 'package:flutter/material.dart';
import '../constants/color_constants.dart';
import '../constants/size_constants.dart';

/// AppTheme class responsible for managing the Busaty school bus management system's theme
/// Following Single Responsibility Principle by focusing only on theme management
class AppTheme {
  // Private constructor to prevent instantiation
  AppTheme._();

  /// Light theme configuration for the Busaty school bus management system
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      primaryColor: ColorConstants.primary,
      scaffoldBackgroundColor: ColorConstants.backgroundLightColor,
      colorScheme: ColorScheme(
        brightness: Brightness.light,
        primary: ColorConstants.primary,
        onPrimary: ColorConstants.white,
        secondary: ColorConstants.mainColor,
        onSecondary: ColorConstants.white,
        tertiary: ColorConstants.tertiaryColor,
        onTertiary: ColorConstants.text,
        error: ColorConstants.error,
        onError: ColorConstants.white,
        // Use surface instead of deprecated background
        surface: ColorConstants.fillFormField,
        onSurface: ColorConstants.text,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: ColorConstants.primary,
        foregroundColor: ColorConstants.white,
        elevation: 0,
        shadowColor: Colors.black.withAlpha(51), // 0.2 opacity = 51/255
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorConstants.primary,
          foregroundColor: ColorConstants.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
          ),
        ),
      ),
      cardTheme: CardTheme(
        color: ColorConstants.surfaceLightColor,
        elevation: 2,
        margin: EdgeInsets.symmetric(
          vertical: SizeConstants.spaceXS,
          horizontal: SizeConstants.spaceMD,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        ),
      ),
      textTheme: TextTheme(
        headlineLarge: TextStyle(
          fontSize: SizeConstants.fontMD,
          fontWeight: FontWeight.bold,
          color: ColorConstants.textPrimaryLightColor,
        ),
        headlineMedium: TextStyle(
          fontSize: SizeConstants.fontBase,
          fontWeight: FontWeight.bold,
          color: ColorConstants.textPrimaryLightColor,
        ),
        titleLarge: TextStyle(
          fontSize: SizeConstants.fontSM,
          fontWeight: FontWeight.bold,
          color: ColorConstants.textPrimaryLightColor,
        ),
        titleMedium: TextStyle(
          fontSize: SizeConstants.fontBase,
          fontWeight: FontWeight.w500,
          color: ColorConstants.textPrimaryLightColor,
        ),
        bodyLarge: TextStyle(
          fontSize: SizeConstants.fontSM,
          color: ColorConstants.textPrimaryLightColor,
        ),
        bodyMedium: TextStyle(
          fontSize: SizeConstants.fontXS,
          color: ColorConstants.textSecondaryLightColor,
        ),
      ),
    );
  }

  /// Dark theme configuration for the Busaty school bus management system
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      primaryColor: ColorConstants.primary,
      scaffoldBackgroundColor: ColorConstants.backgroundDark,
      colorScheme: ColorScheme(
        brightness: Brightness.dark,
        primary: ColorConstants.primary,
        onPrimary: ColorConstants.white,
        secondary: ColorConstants.mainColor,
        onSecondary: ColorConstants.white,
        tertiary: ColorConstants.tertiaryColor,
        onTertiary: ColorConstants.textPrimaryDark,
        error: ColorConstants.error,
        onError: ColorConstants.white,
        // Use proper dark mode colors
        surface: ColorConstants.cardDark,
        onSurface: ColorConstants.textPrimaryDark,
        surfaceContainerHighest: ColorConstants.surfaceDark,
        outline: ColorConstants.textSecondaryDark,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: ColorConstants.headerDark,
        foregroundColor: ColorConstants.white,
        elevation: 0,
        shadowColor: Colors.black.withAlpha(102), // 0.4 opacity = 102/255
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorConstants.primary,
          foregroundColor: ColorConstants.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
          ),
        ),
      ),
      cardTheme: CardTheme(
        color: ColorConstants.cardDark,
        elevation: 2,
        margin: EdgeInsets.symmetric(
          vertical: SizeConstants.spaceXS,
          horizontal: SizeConstants.spaceMD,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        ),
      ),
      dividerTheme: DividerThemeData(
        color: ColorConstants.textSecondaryDark.withAlpha(80),
        thickness: 1,
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: ColorConstants.surfaceDark,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
          borderSide: BorderSide(
            color: ColorConstants.textSecondaryDark.withAlpha(100),
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
          borderSide: BorderSide(
            color: ColorConstants.textSecondaryDark.withAlpha(100),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
          borderSide: BorderSide(color: ColorConstants.primary, width: 2),
        ),
        labelStyle: TextStyle(color: ColorConstants.textSecondaryDark),
        hintStyle: TextStyle(color: ColorConstants.textTertiaryDark),
      ),
      textTheme: TextTheme(
        headlineLarge: TextStyle(
          fontSize: SizeConstants.fontMD,
          fontWeight: FontWeight.bold,
          color: ColorConstants.textPrimaryDarkColor,
        ),
        headlineMedium: TextStyle(
          fontSize: SizeConstants.fontBase,
          fontWeight: FontWeight.bold,
          color: ColorConstants.textPrimaryDarkColor,
        ),
        titleLarge: TextStyle(
          fontSize: SizeConstants.fontSM,
          fontWeight: FontWeight.bold,
          color: ColorConstants.textPrimaryDarkColor,
        ),
        titleMedium: TextStyle(
          fontSize: SizeConstants.fontBase,
          fontWeight: FontWeight.w500,
          color: ColorConstants.textPrimaryDarkColor,
        ),
        bodyLarge: TextStyle(
          fontSize: SizeConstants.fontSM,
          color: ColorConstants.textPrimaryDarkColor,
        ),
        bodyMedium: TextStyle(
          fontSize: SizeConstants.fontXS,
          color: ColorConstants.textSecondaryDarkColor,
        ),
      ),
    );
  }
}

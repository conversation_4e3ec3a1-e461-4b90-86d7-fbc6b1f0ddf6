import 'package:get/get.dart';
import '../../presentation/controllers/auth_controller.dart';
import 'logger.dart';

/// AuthTestHelper class for testing authentication
/// This class provides methods to test the authentication flow
class AuthTestHelper {
  /// Test login with predefined credentials
  static Future<void> testLogin() async {
    LoggerService.info('=== Starting Authentication Test ===');
    
    try {
      // Get the auth controller
      final authController = Get.find<AuthController>();
      
      // Test credentials
      const testEmail = '<EMAIL>';
      const testPassword = 'password';
      
      LoggerService.info('Attempting login with test credentials');
      LoggerService.debug('Test credentials', data: {
        'email': testEmail,
        'password': testPassword,
      });
      
      // Attempt login
      final success = await authController.login(testEmail, testPassword);
      
      if (success) {
        LoggerService.success('Test login successful');
        LoggerService.debug('User data', data: {
          'id': authController.user?.id,
          'name': authController.user?.name,
          'email': authController.user?.email,
          'isAuthenticated': authController.isAuthenticated,
        });
      } else {
        LoggerService.error('Test login failed', error: authController.errorMessage);
      }
    } catch (e, stackTrace) {
      LoggerService.error('Unexpected error during test login', 
        error: e, 
        stackTrace: stackTrace
      );
    }
    
    LoggerService.info('=== Authentication Test Completed ===');
  }
  
  /// Test the full authentication flow
  static Future<void> testAuthFlow() async {
    LoggerService.info('=== Starting Full Authentication Flow Test ===');
    
    try {
      // Get the auth controller
      final authController = Get.find<AuthController>();
      
      // Check initial auth status
      LoggerService.info('Checking initial authentication status');
      await authController.checkAuthStatus();
      LoggerService.debug('Initial auth status', data: {
        'isAuthenticated': authController.isAuthenticated,
        'user': authController.user != null ? 'exists' : 'null',
      });
      
      // Test login
      await testLogin();
      
      // Test logout
      if (authController.isAuthenticated) {
        LoggerService.info('Testing logout');
        final logoutSuccess = await authController.logout();
        
        if (logoutSuccess) {
          LoggerService.success('Logout successful');
          LoggerService.debug('Auth status after logout', data: {
            'isAuthenticated': authController.isAuthenticated,
            'user': authController.user != null ? 'exists' : 'null',
          });
        } else {
          LoggerService.error('Logout failed', error: authController.errorMessage);
        }
      }
    } catch (e, stackTrace) {
      LoggerService.error('Unexpected error during auth flow test', 
        error: e, 
        stackTrace: stackTrace
      );
    }
    
    LoggerService.info('=== Full Authentication Flow Test Completed ===');
  }
}

import 'package:flutter/material.dart';
import '../constants/size_constants.dart';

/// Helper class to migrate old sizes to new web-optimized sizes
/// This helps in systematically updating all components to use the new size system
class SizeMigrationHelper {
  // Private constructor to prevent instantiation
  SizeMigrationHelper._();

  // =============================================================================
  // FONT SIZE MIGRATION MAPPING
  // =============================================================================

  /// Map old font sizes to new optimized sizes
  static final Map<double, double> fontSizeMigration = {
    // Old mobile sizes → New web sizes
    48.0: SizeConstants.font3XL, // Hero titles: 48 → 28
    36.0: SizeConstants.font2XL, // Main headings: 36 → 24
    32.0: SizeConstants.fontXL, // Page titles: 32 → 20
    28.0: SizeConstants.fontLG, // Section titles: 28 → 18
    24.0: SizeConstants.fontMD, // Subheadings: 24 → 16
    20.0: SizeConstants.fontBase, // Body text: 20 → 14
    18.0: SizeConstants.fontSM, // Secondary text: 18 → 12
    16.0: SizeConstants.fontSM, // Small text: 16 → 12
    14.0: SizeConstants.fontXS, // Captions: 14 → 10
    12.0: SizeConstants.fontXS, // Labels: 12 → 10
  };

  // =============================================================================
  // SPACING MIGRATION MAPPING
  // =============================================================================

  /// Map old spacing values to new optimized spacing
  static final Map<double, double> spacingMigration = {
    // Old mobile spacing → New web spacing
    48.0: SizeConstants.space3XL, // Large sections: 48 → 40
    40.0: SizeConstants.space2XL, // Section spacing: 40 → 32
    32.0: SizeConstants.spaceXL, // Card spacing: 32 → 24
    24.0: SizeConstants.spaceLG, // Component spacing: 24 → 20
    20.0: SizeConstants.spaceMD, // Element spacing: 20 → 16
    16.0: SizeConstants.spaceBase, // Base spacing: 16 → 12
    12.0: SizeConstants.spaceSM, // Small spacing: 12 → 8
    8.0: SizeConstants.spaceXS, // Tiny spacing: 8 → 4
    4.0: SizeConstants.spaceXS, // Minimal spacing: 4 → 4
  };

  // =============================================================================
  // ICON SIZE MIGRATION MAPPING
  // =============================================================================

  /// Map old icon sizes to new optimized sizes
  static final Map<double, double> iconSizeMigration = {
    // Old mobile icons → New web icons
    64.0: SizeConstants.icon2XL, // Large icons: 64 → 32
    48.0: SizeConstants.iconXL, // Medium-large icons: 48 → 28
    32.0: SizeConstants.iconLG, // Medium icons: 32 → 24
    24.0: SizeConstants.iconMD, // Standard icons: 24 → 20
    20.0: SizeConstants.iconSM, // Small icons: 20 → 16
    16.0: SizeConstants.iconXS, // Tiny icons: 16 → 12
  };

  // =============================================================================
  // BUTTON SIZE MIGRATION MAPPING
  // =============================================================================

  /// Map old button heights to new optimized heights
  static final Map<double, double> buttonHeightMigration = {
    // Old mobile buttons → New web buttons
    56.0: SizeConstants.buttonHeightXL, // Large buttons: 56 → 44
    48.0: SizeConstants.buttonHeightLG, // Medium buttons: 48 → 40
    40.0: SizeConstants.buttonHeightMD, // Standard buttons: 40 → 36
    32.0: SizeConstants.buttonHeightSM, // Small buttons: 32 → 32
  };

  // =============================================================================
  // BORDER RADIUS MIGRATION MAPPING
  // =============================================================================

  /// Map old border radius to new optimized radius
  static final Map<double, double> borderRadiusMigration = {
    // Old mobile radius → New web radius
    24.0: SizeConstants.radius2XL, // Large radius: 24 → 16
    20.0: SizeConstants.radiusXL, // Medium-large radius: 20 → 12
    16.0: SizeConstants.radiusLG, // Medium radius: 16 → 8
    12.0: SizeConstants.radiusMD, // Standard radius: 12 → 6
    8.0: SizeConstants.radiusSM, // Small radius: 8 → 4
    4.0: SizeConstants.radiusXS, // Tiny radius: 4 → 2
  };

  // =============================================================================
  // HELPER METHODS
  // =============================================================================

  /// Migrate font size from old to new system
  static double migrateFontSize(double oldSize) {
    return fontSizeMigration[oldSize] ??
        _calculateProportionalFontSize(oldSize);
  }

  /// Migrate spacing from old to new system
  static double migrateSpacing(double oldSpacing) {
    return spacingMigration[oldSpacing] ??
        _calculateProportionalSpacing(oldSpacing);
  }

  /// Migrate icon size from old to new system
  static double migrateIconSize(double oldSize) {
    return iconSizeMigration[oldSize] ??
        _calculateProportionalIconSize(oldSize);
  }

  /// Migrate button height from old to new system
  static double migrateButtonHeight(double oldHeight) {
    return buttonHeightMigration[oldHeight] ??
        _calculateProportionalButtonHeight(oldHeight);
  }

  /// Migrate border radius from old to new system
  static double migrateBorderRadius(double oldRadius) {
    return borderRadiusMigration[oldRadius] ??
        _calculateProportionalBorderRadius(oldRadius);
  }

  // =============================================================================
  // PROPORTIONAL CALCULATION METHODS
  // =============================================================================

  /// Calculate proportional font size for unmapped values
  static double _calculateProportionalFontSize(double oldSize) {
    // Reduce font sizes by approximately 30% for web
    return (oldSize * 0.7).clamp(SizeConstants.fontXS, SizeConstants.font3XL);
  }

  /// Calculate proportional spacing for unmapped values
  static double _calculateProportionalSpacing(double oldSpacing) {
    // Reduce spacing by approximately 25% for web
    return (oldSpacing * 0.75).clamp(
      SizeConstants.spaceXS,
      SizeConstants.space3XL,
    );
  }

  /// Calculate proportional icon size for unmapped values
  static double _calculateProportionalIconSize(double oldSize) {
    // Reduce icon sizes by approximately 35% for web
    return (oldSize * 0.65).clamp(SizeConstants.iconXS, SizeConstants.icon2XL);
  }

  /// Calculate proportional button height for unmapped values
  static double _calculateProportionalButtonHeight(double oldHeight) {
    // Reduce button heights by approximately 20% for web
    return (oldHeight * 0.8).clamp(
      SizeConstants.buttonHeightSM,
      SizeConstants.buttonHeightXL,
    );
  }

  /// Calculate proportional border radius for unmapped values
  static double _calculateProportionalBorderRadius(double oldRadius) {
    // Reduce border radius by approximately 30% for web
    return (oldRadius * 0.7).clamp(
      SizeConstants.radiusXS,
      SizeConstants.radius2XL,
    );
  }

  // =============================================================================
  // BATCH MIGRATION METHODS
  // =============================================================================

  /// Migrate multiple font sizes at once
  static Map<String, double> migrateFontSizes(Map<String, double> oldSizes) {
    return oldSizes.map((key, value) => MapEntry(key, migrateFontSize(value)));
  }

  /// Migrate multiple spacing values at once
  static Map<String, double> migrateSpacingValues(
    Map<String, double> oldSpacing,
  ) {
    return oldSpacing.map((key, value) => MapEntry(key, migrateSpacing(value)));
  }

  /// Migrate multiple icon sizes at once
  static Map<String, double> migrateIconSizes(Map<String, double> oldSizes) {
    return oldSizes.map((key, value) => MapEntry(key, migrateIconSize(value)));
  }

  // =============================================================================
  // TEXT STYLE MIGRATION
  // =============================================================================

  /// Migrate TextStyle to use new font sizes
  static TextStyle migrateTextStyle(TextStyle oldStyle) {
    final oldFontSize = oldStyle.fontSize ?? 14.0;
    final newFontSize = migrateFontSize(oldFontSize);

    return oldStyle.copyWith(
      fontSize: newFontSize,
      height: _calculateOptimalLineHeight(newFontSize),
    );
  }

  /// Calculate optimal line height for given font size
  static double _calculateOptimalLineHeight(double fontSize) {
    if (fontSize <= SizeConstants.fontSM) return 1.3;
    if (fontSize <= SizeConstants.fontBase) return 1.4;
    if (fontSize <= SizeConstants.fontMD) return 1.5;
    if (fontSize <= SizeConstants.fontLG) return 1.4;
    return 1.3; // For larger fonts
  }

  // =============================================================================
  // EDGE INSETS MIGRATION
  // =============================================================================

  /// Migrate EdgeInsets to use new spacing values
  static EdgeInsets migrateEdgeInsets(EdgeInsets oldInsets) {
    return EdgeInsets.only(
      left: migrateSpacing(oldInsets.left),
      top: migrateSpacing(oldInsets.top),
      right: migrateSpacing(oldInsets.right),
      bottom: migrateSpacing(oldInsets.bottom),
    );
  }

  /// Migrate symmetric EdgeInsets
  static EdgeInsets migrateSymmetricInsets(double horizontal, double vertical) {
    return EdgeInsets.symmetric(
      horizontal: migrateSpacing(horizontal),
      vertical: migrateSpacing(vertical),
    );
  }

  /// Migrate all EdgeInsets
  static EdgeInsets migrateAllInsets(double value) {
    return EdgeInsets.all(migrateSpacing(value));
  }

  // =============================================================================
  // VALIDATION METHODS
  // =============================================================================

  /// Validate that a size is within acceptable web ranges
  static bool isValidWebSize(double size, String type) {
    switch (type.toLowerCase()) {
      case 'font':
        return size >= SizeConstants.fontXS && size <= SizeConstants.font3XL;
      case 'spacing':
        return size >= SizeConstants.spaceXS && size <= SizeConstants.space3XL;
      case 'icon':
        return size >= SizeConstants.iconXS && size <= SizeConstants.icon2XL;
      case 'button':
        return size >= SizeConstants.buttonHeightSM &&
            size <= SizeConstants.buttonHeightXL;
      case 'radius':
        return size >= SizeConstants.radiusXS &&
            size <= SizeConstants.radius2XL;
      default:
        return true;
    }
  }

  /// Get recommended size if current size is not optimal for web
  static double getRecommendedSize(double currentSize, String type) {
    if (isValidWebSize(currentSize, type)) {
      return currentSize;
    }

    switch (type.toLowerCase()) {
      case 'font':
        return migrateFontSize(currentSize);
      case 'spacing':
        return migrateSpacing(currentSize);
      case 'icon':
        return migrateIconSize(currentSize);
      case 'button':
        return migrateButtonHeight(currentSize);
      case 'radius':
        return migrateBorderRadius(currentSize);
      default:
        return currentSize;
    }
  }
}

import 'package:get/get.dart';

/// TranslationHelper class for managing translations
/// This class provides methods to translate strings
class TranslationHelper {
  /// Translates a string using GetX translation
  static String translate(String key) {
    // GetX already provides the tr extension method on String
    return key.tr;
  }

  /// Translates a string with parameters using GetX translation
  static String translateWithParams(String key, Map<String, String> params) {
    // GetX already provides the trParams extension method on String
    return key.trParams(params);
  }

  /// Gets the current locale
  static String get currentLocale => Get.locale?.languageCode ?? 'en';

  /// Checks if the current locale is Arabic
  static bool get isArabic => currentLocale == 'ar';

  /// Checks if the current locale is English
  static bool get isEnglish => currentLocale == 'en';

  /// Gets the text direction based on the current locale
  static bool get isRTL => isArabic;
}

import '../../domain/entities/dashboard_stats.dart';
import '../../domain/entities/school_stats.dart';
import '../../domain/entities/trip.dart';

/// DashboardRemoteDataSource interface
/// Following Interface Segregation Principle by creating a specific interface for remote data operations
abstract class DashboardRemoteDataSource {
  /// Get dashboard statistics
  Future<DashboardStats> getDashboardStats();
  
  /// Get school statistics
  Future<List<SchoolStats>> getSchoolStats();
  
  /// Get recent trips
  Future<List<Trip>> getRecentTrips();
  
  /// Get upcoming trips
  Future<List<Trip>> getUpcomingTrips();
}

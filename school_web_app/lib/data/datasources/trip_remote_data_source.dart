import '../../core/constants/app_constants.dart';
import '../../core/network/api_service.dart';
import '../../core/utils/logger.dart';
import '../models/trip_model.dart';
import '../models/previous_trip_model.dart';

/// TripRemoteDataSource interface
/// Following Dependency Inversion Principle
abstract class TripRemoteDataSource {
  /// Get current trip
  Future<TripModel?> getCurrentTrip();

  /// Get trip by id
  Future<TripModel> getTripById(String id);

  /// Get upcoming trips
  Future<List<TripModel>> getUpcomingTrips();

  /// Get recent trips with pagination
  Future<List<TripModel>> getRecentTrips({
    int page = 1,
    int limit = 10,
    String? busId,
    String? date,
    String? search,
  });

  /// Get trip route details
  Future<Map<String, dynamic>> getTripRoute(String tripId);

  /// Get trip attendance (present students)
  Future<List<Map<String, dynamic>>> getTripAttendance(String tripId);

  /// Get trip absent students
  Future<List<Map<String, dynamic>>> getTripAbsentStudents(String tripId);

  /// Get trip route details
  Future<Map<String, dynamic>> getTripRouteDetails(String tripId);

  /// Enhanced trip management methods

  /// Get trips with pagination and filtering
  Future<List<TripModel>> getTrips({
    int page = 1,
    int limit = 10,
    String? search,
    String? status,
    String? busId,
    String? driverId,
    String? supervisorId,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Create new trip
  Future<TripModel> createTrip(Map<String, dynamic> tripData);

  /// Update existing trip
  Future<TripModel> updateTrip(String id, Map<String, dynamic> tripData);

  /// Delete trip
  Future<bool> deleteTrip(String id);

  /// Start trip
  Future<TripModel> startTrip(String id);

  /// End trip
  Future<TripModel> endTrip(String id);

  /// Update trip location
  Future<bool> updateTripLocation(String id, double latitude, double longitude);

  /// Get real-time trip tracking data
  Future<Map<String, dynamic>> getTripTracking(String id);

  /// Get morning trips
  Future<List<TripModel>> getMorningTrips({DateTime? date, String? busId});

  /// Get evening trips
  Future<List<TripModel>> getEveningTrips({DateTime? date, String? busId});
}

/// TripRemoteDataSourceImpl implementation
/// Following Single Responsibility Principle by focusing only on trip data operations
class TripRemoteDataSourceImpl implements TripRemoteDataSource {
  final ApiService apiService;

  TripRemoteDataSourceImpl({required this.apiService});

  @override
  Future<TripModel?> getCurrentTrip() async {
    try {
      LoggerService.info('Calling API for current trip');

      final response = await apiService.get(
        AppConstants.schoolCurrentTrips,
        isAuth: true,
      );

      LoggerService.debug('Current trip API response', data: response.data);

      // Handle different response formats from the original API
      if (response.data['status'] == true && response.data['data'] != null) {
        final tripData = response.data['data'];

        // Check if we have trip data
        if (tripData is List && tripData.isNotEmpty) {
          // Take the first active trip
          final firstTrip = tripData.first;
          return _mapCurrentTripToTripModel(firstTrip);
        } else if (tripData is Map<String, dynamic>) {
          return _mapCurrentTripToTripModel(tripData);
        }
      }

      // No current trip
      return null;
    } catch (e) {
      LoggerService.error('Failed to get current trip', error: e);
      rethrow;
    }
  }

  @override
  Future<TripModel> getTripById(String id) async {
    try {
      LoggerService.info('Calling API for trip by id: $id');

      final response = await apiService.get(
        '${AppConstants.tripEndpoint}/$id',
        isAuth: true,
      );

      LoggerService.debug('Trip by id API response', data: response.data);

      if (response.data['data'] != null) {
        return _mapCurrentTripToTripModel(response.data['data']);
      }

      throw Exception('Trip not found');
    } catch (e) {
      LoggerService.error('Failed to get trip by id', error: e);
      rethrow;
    }
  }

  @override
  Future<List<TripModel>> getUpcomingTrips() async {
    try {
      LoggerService.info('Calling API for upcoming trips');

      final response = await apiService.get(
        AppConstants.tripEndpoint,
        isAuth: true,
      );

      LoggerService.debug('Upcoming trips API response', data: response.data);

      if (response.data['data'] != null) {
        final tripsData = response.data['data'] as List;
        return tripsData
            .map((tripData) => _mapCurrentTripToTripModel(tripData))
            .toList();
      }

      return [];
    } catch (e) {
      LoggerService.error('Failed to get upcoming trips', error: e);
      rethrow;
    }
  }

  @override
  Future<List<TripModel>> getRecentTrips({
    int page = 1,
    int limit = 10,
    String? busId,
    String? date,
    String? search,
  }) async {
    try {
      LoggerService.info(
        'Calling API for recent trips',
        data: {
          'page': page,
          'limit': limit,
          'busId': busId,
          'date': date,
          'search': search,
        },
      );

      // Build query parameters
      Map<String, dynamic> queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (busId != null && busId.isNotEmpty && busId != "0") {
        queryParams['bus_id'] = busId;
      }

      if (date != null && date.isNotEmpty) {
        queryParams['date'] = date;
      }

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }

      final response = await apiService.get(
        AppConstants.schoolPreviousTrips,
        queryParameters: queryParams,
        isAuth: true,
      );

      LoggerService.debug('Recent trips API response received');
      LoggerService.debug(
        'Response structure',
        data: {
          'hasData': response.data != null,
          'hasPreviousTrips': response.data?['previous_trips'] != null,
          'hasDirectData': response.data?['data'] != null,
          'responseKeys': response.data?.keys.toList(),
        },
      );

      // Handle different response formats from the original API
      List<TripModel> trips = [];

      try {
        // Use PreviousTripsResponse to parse the data correctly
        final previousTripsResponse = PreviousTripsResponse.fromJson(
          response.data,
        );

        if (previousTripsResponse.data != null) {
          trips =
              previousTripsResponse.data!
                  .map(
                    (previousTrip) =>
                        _convertPreviousTripToTripModel(previousTrip),
                  )
                  .toList();
        }

        LoggerService.success(
          'Recent trips parsed successfully',
          data: {'count': trips.length},
        );
      } catch (e) {
        LoggerService.error(
          'Error parsing recent trips response: ${e.toString()}',
        );
        // Return empty list instead of crashing
        trips = [];
      }

      return trips;
    } catch (e) {
      LoggerService.error('Failed to get recent trips', error: e);
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getTripRoute(String tripId) async {
    try {
      LoggerService.info('Calling API for trip route: $tripId');

      final response = await apiService.get(
        '${AppConstants.tripRouteDetails}$tripId',
        isAuth: true,
      );

      LoggerService.debug('Trip route API response', data: response.data);

      return response.data;
    } catch (e) {
      LoggerService.error('Failed to get trip route', error: e);
      rethrow;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getTripAttendance(String tripId) async {
    try {
      LoggerService.info('Calling API for trip attendance: $tripId');

      final response = await apiService.get(
        '${AppConstants.tripAttendantStudents}$tripId',
        isAuth: true,
      );

      LoggerService.debug('Trip attendance API response', data: response.data);

      if (response.data['data'] != null) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      }

      return [];
    } catch (e) {
      LoggerService.error('Failed to get trip attendance', error: e);
      rethrow;
    }
  }

  /// Get trip absent students
  @override
  Future<List<Map<String, dynamic>>> getTripAbsentStudents(
    String tripId,
  ) async {
    try {
      LoggerService.info('Calling API for trip absent students: $tripId');

      final response = await apiService.get(
        '${AppConstants.tripAbsentStudents}$tripId',
        isAuth: true,
      );

      LoggerService.debug(
        'Trip absent students API response',
        data: response.data,
      );

      if (response.data['data'] != null) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      }

      return [];
    } catch (e) {
      LoggerService.error('Failed to get trip absent students', error: e);
      rethrow;
    }
  }

  /// Get trip route details
  @override
  Future<Map<String, dynamic>> getTripRouteDetails(String tripId) async {
    try {
      LoggerService.info('Calling API for trip route details: $tripId');

      final response = await apiService.get(
        '${AppConstants.tripRouteDetails}$tripId',
        isAuth: true,
      );

      LoggerService.debug(
        'Trip route details API response',
        data: response.data,
      );

      return response.data;
    } catch (e) {
      LoggerService.error('Failed to get trip route details', error: e);
      rethrow;
    }
  }

  /// Map current trip API response to TripModel
  TripModel _mapCurrentTripToTripModel(Map<String, dynamic> data) {
    // Helper function to safely parse integers
    int parseInt(dynamic value, {int defaultValue = 0}) {
      if (value == null) return defaultValue;
      if (value is int) return value;
      if (value is String) {
        return int.tryParse(value) ?? defaultValue;
      }
      if (value is double) return value.toInt();
      return defaultValue;
    }

    // Helper function to safely parse doubles
    double parseDouble(dynamic value, {double defaultValue = 0.0}) {
      if (value == null) return defaultValue;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) {
        return double.tryParse(value) ?? defaultValue;
      }
      return defaultValue;
    }

    return TripModel(
      id: data['id']?.toString() ?? '',
      name: data['name']?.toString() ?? 'Current Trip',
      schoolId: data['school_id']?.toString() ?? '',
      schoolName: data['school_name']?.toString() ?? '',
      busId: data['bus_id']?.toString() ?? '',
      busNumber:
          data['bus']?['car_number']?.toString() ??
          data['car_number']?.toString() ??
          '',
      driverId: data['driver_id']?.toString() ?? '',
      driverName: data['driver_name']?.toString() ?? '',
      supervisorId:
          data['supervisor_id']?.toString() ??
          data['userable_id']?.toString() ??
          '',
      supervisorName:
          data['supervisor_name']?.toString() ?? data['name']?.toString() ?? '',
      startTime:
          data['created_at'] != null
              ? DateTime.parse(data['created_at'].toString())
              : DateTime.now(),
      endTime:
          data['end_at'] != null
              ? DateTime.parse(data['end_at'].toString())
              : null,
      status: data['status']?.toString() ?? 'active',
      studentIds: [], // Will be populated from separate API calls
      attendedStudentIds: [], // Will be populated from separate API calls
      startLocation: data['address']?.toString() ?? '',
      endLocation: data['address']?.toString() ?? '',
      distance: parseDouble(data['distance']), // Safe parsing for distance
      duration: parseInt(data['duration']), // Safe parsing for duration
      stops: [], // Will be populated from route API
      events: [], // Will be populated from route API
    );
  }

  /// Convert PreviousTripModel to TripModel
  TripModel _convertPreviousTripToTripModel(PreviousTripModel previousTrip) {
    try {
      // Extract supervisor information
      String supervisorName = previousTrip.supervisorName ?? '';
      String supervisorId = previousTrip.supervisorId?.toString() ?? '';

      // Find supervisor from attendants if not directly available
      if (supervisorName.isEmpty && previousTrip.attendants != null) {
        for (var attendant in previousTrip.attendants!) {
          if (attendant.type == 'admins') {
            supervisorName = attendant.name ?? '';
            supervisorId = attendant.id?.toString() ?? '';
            break;
          }
        }
      }

      // Extract driver information
      String driverName = '';
      String driverId = '';
      if (previousTrip.attendants != null) {
        for (var attendant in previousTrip.attendants!) {
          if (attendant.type == 'drivers') {
            driverName = attendant.name ?? '';
            driverId = attendant.id?.toString() ?? '';
            break;
          }
        }
      }

      // Parse start time from trips_date
      DateTime startTime = DateTime.now();
      if (previousTrip.tripsDate != null) {
        try {
          startTime = DateTime.parse(previousTrip.tripsDate!);
        } catch (e) {
          LoggerService.debug(
            'Failed to parse trips_date: ${previousTrip.tripsDate}',
          );
        }
      }

      // Parse end time from end_at
      DateTime? endTime;
      if (previousTrip.endAt != null) {
        try {
          endTime = DateTime.parse(previousTrip.endAt!);
        } catch (e) {
          LoggerService.debug('Failed to parse end_at: ${previousTrip.endAt}');
        }
      }

      return TripModel(
        id: previousTrip.id?.toString() ?? '',
        name: previousTrip.tripType ?? 'Previous Trip',
        schoolId: previousTrip.schoolId?.toString() ?? '',
        schoolName: '', // Not available in previous trip data
        busId: previousTrip.busId?.toString() ?? '',
        busNumber: previousTrip.bus?.name ?? previousTrip.busName ?? '',
        driverId: driverId,
        driverName: driverName,
        supervisorId: supervisorId,
        supervisorName: supervisorName,
        startTime: startTime,
        endTime: endTime,
        status: previousTrip.status == 1 ? 'completed' : 'cancelled',
        studentIds: [], // Will be populated from separate API calls
        attendedStudentIds: [], // Will be populated from separate API calls
        startLocation: '', // Not available in previous trip data
        endLocation: '', // Not available in previous trip data
        distance: 0.0, // Not available in previous trip data
        duration: 0, // Not available in previous trip data
        stops: [], // Will be populated from route API
        events: [], // Will be populated from route API
      );
    } catch (e) {
      LoggerService.error(
        'Error converting previous trip to trip model: ${e.toString()}',
      );
      LoggerService.debug(
        'Previous trip data that caused error',
        data: previousTrip.toJson(),
      );

      // Return a default trip model to prevent crashes
      return TripModel(
        id: previousTrip.id?.toString() ?? '',
        name: 'Previous Trip',
        schoolId: '',
        schoolName: '',
        busId: '',
        busNumber: '',
        driverId: '',
        driverName: '',
        supervisorId: '',
        supervisorName: '',
        startTime: DateTime.now(),
        endTime: null,
        status: 'completed',
        studentIds: [],
        attendedStudentIds: [],
        startLocation: '',
        endLocation: '',
        distance: 0.0,
        duration: 0,
        stops: [],
        events: [],
      );
    }
  }

  // Enhanced trip management implementations

  @override
  Future<List<TripModel>> getTrips({
    int page = 1,
    int limit = 10,
    String? search,
    String? status,
    String? busId,
    String? driverId,
    String? supervisorId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      LoggerService.info(
        'Calling API for trips with filters',
        data: {
          'page': page,
          'limit': limit,
          'search': search,
          'status': status,
          'busId': busId,
          'driverId': driverId,
          'supervisorId': supervisorId,
          'startDate': startDate?.toIso8601String(),
          'endDate': endDate?.toIso8601String(),
        },
      );

      // Build query parameters
      Map<String, dynamic> queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      if (status != null && status.isNotEmpty) {
        queryParams['status'] = status;
      }
      if (busId != null && busId.isNotEmpty) {
        queryParams['bus_id'] = busId;
      }
      if (driverId != null && driverId.isNotEmpty) {
        queryParams['driver_id'] = driverId;
      }
      if (supervisorId != null && supervisorId.isNotEmpty) {
        queryParams['supervisor_id'] = supervisorId;
      }
      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String().split('T')[0];
      }
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String().split('T')[0];
      }

      final response = await apiService.get(
        AppConstants.tripEndpoint,
        queryParameters: queryParams,
        isAuth: true,
      );

      LoggerService.debug('Trips API response', data: response.data);

      if (response.data['data'] != null) {
        final tripsData = response.data['data'] as List;
        return tripsData
            .map((tripData) => _mapCurrentTripToTripModel(tripData))
            .toList();
      }

      return [];
    } catch (e) {
      LoggerService.error('Failed to get trips', error: e);
      rethrow;
    }
  }

  @override
  Future<TripModel> createTrip(Map<String, dynamic> tripData) async {
    try {
      LoggerService.info('Creating trip', data: tripData);

      final response = await apiService.post(
        url: AppConstants.tripEndpoint,
        body: tripData,
        isAuth: true,
      );

      LoggerService.debug('Create trip API response', data: response.data);

      if (response.data['data'] != null) {
        return _mapCurrentTripToTripModel(response.data['data']);
      }

      throw Exception('Failed to create trip');
    } catch (e) {
      LoggerService.error('Failed to create trip', error: e);
      rethrow;
    }
  }

  @override
  Future<TripModel> updateTrip(String id, Map<String, dynamic> tripData) async {
    try {
      LoggerService.info('Updating trip', data: {'id': id, 'data': tripData});

      final response = await apiService.put(
        url: '${AppConstants.tripEndpoint}/$id',
        body: tripData,
        isAuth: true,
      );

      LoggerService.debug('Update trip API response', data: response.data);

      if (response.data['data'] != null) {
        return _mapCurrentTripToTripModel(response.data['data']);
      }

      throw Exception('Failed to update trip');
    } catch (e) {
      LoggerService.error('Failed to update trip', error: e);
      rethrow;
    }
  }

  @override
  Future<bool> deleteTrip(String id) async {
    try {
      LoggerService.info('Deleting trip', data: {'id': id});

      final response = await apiService.delete(
        url: '${AppConstants.tripEndpoint}/$id',
        body: {},
        isAuth: true,
      );

      LoggerService.debug('Delete trip API response', data: response.data);

      return response.data['status'] == true;
    } catch (e) {
      LoggerService.error('Failed to delete trip', error: e);
      rethrow;
    }
  }

  @override
  Future<TripModel> startTrip(String id) async {
    try {
      LoggerService.info('Starting trip', data: {'id': id});

      final response = await apiService.post(
        url: '${AppConstants.tripEndpoint}/$id/start',
        body: {},
        isAuth: true,
      );

      LoggerService.debug('Start trip API response', data: response.data);

      if (response.data['data'] != null) {
        return _mapCurrentTripToTripModel(response.data['data']);
      }

      throw Exception('Failed to start trip');
    } catch (e) {
      LoggerService.error('Failed to start trip', error: e);
      rethrow;
    }
  }

  @override
  Future<TripModel> endTrip(String id) async {
    try {
      LoggerService.info('Ending trip', data: {'id': id});

      final response = await apiService.post(
        url: '${AppConstants.tripEndpoint}/$id/end',
        body: {},
        isAuth: true,
      );

      LoggerService.debug('End trip API response', data: response.data);

      if (response.data['data'] != null) {
        return _mapCurrentTripToTripModel(response.data['data']);
      }

      throw Exception('Failed to end trip');
    } catch (e) {
      LoggerService.error('Failed to end trip', error: e);
      rethrow;
    }
  }

  @override
  Future<bool> updateTripLocation(
    String id,
    double latitude,
    double longitude,
  ) async {
    try {
      LoggerService.info(
        'Updating trip location',
        data: {'id': id, 'latitude': latitude, 'longitude': longitude},
      );

      final response = await apiService.post(
        url: '${AppConstants.tripEndpoint}/$id/location',
        body: {'latitude': latitude, 'longitude': longitude},
        isAuth: true,
      );

      LoggerService.debug(
        'Update trip location API response',
        data: response.data,
      );

      return response.data['status'] == true;
    } catch (e) {
      LoggerService.error('Failed to update trip location', error: e);
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getTripTracking(String id) async {
    try {
      LoggerService.info('Getting trip tracking data', data: {'id': id});

      final response = await apiService.get(
        '${AppConstants.tripEndpoint}/$id/tracking',
        isAuth: true,
      );

      LoggerService.debug('Trip tracking API response', data: response.data);

      return response.data;
    } catch (e) {
      LoggerService.error('Failed to get trip tracking data', error: e);
      rethrow;
    }
  }

  @override
  Future<List<TripModel>> getMorningTrips({
    DateTime? date,
    String? busId,
  }) async {
    try {
      LoggerService.info(
        'Getting morning trips',
        data: {'date': date?.toIso8601String(), 'busId': busId},
      );

      Map<String, dynamic> queryParams = {'trip_type': 'morning'};

      if (date != null) {
        queryParams['date'] = date.toIso8601String().split('T')[0];
      }
      if (busId != null && busId.isNotEmpty) {
        queryParams['bus_id'] = busId;
      }

      final response = await apiService.get(
        AppConstants.tripEndpoint,
        queryParameters: queryParams,
        isAuth: true,
      );

      LoggerService.debug('Morning trips API response', data: response.data);

      if (response.data['data'] != null) {
        final tripsData = response.data['data'] as List;
        return tripsData
            .map((tripData) => _mapCurrentTripToTripModel(tripData))
            .toList();
      }

      return [];
    } catch (e) {
      LoggerService.error('Failed to get morning trips', error: e);
      rethrow;
    }
  }

  @override
  Future<List<TripModel>> getEveningTrips({
    DateTime? date,
    String? busId,
  }) async {
    try {
      LoggerService.info(
        'Getting evening trips',
        data: {'date': date?.toIso8601String(), 'busId': busId},
      );

      Map<String, dynamic> queryParams = {'trip_type': 'evening'};

      if (date != null) {
        queryParams['date'] = date.toIso8601String().split('T')[0];
      }
      if (busId != null && busId.isNotEmpty) {
        queryParams['bus_id'] = busId;
      }

      final response = await apiService.get(
        AppConstants.tripEndpoint,
        queryParameters: queryParams,
        isAuth: true,
      );

      LoggerService.debug('Evening trips API response', data: response.data);

      if (response.data['data'] != null) {
        final tripsData = response.data['data'] as List;
        return tripsData
            .map((tripData) => _mapCurrentTripToTripModel(tripData))
            .toList();
      }

      return [];
    } catch (e) {
      LoggerService.error('Failed to get evening trips', error: e);
      rethrow;
    }
  }
}

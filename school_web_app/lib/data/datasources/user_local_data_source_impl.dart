import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/failures.dart';
import '../models/user_model.dart';
import 'user_local_data_source.dart';

/// Implementation of UserLocalDataSource
/// Following Single Responsibility Principle by focusing only on local data operations
class UserLocalDataSourceImpl implements UserLocalDataSource {
  final SharedPreferences sharedPreferences;

  // Keys for storing data
  static const String userKey = 'cached_user';
  static const String tokenKey = AppConstants.tokenKey;

  UserLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<UserModel> getCachedUser() async {
    // Check if user data exists
    final jsonString = sharedPreferences.getString(userKey);

    if (jsonString == null) {
      throw CacheFailure(message: 'No cached user found');
    }

    // Parse user data
    try {
      final Map<String, dynamic> jsonMap = json.decode(jsonString);
      return UserModel.fromJson(jsonMap);
    } catch (e) {
      throw CacheFailure(message: 'Failed to parse cached user data');
    }
  }

  @override
  Future<void> cacheUser(UserModel user) async {
    // Convert user to JSON
    final jsonString = json.encode(user.toJson());

    // Save user data
    await sharedPreferences.setString(userKey, jsonString);
  }

  @override
  Future<bool> clearCachedUser() async {
    // Remove user data
    return await sharedPreferences.remove(userKey);
  }

  @override
  Future<void> cacheToken(String token) async {
    // Save token
    await sharedPreferences.setString(tokenKey, token);
  }

  @override
  Future<String?> getCachedToken() async {
    // Get token
    return sharedPreferences.getString(tokenKey);
  }

  @override
  Future<bool> clearCachedToken() async {
    // Remove token
    return await sharedPreferences.remove(tokenKey);
  }

  @override
  Future<bool> isAuthenticated() async {
    // Check if token exists
    final token = await getCachedToken();
    return token != null && token.isNotEmpty;
  }
}

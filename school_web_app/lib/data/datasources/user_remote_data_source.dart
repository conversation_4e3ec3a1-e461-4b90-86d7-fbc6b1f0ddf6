import '../models/user_model.dart';

/// UserRemoteDataSource interface
/// Following Interface Segregation Principle by creating a specific interface for remote data operations
abstract class UserRemoteDataSource {
  /// Login with email and password
  Future<UserModel> login(String email, String password);

  /// Register a new user
  Future<UserModel> register(UserModel user, String password);

  /// Get current user
  Future<UserModel> getCurrentUser();

  /// Logout current user
  Future<bool> logout();

  /// Forgot password
  Future<bool> forgotPassword(String email);

  /// Reset password
  Future<bool> resetPassword(String email, String code, String password);

  /// Verify email
  Future<bool> verifyEmail(String email, String code);

  /// Resend verification code
  Future<bool> resendVerificationCode(String email);

  /// Refresh authentication token
  Future<String> refreshToken();

  /// Update user profile
  Future<UserModel> updateProfile(UserModel user);

  /// Change password
  Future<bool> changePassword(String currentPassword, String newPassword);

  /// Get all users
  Future<List<UserModel>> getUsers();

  /// Get user by id
  Future<UserModel> getUserById(String id);

  /// Update user
  Future<UserModel> updateUser(UserModel user);

  /// Delete user
  Future<bool> deleteUser(String id);

  /// Upload profile image
  Future<String> uploadProfileImage(String userId, String imagePath);
}

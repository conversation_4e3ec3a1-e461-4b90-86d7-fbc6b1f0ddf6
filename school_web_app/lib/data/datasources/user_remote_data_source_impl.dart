import 'package:dio/dio.dart' as dio;
import '../../core/constants/app_constants.dart';
import '../../core/errors/exceptions.dart';
import '../../core/network/api_service.dart';
import '../../core/utils/logger.dart';
import '../models/user_model.dart';
import 'user_local_data_source.dart';
import 'user_remote_data_source.dart';

/// Implementation of UserRemoteDataSource
/// Following Single Responsibility Principle by focusing only on remote data operations
class UserRemoteDataSourceImpl implements UserRemoteDataSource {
  final ApiService apiService;
  final UserLocalDataSource? localDataSource;

  UserRemoteDataSourceImpl(this.apiService, {this.localDataSource});

  // List to store users from API
  final List<UserModel> _users = [];

  // Current user
  UserModel? _currentUser;

  @override
  Future<UserModel> login(String email, String password) async {
    LoggerService.debug(
      'UserRemoteDataSourceImpl.login called',
      data: {'email': email},
    );

    try {
      // Call the real API
      LoggerService.info('Calling API for login');

      final response = await apiService.post(
        url: AppConstants.loginEndpoint,
        body: {'email': email, 'password': password},
      );

      LoggerService.debug('Login API response', data: response.data);

      // Check if response contains expected data structure
      if (response.data == null) {
        throw ServerException(message: 'Invalid response from server');
      }

      // Check for error messages in response
      if (response.data['message'] != null && response.data['data'] == null) {
        final errorMessage = response.data['message'].toString();
        LoggerService.error('Login API returned error', error: errorMessage);
        throw ServerException(message: errorMessage);
      }

      // Parse response
      final userData = response.data['data'];
      final token = response.data['token'] ?? '';

      // Validate required data
      if (userData == null) {
        throw ServerException(message: 'No user data in response');
      }

      // Create user model from response
      final user = UserModel.fromJson(userData);

      // Set token in API service
      if (token.isNotEmpty) {
        await apiService.setToken(token);
      }

      // Set current user
      _currentUser = user;

      // Store user in local data source
      try {
        if (localDataSource != null) {
          // Cache user data
          await localDataSource!.cacheUser(user);
          // Cache token
          if (token.isNotEmpty) {
            await localDataSource!.cacheToken(token);
            LoggerService.debug(
              'Token stored in local data source',
              data: {'tokenLength': token.length},
            );
          }
          LoggerService.debug(
            'User data stored in local data source',
            data: {'userId': user.id},
          );
        }
      } catch (e) {
        LoggerService.error(
          'Failed to store user data in local data source',
          error: e,
        );
      }

      // Add user to the list for local operations
      if (!_users.any((u) => u.id == user.id)) {
        _users.add(user);
      }

      LoggerService.success('Login successful', data: {'userId': user.id});
      return user;
    } catch (e) {
      LoggerService.error('Login failed', error: e);
      rethrow;
    }
  }

  @override
  Future<UserModel> register(UserModel user, String password) async {
    try {
      LoggerService.info('Calling API for register');

      // Prepare request body with all required fields (based on original project)
      final requestBody = {
        'name': user.name,
        'email': user.email,
        'password': password,
        'confirmed':
            password, // Use 'confirmed' instead of 'password_confirmation'
        'phone': user.phone,
        'address': user.address ?? '',
        'latitude': user.latitude?.toString() ?? '0.0',
        'longitude': user.longitude?.toString() ?? '0.0',
      };

      LoggerService.debug('Register request body', data: requestBody);

      final response = await apiService.post(
        url: AppConstants.registerEndpoint,
        body: requestBody,
      );

      LoggerService.debug('Register API response', data: response.data);

      // Check if response contains expected data structure
      if (response.data == null) {
        throw ServerException(message: 'Invalid response from server');
      }

      // Check for validation errors or other error messages
      if (response.data['errors'] != null) {
        final errors = response.data['errors'];
        final messages = response.data['messages'];

        // Extract error messages
        String errorMessage = 'فشل في التسجيل';

        // Handle different error formats
        if (messages != null && messages is Map<String, dynamic>) {
          final errorList = <String>[];
          messages.forEach((key, value) {
            if (value is List) {
              errorList.addAll(value.map((e) => e.toString()));
            } else {
              errorList.add(value.toString());
            }
          });
          errorMessage = errorList.join('\n');
        } else if (response.data['message'] != null) {
          errorMessage = response.data['message'].toString();
        }

        LoggerService.error(
          'Registration validation errors',
          error: {
            'errors': errors,
            'messages': messages,
            'errorMessage': errorMessage,
          },
        );

        throw ValidationException(
          message: errorMessage,
          errors: errors is Map<String, dynamic> ? errors : null,
        );
      }

      // Check for general error message
      if (response.data['message'] != null && response.data['data'] == null) {
        final errorMessage = response.data['message'].toString();
        LoggerService.error(
          'Registration API returned error',
          error: errorMessage,
        );
        throw ServerException(message: errorMessage);
      }

      // Parse successful response
      final userData = response.data['data'];
      final token = response.data['token'] ?? '';

      // Validate required data
      if (userData == null) {
        throw ServerException(message: 'No user data in response');
      }

      // Create user model from response
      final registeredUser = UserModel.fromJson(userData);

      // Set token in API service
      if (token.isNotEmpty) {
        await apiService.setToken(token);
      }

      // Set current user
      _currentUser = registeredUser;

      // Add user to the list for local operations
      if (!_users.any((u) => u.id == registeredUser.id)) {
        _users.add(registeredUser);
      }

      LoggerService.success(
        'Registration successful',
        data: {'userId': registeredUser.id},
      );
      return registeredUser;
    } on ValidationException {
      // Re-throw validation exceptions as-is
      rethrow;
    } on ServerException {
      // Re-throw server exceptions as-is
      rethrow;
    } catch (e) {
      LoggerService.error('Registration failed', error: e);
      // Convert other exceptions to ServerException
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<UserModel> getCurrentUser() async {
    try {
      // First, try to get user from memory
      if (_currentUser != null) {
        LoggerService.info('Using in-memory current user');

        // Make sure token is set in API service
        if (localDataSource != null) {
          try {
            final token = await localDataSource!.getCachedToken();
            if (token != null && token.isNotEmpty) {
              await apiService.setToken(token);
              LoggerService.debug(
                'Token restored from local storage',
                data: {'tokenLength': token.length},
              );
            }
          } catch (e) {
            LoggerService.error(
              'Failed to get token from local data source',
              error: e,
            );
          }
        }

        return _currentUser!;
      }

      // Second, try to get user from local data source
      if (localDataSource != null) {
        try {
          // Get token from local data source first
          final token = await localDataSource!.getCachedToken();
          if (token != null && token.isNotEmpty) {
            await apiService.setToken(token);
            LoggerService.debug(
              'Token restored from local storage',
              data: {'tokenLength': token.length},
            );
          }

          // Get user from local data source
          final user = await localDataSource!.getCachedUser();

          LoggerService.info(
            'Using user from local data source',
            data: {'userId': user.id},
          );

          // Set current user
          _currentUser = user;
          return user;
        } catch (e) {
          LoggerService.error(
            'Failed to get user from local data source',
            error: e,
          );
          // Continue to API call since we couldn't get the stored data
        }
      }

      // Finally, try to get user from API
      LoggerService.info('Calling API for current user');

      final response = await apiService.get(AppConstants.userProfileEndpoint);

      LoggerService.debug('Get current user API response', data: response.data);

      // Check if response contains data
      if (response.data == null || response.data['data'] == null) {
        throw UnauthorizedException(message: 'User not logged in');
      }

      // Parse response
      final userData = response.data['data'];

      // Create user model from response
      final user = UserModel.fromJson(userData);

      // Set current user
      _currentUser = user;

      // Store user in local data source
      try {
        if (localDataSource != null) {
          // Cache user data
          await localDataSource!.cacheUser(user);
          // Get token and cache it if available
          final token = await apiService.getToken();
          if (token != null && token.isNotEmpty) {
            await localDataSource!.cacheToken(token);
          }
          LoggerService.debug(
            'User data stored in local data source',
            data: {'userId': user.id},
          );
        }
      } catch (e) {
        LoggerService.error(
          'Failed to store user data in local data source',
          error: e,
        );
      }

      // Add user to the list for local operations
      if (!_users.any((u) => u.id == user.id)) {
        _users.add(user);
      }

      LoggerService.success(
        'Got current user from API',
        data: {'userId': user.id},
      );
      return user;
    } catch (e) {
      LoggerService.error('Failed to get current user', error: e);
      throw UnauthorizedException(message: 'User not logged in');
    }
  }

  @override
  Future<bool> logout() async {
    try {
      LoggerService.info('Calling API for logout');

      await apiService.post(url: AppConstants.logoutEndpoint);

      // Clear token
      await apiService.clearToken();

      // Clear current user
      _currentUser = null;

      // Clear user data from local data source
      try {
        if (localDataSource != null) {
          await localDataSource!.clearCachedUser();
          await localDataSource!.clearCachedToken();
          LoggerService.debug('User data cleared from local data source');
        }
      } catch (e) {
        LoggerService.error(
          'Failed to clear user data from local data source',
          error: e,
        );
      }

      LoggerService.success('Logout successful');
      return true;
    } catch (e) {
      LoggerService.error(
        'Logout failed, clearing local session anyway',
        error: e,
      );

      // Even if the API call fails, clear the local session
      await apiService.clearToken();
      _currentUser = null;

      // Clear user data from local data source
      try {
        if (localDataSource != null) {
          await localDataSource!.clearCachedUser();
          await localDataSource!.clearCachedToken();
          LoggerService.debug('User data cleared from local data source');
        }
      } catch (e) {
        LoggerService.error(
          'Failed to clear user data from local data source',
          error: e,
        );
      }

      return true;
    }
  }

  @override
  Future<List<UserModel>> getUsers() async {
    try {
      LoggerService.info('Calling API for get users');

      // In SchoolX, there's no direct endpoint for all users, so we'll use a more specific endpoint
      final response = await apiService.get(
        AppConstants.supervisorEndpoint, // Get supervisors as users
      );

      LoggerService.debug('Get users API response', data: response.data);

      // Parse response
      final usersData = response.data['data'] as List;

      // Clear existing users and add new ones
      _users.clear();
      final users =
          usersData.map((userData) => UserModel.fromJson(userData)).toList();
      _users.addAll(users);

      return users;
    } catch (e) {
      LoggerService.error('Failed to get users', error: e);
      // Return local users if available
      return _users;
    }
  }

  @override
  Future<UserModel> getUserById(String id) async {
    try {
      LoggerService.info('Calling API for get user by id: $id');

      // In SchoolX, we use the parentShow endpoint to get user details
      final response = await apiService.get(
        '${AppConstants.parentShowEndpoint}/$id',
      );

      LoggerService.debug('Get user by id API response', data: response.data);

      // Parse response
      final userData = response.data['data'];

      // Create user model from response
      final user = UserModel.fromJson(userData);

      // Update in local list
      final index = _users.indexWhere((u) => u.id == user.id);
      if (index != -1) {
        _users[index] = user;
      } else {
        _users.add(user);
      }

      return user;
    } catch (e) {
      LoggerService.error('Failed to get user by id', error: e);

      // Try to find user in local list
      final localUser = _users.firstWhere(
        (user) => user.id == id,
        orElse: () => throw NotFoundException(message: 'User not found'),
      );

      return localUser;
    }
  }

  @override
  Future<UserModel> updateUser(UserModel user) async {
    try {
      LoggerService.info(
        'Calling API to update user',
        data: {'userId': user.id},
      );

      // In SchoolX, we use the updateProfile endpoint
      final response = await apiService.post(
        url: AppConstants.userUpdateProfileEndpoint,
        body: user.toJson(),
      );

      LoggerService.debug('Update user API response', data: response.data);

      // Parse response
      final userData = response.data['data'];

      // Create user model from response
      final updatedUser = UserModel.fromJson(userData);

      // Update in local list
      final index = _users.indexWhere((u) => u.id == updatedUser.id);
      if (index != -1) {
        _users[index] = updatedUser;
      } else {
        _users.add(updatedUser);
      }

      // Update current user if needed
      if (_currentUser != null && _currentUser!.id == updatedUser.id) {
        _currentUser = updatedUser;
      }

      return updatedUser;
    } catch (e) {
      LoggerService.error('Failed to update user', error: e);
      rethrow;
    }
  }

  @override
  Future<bool> deleteUser(String id) async {
    try {
      LoggerService.info('Calling API to delete user', data: {'userId': id});

      // In SchoolX, we use the deleteSupervisorAndDriver endpoint
      await apiService.delete(
        url: '${AppConstants.deleteSupervisorAndDriverEndpoint}/$id',
      );

      // Remove from local list
      final index = _users.indexWhere((u) => u.id == id);
      if (index != -1) {
        _users.removeAt(index);
      }

      // Clear current user if needed
      if (_currentUser != null && _currentUser!.id == id) {
        _currentUser = null;
      }

      LoggerService.success('User deleted successfully', data: {'userId': id});
      return true;
    } catch (e) {
      LoggerService.error('Failed to delete user', error: e);
      rethrow;
    }
  }

  @override
  Future<bool> forgotPassword(String email) async {
    try {
      LoggerService.info(
        'Calling API for forgot password',
        data: {'email': email},
      );

      final response = await apiService.post(
        url: AppConstants.forgotPasswordEndpoint,
        body: {'email': email},
      );

      LoggerService.debug('Forgot password API response', data: response.data);
      LoggerService.success('Password reset email sent successfully');
      return true;
    } catch (e) {
      LoggerService.error('Failed to send password reset email', error: e);
      rethrow;
    }
  }

  @override
  Future<bool> resetPassword(String email, String code, String password) async {
    try {
      LoggerService.info(
        'Calling API for reset password',
        data: {'email': email},
      );

      final response = await apiService.post(
        url: AppConstants.resetPasswordEndpoint,
        body: {
          'email': email,
          'token': code,
          'password': password,
          'password_confirmation': password,
        },
      );

      LoggerService.debug('Reset password API response', data: response.data);
      LoggerService.success('Password reset successfully');
      return true;
    } catch (e) {
      LoggerService.error('Failed to reset password', error: e);
      rethrow;
    }
  }

  @override
  Future<bool> verifyEmail(String email, String code) async {
    try {
      LoggerService.info(
        'Calling API for verify email',
        data: {'email': email},
      );

      final response = await apiService.post(
        url: AppConstants.verifyEmailEndpoint,
        body: {'email': email, 'token': code},
      );

      LoggerService.debug('Verify email API response', data: response.data);

      // If we have this user in local list, update verification status
      final userIndex = _users.indexWhere((user) => user.email == email);
      if (userIndex != -1) {
        final user = _users[userIndex];
        _users[userIndex] = user.copyWith(isVerified: true);

        // Update current user if needed
        if (_currentUser != null && _currentUser!.email == email) {
          _currentUser = _users[userIndex];
        }
      }

      LoggerService.success('Email verified successfully');
      return true;
    } catch (e) {
      LoggerService.error('Failed to verify email', error: e);
      rethrow;
    }
  }

  @override
  Future<bool> resendVerificationCode(String email) async {
    try {
      LoggerService.info(
        'Calling API to resend verification code',
        data: {'email': email},
      );

      // SchoolX doesn't have a direct endpoint for resending verification code,
      // so we'll reuse the verify endpoint
      final response = await apiService.post(
        url: AppConstants.verifyEmailEndpoint,
        body: {'email': email, 'resend': true},
      );

      LoggerService.debug(
        'Resend verification code API response',
        data: response.data,
      );
      LoggerService.success('Verification code resent successfully');
      return true;
    } catch (e) {
      LoggerService.error('Failed to resend verification code', error: e);
      rethrow;
    }
  }

  @override
  Future<String> refreshToken() async {
    try {
      LoggerService.info('Calling API to refresh token');

      final response = await apiService.post(
        url: AppConstants.refreshTokenEndpoint,
        isAuth: true,
      );

      LoggerService.debug('Refresh token API response', data: response.data);

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to refresh token';
        throw ServerException(message: errorMessage);
      }

      // Extract new token
      final newToken = responseData['data']?['token'] as String?;
      if (newToken == null || newToken.isEmpty) {
        throw ServerException(
          message: 'No token received from refresh endpoint',
        );
      }

      // Update the API service with new token
      await apiService.setToken(newToken);

      LoggerService.success('Token refreshed successfully');
      return newToken;
    } catch (e) {
      LoggerService.error('Failed to refresh token', error: e);
      rethrow;
    }
  }

  @override
  Future<UserModel> updateProfile(UserModel user) async {
    try {
      LoggerService.info(
        'Calling API to update profile',
        data: {'userId': user.id},
      );

      // Get the current user to access the current values for required fields
      UserModel? currentUser = _currentUser;

      // If current user is null, try to get it from local data source
      if (currentUser == null && localDataSource != null) {
        try {
          currentUser = await localDataSource!.getCachedUser();
          // Also set the token from local data source
          final token = await localDataSource!.getCachedToken();
          if (token != null && token.isNotEmpty) {
            await apiService.setToken(token);
          }
          // Update in-memory current user
          _currentUser = currentUser;
        } catch (e) {
          LoggerService.error(
            'Failed to get user from local data source',
            error: e,
          );
        }
      }

      if (currentUser == null) {
        throw UnauthorizedException(message: 'User not logged in');
      }

      // Prepare the request body with all required fields
      final Map<String, dynamic> requestBody = {
        'name': user.name,
        'email': user.email,
        'phone': user.phone ?? currentUser.phone ?? '',
        'address': user.address ?? currentUser.address ?? '',
        // Required fields from the error message
        'city_name':
            user.cityName ??
            currentUser.cityName ??
            'D', // Default to 'D' if not available
        'current_password':
            '<EMAIL>', // Using the same password as login for now
        'latitude': (user.latitude ?? currentUser.latitude ?? 90.0).toString(),
        'longitude':
            (user.longitude ?? currentUser.longitude ?? 90.0).toString(),
        // Additional fields that might be required
        'status': 1, // Default status value
        'trip_start_end_notification_status': 1,
        'student_absence_notification_status': 0,
        'student_address_notification_status': 0,
      };

      LoggerService.debug('Update profile request body', data: requestBody);

      final response = await apiService.post(
        url: AppConstants.userUpdateProfileEndpoint,
        body: requestBody,
      );

      LoggerService.debug('Update profile API response', data: response.data);

      // Parse response
      final userData = response.data['data'];

      // Create user model from response
      final updatedUser = UserModel.fromJson(userData);

      // Update in local list
      final index = _users.indexWhere((u) => u.id == updatedUser.id);
      if (index != -1) {
        _users[index] = updatedUser;
      } else {
        _users.add(updatedUser);
      }

      // Update current user if needed
      if (_currentUser != null && _currentUser!.id == updatedUser.id) {
        _currentUser = updatedUser;

        // Store updated user in local data source
        try {
          if (localDataSource != null) {
            // Cache updated user data
            await localDataSource!.cacheUser(updatedUser);
            LoggerService.debug(
              'Updated user data stored in local data source',
              data: {'userId': updatedUser.id},
            );
          }
        } catch (e) {
          LoggerService.error(
            'Failed to store updated user data in local data source',
            error: e,
          );
        }
      }

      LoggerService.success(
        'Profile updated successfully',
        data: {'userId': updatedUser.id},
      );
      return updatedUser;
    } catch (e) {
      LoggerService.error('Failed to update profile', error: e);
      rethrow;
    }
  }

  @override
  Future<bool> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    try {
      LoggerService.info('Calling API to change password');

      final response = await apiService.post(
        url: AppConstants.changePasswordEndpoint,
        body: {
          'current_password': currentPassword,
          'password': newPassword,
          'password_confirmation': newPassword,
        },
      );

      LoggerService.debug('Change password API response', data: response.data);

      LoggerService.success('Password changed successfully');
      return true;
    } catch (e) {
      LoggerService.error('Failed to change password', error: e);
      rethrow;
    }
  }

  @override
  Future<String> uploadProfileImage(String userId, String imagePath) async {
    try {
      LoggerService.info('Uploading profile image for user: $userId');
      LoggerService.debug('Image path: $imagePath');

      // Create form data for file upload
      final formData = dio.FormData.fromMap({
        'image': await dio.MultipartFile.fromFile(imagePath),
      });

      final response = await apiService.post(
        url: '${AppConstants.userProfileEndpoint}/upload-image',
        body: formData,
      );

      LoggerService.debug(
        'Upload profile image API response',
        data: response.data,
      );

      // Parse response to get image URL
      final imageUrl = response.data['data']['image_url'] ?? '';

      // Update user in local list if found
      final userIndex = _users.indexWhere((u) => u.id == userId);
      if (userIndex != -1) {
        final user = _users[userIndex];
        _users[userIndex] = user.copyWith(imageUrl: imageUrl);

        // Update current user if needed
        if (_currentUser != null && _currentUser!.id == userId) {
          _currentUser = _users[userIndex];
        }
      }

      LoggerService.success(
        'Profile image uploaded successfully',
        data: {'userId': userId, 'imageUrl': imageUrl},
      );

      return imageUrl;
    } catch (e) {
      LoggerService.error('Failed to upload profile image', error: e);
      rethrow;
    }
  }
}

import '../../domain/entities/absence_request.dart';

/// Model class for absence requests
class AbsenceRequestModel extends AbsenceRequest {
  const AbsenceRequestModel({
    super.id,
    super.gradeId,
    super.schoolId,
    super.classroomId,
    super.busId,
    super.parentId,
    super.studentId,
    super.attendanceDate,
    super.attendanceType,
    super.status,
    super.createdAt,
    super.updatedAt,
    super.school,
    super.bus,
    super.grade,
    super.classroom,
    super.parent,
    super.student,
  });

  /// Create AbsenceRequestModel from JSON
  factory AbsenceRequestModel.fromJson(Map<String, dynamic> json) {
    return AbsenceRequestModel(
      id: _parseId(json['id']),
      gradeId: _parseString(json['grade_id']),
      schoolId: _parseString(json['school_id']),
      classroomId: _parseString(json['classroom_id']),
      busId: _parseString(json['bus_id']),
      parentId: _parseString(json['my__parent_id']),
      studentId: _parseString(json['student_id']),
      attendanceDate: _parseString(json['attendence_date']),
      attendanceType: _parseString(json['attendence_type']),
      status: _parseString(json['status']) ?? 'pending',
      createdAt: _parseString(json['created_at']),
      updatedAt: _parseString(json['updated_at']),
      school:
          json['schools'] != null
              ? SchoolModel.fromJson(json['schools'])
              : null,
      bus: json['bus'] != null ? BusModel.fromJson(json['bus']) : null,
      grade: json['grade'] != null ? GradeModel.fromJson(json['grade']) : null,
      classroom:
          json['classroom'] != null
              ? ClassroomModel.fromJson(json['classroom'])
              : null,
      parent:
          json['parent'] != null ? ParentModel.fromJson(json['parent']) : null,
      student:
          json['students'] != null
              ? StudentModel.fromJson(json['students'])
              : null,
    );
  }

  /// Convert AbsenceRequestModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'grade_id': gradeId,
      'school_id': schoolId,
      'classroom_id': classroomId,
      'bus_id': busId,
      'my__parent_id': parentId,
      'student_id': studentId,
      'attendence_date': attendanceDate,
      'attendence_type': attendanceType,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}

/// School model
class SchoolModel extends School {
  const SchoolModel({
    super.id,
    super.name,
    super.email,
    super.phone,
    super.address,
    super.logo,
  });

  factory SchoolModel.fromJson(Map<String, dynamic> json) {
    return SchoolModel(
      id: _parseId(json['id']),
      name: _parseString(json['name']),
      email: _parseString(json['email']),
      phone: _parseString(json['phone']),
      address: _parseString(json['address']),
      logo: _parseString(json['logo']),
    );
  }
}

/// Bus model
class BusModel extends Bus {
  const BusModel({super.id, super.name, super.carNumber, super.notes});

  factory BusModel.fromJson(Map<String, dynamic> json) {
    return BusModel(
      id: _parseId(json['id']),
      name: _parseString(json['name']),
      carNumber: _parseString(json['car_number']),
      notes: _parseString(json['notes']),
    );
  }
}

/// Grade model
class GradeModel extends Grade {
  const GradeModel({super.id, super.name});

  factory GradeModel.fromJson(Map<String, dynamic> json) {
    return GradeModel(
      id: _parseId(json['id']),
      name: _parseString(json['name']),
    );
  }
}

/// Classroom model
class ClassroomModel extends Classroom {
  const ClassroomModel({super.id, super.name});

  factory ClassroomModel.fromJson(Map<String, dynamic> json) {
    return ClassroomModel(
      id: _parseId(json['id']),
      name: _parseString(json['name']),
    );
  }
}

/// Parent model for absence request
class ParentModel extends Parent {
  const ParentModel({super.id, super.name, super.phone, super.email});

  factory ParentModel.fromJson(Map<String, dynamic> json) {
    return ParentModel(
      id: _parseId(json['id']),
      name: _parseString(json['name']),
      phone: _parseString(json['phone']),
      email: _parseString(json['email']),
    );
  }
}

/// Student model for absence request
class StudentModel extends Student {
  const StudentModel({
    super.id,
    super.name,
    super.phone,
    super.gradeId,
    super.genderId,
    super.schoolId,
    super.religionId,
    super.typeBloodId,
    super.classroomId,
    super.busId,
    super.address,
    super.cityName,
    super.status,
    super.tripType,
    super.attendanceType,
    super.latitude,
    super.longitude,
  });

  factory StudentModel.fromJson(Map<String, dynamic> json) {
    return StudentModel(
      id: _parseId(json['id']),
      name: _parseString(json['name']),
      phone: _parseString(json['phone']),
      gradeId: _parseString(json['grade_id']),
      genderId: _parseString(json['gender_id']),
      schoolId: _parseString(json['school_id']),
      religionId: _parseString(json['religion_id']),
      typeBloodId: _parseString(json['type__blood_id']),
      classroomId: _parseString(json['classroom_id']),
      busId: _parseString(json['bus_id']),
      address: _parseString(json['address']),
      cityName: _parseString(json['city_name']),
      status: _parseString(json['status']),
      tripType: _parseString(json['trip_type']),
      attendanceType: _parseString(json['attendence_type']),
      latitude: _parseString(json['latitude']),
      longitude: _parseString(json['longitude']),
    );
  }
}

/// Helper methods for safe parsing
/// Helper method to safely parse string values
String? _parseString(dynamic value) {
  if (value == null) return null;
  if (value is String) return value.isEmpty ? null : value;
  return value.toString();
}

/// Helper method to safely parse integer values
int? _parseId(dynamic value) {
  if (value == null) return null;
  if (value is int) return value;
  if (value is String) {
    return int.tryParse(value);
  }
  return null;
}

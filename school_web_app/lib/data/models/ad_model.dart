import '../../domain/entities/ad.dart';

/// AdModel class for Ad entity
/// Following Single Responsibility Principle by focusing only on ad data
class AdModel extends Ad {
  AdModel({
    required super.id,
    required super.title,
    required super.description,
    required super.imageUrl,
    required super.link,
    required super.startDate,
    required super.endDate,
    required super.isActive,
  });

  /// Create AdModel from JSON
  factory AdModel.fromJson(Map<String, dynamic> json) {
    return AdModel(
      id: _parseId(json['id']) ?? 0,
      title: _parseString(json['title']) ?? '',
      description: _parseString(json['description']) ?? '',
      imageUrl: _parseString(json['image_url']) ?? '',
      link: _parseString(json['link']) ?? '',
      startDate: _parseDateTime(json['start_date']) ?? DateTime.now(),
      endDate: _parseDateTime(json['end_date']) ?? DateTime.now(),
      isActive: _parseBool(json['is_active']) ?? false,
    );
  }

  /// Convert AdModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'image_url': imageUrl,
      'link': link,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'is_active': isActive ? 1 : 0,
    };
  }
}

/// Helper methods for safe parsing
/// Helper method to safely parse string values
String? _parseString(dynamic value) {
  if (value == null) return null;
  if (value is String) return value.isEmpty ? null : value;
  return value.toString();
}

/// Helper method to safely parse integer values
int? _parseId(dynamic value) {
  if (value == null) return null;
  if (value is int) return value;
  if (value is String) {
    return int.tryParse(value);
  }
  return null;
}

/// Helper method to safely parse DateTime values
DateTime? _parseDateTime(dynamic value) {
  if (value == null) return null;
  if (value is String) {
    return DateTime.tryParse(value);
  }
  return null;
}

/// Helper method to safely parse boolean values
bool? _parseBool(dynamic value) {
  if (value == null) return null;
  if (value is bool) return value;
  if (value is int) return value == 1;
  if (value is String) {
    return value.toLowerCase() == 'true' || value == '1';
  }
  return null;
}

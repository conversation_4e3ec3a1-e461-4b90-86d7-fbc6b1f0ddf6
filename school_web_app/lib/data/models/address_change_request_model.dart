import '../../domain/entities/address_change_request.dart';

/// Model class for address change requests
class AddressChangeRequestModel extends AddressChangeRequest {
  const AddressChangeRequestModel({
    required super.id,
    super.oldAddress,
    super.oldLatitude,
    super.oldLongitude,
    super.address,
    super.longitude,
    super.latitude,
    super.parentName,
    required super.schoolName,
    required super.studentName,
    required super.gradeName,
    required super.busName,
    required super.status,
    super.statusText,
    super.parentId,
    super.schoolId,
    super.busId,
    super.createdAt,
    super.updatedAt,
  });

  /// Create AddressChangeRequestModel from JSON
  factory AddressChangeRequestModel.fromJson(Map<String, dynamic> json) {
    return AddressChangeRequestModel(
      id: _parseId(json['id']) ?? 0,
      oldAddress: _parseString(json['old_address']),
      oldLatitude: _parseString(json['old_latitude']),
      oldLongitude: _parseString(json['old_longitude']),
      address: _parseString(json['address']),
      longitude: _parseString(json['longitude']),
      latitude: _parseString(json['latitude']),
      parentName: _parseString(json['parent']?['name']),
      schoolName: _parseString(json['schools']?['name']) ?? '',
      studentName: _parseString(json['students']?['name']) ?? '',
      gradeName: _parseString(json['grade']?['name']) ?? '',
      busName: _parseString(json['bus']?['name']) ?? '',
      status: _parseId(json['status']) ?? 0,
      statusText:
          json['status_text'] != null
              ? StatusTextModel.fromJson(json['status_text'])
              : null,
      parentId: _parseId(json['my__parent_id']),
      schoolId: _parseId(json['school_id']),
      busId: _parseId(json['bus_id']),
      createdAt: _parseDateTime(json['created_at']),
      updatedAt: _parseDateTime(json['updated_at']),
    );
  }

  /// Convert AddressChangeRequestModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'old_address': oldAddress,
      'old_latitude': oldLatitude,
      'old_longitude': oldLongitude,
      'address': address,
      'longitude': longitude,
      'latitude': latitude,
      'my__parent_id': parentId,
      'school_id': schoolId,
      'bus_id': busId,
      'status': status,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  AddressChangeRequestModel copyWith({
    int? id,
    String? oldAddress,
    String? oldLatitude,
    String? oldLongitude,
    String? address,
    String? longitude,
    String? latitude,
    String? parentName,
    String? schoolName,
    String? studentName,
    String? gradeName,
    String? busName,
    int? status,
    StatusText? statusText,
    int? parentId,
    int? schoolId,
    int? busId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AddressChangeRequestModel(
      id: id ?? this.id,
      oldAddress: oldAddress ?? this.oldAddress,
      oldLatitude: oldLatitude ?? this.oldLatitude,
      oldLongitude: oldLongitude ?? this.oldLongitude,
      address: address ?? this.address,
      longitude: longitude ?? this.longitude,
      latitude: latitude ?? this.latitude,
      parentName: parentName ?? this.parentName,
      schoolName: schoolName ?? this.schoolName,
      studentName: studentName ?? this.studentName,
      gradeName: gradeName ?? this.gradeName,
      busName: busName ?? this.busName,
      status: status ?? this.status,
      statusText: statusText ?? this.statusText,
      parentId: parentId ?? this.parentId,
      schoolId: schoolId ?? this.schoolId,
      busId: busId ?? this.busId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// Model class for status text
class StatusTextModel extends StatusText {
  const StatusTextModel({super.text, super.color});

  /// Create StatusTextModel from JSON
  factory StatusTextModel.fromJson(Map<String, dynamic> json) {
    return StatusTextModel(
      text: _parseString(json['text']),
      color: _parseString(json['color']),
    );
  }

  /// Convert StatusTextModel to JSON
  Map<String, dynamic> toJson() {
    return {'text': text, 'color': color};
  }
}

/// Helper methods for safe parsing
/// Helper method to safely parse string values
String? _parseString(dynamic value) {
  if (value == null) return null;
  if (value is String) return value.isEmpty ? null : value;
  return value.toString();
}

/// Helper method to safely parse integer values
int? _parseId(dynamic value) {
  if (value == null) return null;
  if (value is int) return value;
  if (value is String) {
    return int.tryParse(value);
  }
  return null;
}

/// Helper method to safely parse DateTime values
DateTime? _parseDateTime(dynamic value) {
  if (value == null) return null;
  if (value is String) {
    return DateTime.tryParse(value);
  }
  return null;
}

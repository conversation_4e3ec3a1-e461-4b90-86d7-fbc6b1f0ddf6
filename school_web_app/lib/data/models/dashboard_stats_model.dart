import '../../domain/entities/dashboard_stats.dart';

/// DashboardStatsModel model
/// Following Single Responsibility Principle by focusing only on dashboard stats data
class DashboardStatsModel extends DashboardStats {
  const DashboardStatsModel({
    required super.totalStudents,
    required super.totalBuses,
    required super.totalDrivers,
    required super.totalSupervisors,
    required super.totalTrips,
    required super.completedTrips,
    required super.cancelledTrips,
    required super.inProgressTrips,
    required super.upcomingTrips,
    required super.attendanceRate,
    required super.onTimeRate,
  });

  /// Create DashboardStatsModel from JSON
  factory DashboardStatsModel.fromJson(Map<String, dynamic> json) {
    return DashboardStatsModel(
      totalStudents: json['total_students'] ?? 0,
      totalBuses: json['total_buses'] ?? 0,
      totalDrivers: json['total_drivers'] ?? 0,
      totalSupervisors: json['total_supervisors'] ?? 0,
      totalTrips: json['total_trips'] ?? 0,
      completedTrips: json['completed_trips'] ?? 0,
      cancelledTrips: json['cancelled_trips'] ?? 0,
      inProgressTrips: json['in_progress_trips'] ?? 0,
      upcomingTrips: json['upcoming_trips'] ?? 0,
      attendanceRate: (json['attendance_rate'] ?? 0).toDouble(),
      onTimeRate: (json['on_time_rate'] ?? 0).toDouble(),
    );
  }

  /// Convert DashboardStatsModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'total_students': totalStudents,
      'total_buses': totalBuses,
      'total_drivers': totalDrivers,
      'total_supervisors': totalSupervisors,
      'total_trips': totalTrips,
      'completed_trips': completedTrips,
      'cancelled_trips': cancelledTrips,
      'in_progress_trips': inProgressTrips,
      'upcoming_trips': upcomingTrips,
      'attendance_rate': attendanceRate,
      'on_time_rate': onTimeRate,
    };
  }
}

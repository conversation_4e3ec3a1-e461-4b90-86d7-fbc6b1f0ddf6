import '../../domain/entities/driver.dart';

/// DriverModel model
/// Following Single Responsibility Principle by focusing only on driver data
class DriverModel extends Driver {
  const DriverModel({
    super.id,
    super.email,
    super.username,
    super.name,
    super.genderId,
    super.schoolId,
    super.religionId,
    super.typeBloodId,
    super.joiningDate,
    super.address,
    super.busId,
    super.cityName,
    super.status,
    super.logo,
    super.type,
    super.phone,
    super.birthDate,
    super.emailVerifiedAt,
    super.deletedAt,
    super.createdAt,
    super.updatedAt,
    super.typeAuth,
    super.logoPath,
    super.schoolName,
    super.genderName,
    super.religionName,
    super.typeBloodName,
    super.busName,
    super.busCarNumber,
  });

  /// Create DriverModel from JSON (matching original SchoolX format)
  factory DriverModel.fromJson(Map<String, dynamic> json) {
    return DriverModel(
      id: json['id'],
      email: json['email'],
      username: json['username'],
      name: json['name'],
      genderId: json['gender_id'],
      schoolId: json['school_id'],
      religionId: json['religion_id'],
      typeBloodId: json['type__blood_id'],
      joiningDate: json['Joining_Date'],
      address: json['address'],
      busId: json['bus_id'],
      cityName: json['city_name'],
      status: json['status'],
      logo: json['logo'],
      type: json['type'],
      phone: json['phone'],
      birthDate: json['birth_date'],
      emailVerifiedAt: json['email_verified_at'],
      deletedAt: json['deleted_at'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      typeAuth: json['typeAuth'],
      logoPath: json['logo_path'],
      // Related data
      schoolName: json['schools']?['name'],
      genderName: json['gender']?['name'],
      religionName: json['religion']?['name'],
      typeBloodName: json['type_blood']?['name'],
      busName: json['bus']?['name'],
      busCarNumber: json['bus']?['car_number'],
    );
  }

  /// Convert DriverModel to JSON (matching original SchoolX format)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'username': username,
      'name': name,
      'gender_id': genderId,
      'school_id': schoolId,
      'religion_id': religionId,
      'type__blood_id': typeBloodId,
      'Joining_Date': joiningDate,
      'address': address,
      'bus_id': busId,
      'city_name': cityName,
      'status': status,
      'logo': logo,
      'type': type,
      'phone': phone,
      'birth_date': birthDate,
      'email_verified_at': emailVerifiedAt,
      'deleted_at': deletedAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'typeAuth': typeAuth,
      'logo_path': logoPath,
    };
  }

  /// Create DriverModel for API requests (matching original SchoolX format)
  Map<String, dynamic> toCreateJson() {
    return {
      if (name != null && name!.isNotEmpty) 'name': name,
      if (username != null && username!.isNotEmpty) 'username': username,
      if (joiningDate != null && joiningDate!.isNotEmpty) 'Joining_Date': joiningDate,
      if (birthDate != null && birthDate!.isNotEmpty) 'birth_date': birthDate,
      if (genderId != null && genderId != 0) 'gender_id': genderId,
      if (religionId != null && religionId != 0) 'religion_id': religionId,
      if (typeBloodId != null && typeBloodId != 0) 'type__blood_id': typeBloodId,
      if (busId != null) 'bus_id': busId == 0 ? null : busId,
      if (address != null && address!.isNotEmpty) 'address': address,
      if (cityName != null && cityName!.isNotEmpty) 'city_name': cityName,
      if (phone != null && phone!.isNotEmpty) 'phone': phone,
    };
  }

  /// Create DriverModel for update API requests (matching original SchoolX format)
  Map<String, dynamic> toUpdateJson() {
    return {
      if (name != null && name!.isNotEmpty) 'name': name,
      if (username != null && username!.isNotEmpty) 'username': username,
      if (joiningDate != null && joiningDate!.isNotEmpty) 'Joining_Date': joiningDate,
      if (birthDate != null && birthDate!.isNotEmpty) 'birth_date': birthDate,
      if (genderId != null && genderId != 0) 'gender_id': genderId,
      if (religionId != null && religionId != 0) 'religion_id': religionId,
      if (typeBloodId != null && typeBloodId != 0) 'type__blood_id': typeBloodId,
      if (busId != null) 'bus_id': busId == 0 ? null : busId,
      if (address != null && address!.isNotEmpty) 'address': address,
      if (cityName != null && cityName!.isNotEmpty) 'city_name': cityName,
      if (phone != null && phone!.isNotEmpty) 'phone': phone,
    };
  }

  @override
  DriverModel copyWith({
    int? id,
    String? email,
    String? username,
    String? name,
    int? genderId,
    int? schoolId,
    int? religionId,
    int? typeBloodId,
    String? joiningDate,
    String? address,
    int? busId,
    String? cityName,
    int? status,
    String? logo,
    String? type,
    String? phone,
    String? birthDate,
    String? emailVerifiedAt,
    String? deletedAt,
    String? createdAt,
    String? updatedAt,
    String? typeAuth,
    String? logoPath,
    String? schoolName,
    String? genderName,
    String? religionName,
    String? typeBloodName,
    String? busName,
    String? busCarNumber,
  }) {
    return DriverModel(
      id: id ?? this.id,
      email: email ?? this.email,
      username: username ?? this.username,
      name: name ?? this.name,
      genderId: genderId ?? this.genderId,
      schoolId: schoolId ?? this.schoolId,
      religionId: religionId ?? this.religionId,
      typeBloodId: typeBloodId ?? this.typeBloodId,
      joiningDate: joiningDate ?? this.joiningDate,
      address: address ?? this.address,
      busId: busId ?? this.busId,
      cityName: cityName ?? this.cityName,
      status: status ?? this.status,
      logo: logo ?? this.logo,
      type: type ?? this.type,
      phone: phone ?? this.phone,
      birthDate: birthDate ?? this.birthDate,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      typeAuth: typeAuth ?? this.typeAuth,
      logoPath: logoPath ?? this.logoPath,
      schoolName: schoolName ?? this.schoolName,
      genderName: genderName ?? this.genderName,
      religionName: religionName ?? this.religionName,
      typeBloodName: typeBloodName ?? this.typeBloodName,
      busName: busName ?? this.busName,
      busCarNumber: busCarNumber ?? this.busCarNumber,
    );
  }
}

import 'dart:convert';
import '../../domain/entities/notification.dart';

/// Model class for notifications
class NotificationModel extends NotificationEntity {
  const NotificationModel({
    super.id,
    super.title,
    super.body,
    super.type,
    super.data,
    super.image,
    super.isRead,
    super.createdAt,
    super.updatedAt,
    super.notificationData,
  });

  /// Create NotificationModel from JSON
  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] as int?,
      title: json['title'] as String?,
      body: json['body'] as String?,
      type: json['type'] as String?,
      data: json['data'] as String?,
      image: json['image'] as String?,
      isRead: json['is_read'] == 1 || json['is_read'] == true,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      notificationData: json['data'] != null
          ? NotificationDataModel.fromJson(
              json['data'] is String
                  ? jsonDecode(json['data'])
                  : json['data'],
            )
          : null,
    );
  }

  /// Convert NotificationModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type,
      'data': data,
      'image': image,
      'is_read': isRead == true ? 1 : 0,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  /// Create NotificationModel from FCM message
  factory NotificationModel.fromFCMMessage(Map<String, dynamic> message) {
    final notification = message['notification'] as Map<String, dynamic>?;
    final data = message['data'] as Map<String, dynamic>?;

    return NotificationModel(
      title: notification?['title'] as String?,
      body: notification?['body'] as String?,
      image: notification?['image'] as String?,
      type: data?['type'] as String?,
      data: data != null ? jsonEncode(data) : null,
      isRead: false,
      createdAt: DateTime.now().toIso8601String(),
      notificationData: data != null
          ? NotificationDataModel.fromJson(data)
          : null,
    );
  }
}

/// Notification data model
class NotificationDataModel extends NotificationData {
  const NotificationDataModel({
    super.tripId,
    super.studentId,
    super.busId,
    super.driverId,
    super.route,
    super.additionalData,
  });

  factory NotificationDataModel.fromJson(Map<String, dynamic> json) {
    return NotificationDataModel(
      tripId: json['trip_id'] as String?,
      studentId: json['student_id'] as String?,
      busId: json['bus_id'] as String?,
      driverId: json['driver_id'] as String?,
      route: json['route'] as String?,
      additionalData: json['additional_data'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'trip_id': tripId,
      'student_id': studentId,
      'bus_id': busId,
      'driver_id': driverId,
      'route': route,
      'additional_data': additionalData,
    };
  }
}

/// FCM Token model
class FCMTokenModel extends FCMToken {
  const FCMTokenModel({
    super.id,
    super.token,
    super.deviceType,
    super.deviceId,
    super.userId,
    super.isActive,
    super.createdAt,
    super.updatedAt,
  });

  factory FCMTokenModel.fromJson(Map<String, dynamic> json) {
    return FCMTokenModel(
      id: json['id'] as int?,
      token: json['token'] as String?,
      deviceType: json['device_type'] as String?,
      deviceId: json['device_id'] as String?,
      userId: json['user_id'] as String?,
      isActive: json['is_active'] == 1 || json['is_active'] == true,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'token': token,
      'device_type': deviceType,
      'device_id': deviceId,
      'user_id': userId,
      'is_active': isActive == true ? 1 : 0,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}

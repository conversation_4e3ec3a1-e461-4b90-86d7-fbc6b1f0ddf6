import '../../domain/entities/parent.dart';

/// Parent model for data layer
/// Following Clean Architecture principles and matching SchoolX structure
class ParentModel extends Parent {
  const ParentModel({
    super.id,
    super.email,
    super.username,
    super.name,
    super.genderId,
    super.schoolId,
    super.religionId,
    super.joiningDate,
    super.address,
    super.cityName,
    super.status,
    super.logo,
    super.type,
    super.phone,
    super.birthDate,
    super.nationalId,
    super.job,
    super.emailVerifiedAt,
    super.deletedAt,
    super.createdAt,
    super.updatedAt,
    super.typeAuth,
    super.logoPath,
    super.schoolName,
    super.genderName,
    super.religionName,
    super.studentIds,
    super.parentKey,
    super.parentSecret,
  });

  /// Create ParentModel from JSON - matching original SchoolX structure
  factory ParentModel.fromJson(Map<String, dynamic> json) {
    return ParentModel(
      id: json['id'] as int?,
      email: json['email'] as String?,
      name: json['name'] as String?,
      phone: json['phone'] as String?,
      address: json['address'] as String?,
      status: json['status'] as int?,
      logo: json['logo'] as String?,
      typeAuth: json['typeAuth'] as String?,
      emailVerifiedAt: json['email_verified_at'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      logoPath: json['logo_path'] as String?,
      // Additional fields that might be present
      genderId: json['gender_id'] as int?,
      schoolId: json['school_id'] as int?,
      religionId: json['religion_id'] as int?,
      cityName: json['city_name'] as String?,
      type: json['type'] as String?,
      birthDate: json['birth_date'] as String?,
      nationalId: json['national_id'] as String?,
      job: json['job'] as String?,
      deletedAt: json['deleted_at'] as String?,
      joiningDate: json['joining_date'] as String?,
      username: json['username'] as String?,
      studentIds:
          json['student_ids'] != null
              ? List<int>.from(json['student_ids'])
              : null,
      parentKey: json['parent_key'] as String?,
      parentSecret: json['parent_secret'] as String?,
      // Related data
      schoolName: json['schools']?['name'] as String?,
      genderName: json['gender']?['name'] as String?,
      religionName: json['religion']?['name'] as String?,
    );
  }

  /// Convert ParentModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'username': username,
      'name': name,
      'gender_id': genderId,
      'school_id': schoolId,
      'religion_id': religionId,
      'joining_date': joiningDate,
      'address': address,
      'city_name': cityName,
      'status': status,
      'logo': logo,
      'type': type,
      'phone': phone,
      'birth_date': birthDate,
      'national_id': nationalId,
      'job': job,
      'email_verified_at': emailVerifiedAt,
      'deleted_at': deletedAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'typeAuth': typeAuth,
      'logo_path': logoPath,
      'student_ids': studentIds,
      'parent_key': parentKey,
      'parent_secret': parentSecret,
    };
  }

  /// Create ParentModel from Parent entity
  factory ParentModel.fromEntity(Parent parent) {
    return ParentModel(
      id: parent.id,
      email: parent.email,
      username: parent.username,
      name: parent.name,
      genderId: parent.genderId,
      schoolId: parent.schoolId,
      religionId: parent.religionId,
      joiningDate: parent.joiningDate,
      address: parent.address,
      cityName: parent.cityName,
      status: parent.status,
      logo: parent.logo,
      type: parent.type,
      phone: parent.phone,
      birthDate: parent.birthDate,
      nationalId: parent.nationalId,
      job: parent.job,
      emailVerifiedAt: parent.emailVerifiedAt,
      deletedAt: parent.deletedAt,
      createdAt: parent.createdAt,
      updatedAt: parent.updatedAt,
      typeAuth: parent.typeAuth,
      logoPath: parent.logoPath,
      schoolName: parent.schoolName,
      genderName: parent.genderName,
      religionName: parent.religionName,
      studentIds: parent.studentIds,
      parentKey: parent.parentKey,
      parentSecret: parent.parentSecret,
    );
  }

  /// Convert to Parent entity
  Parent toEntity() {
    return Parent(
      id: id,
      email: email,
      username: username,
      name: name,
      genderId: genderId,
      schoolId: schoolId,
      religionId: religionId,
      joiningDate: joiningDate,
      address: address,
      cityName: cityName,
      status: status,
      logo: logo,
      type: type,
      phone: phone,
      birthDate: birthDate,
      nationalId: nationalId,
      job: job,
      emailVerifiedAt: emailVerifiedAt,
      deletedAt: deletedAt,
      createdAt: createdAt,
      updatedAt: updatedAt,
      typeAuth: typeAuth,
      logoPath: logoPath,
      schoolName: schoolName,
      genderName: genderName,
      religionName: religionName,
      studentIds: studentIds,
      parentKey: parentKey,
      parentSecret: parentSecret,
    );
  }
}

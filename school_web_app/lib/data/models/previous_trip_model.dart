import 'package:equatable/equatable.dart';

/// Bus information model for previous trips
class BusInfo extends Equatable {
  final int? id;
  final String? name;

  const BusInfo({this.id, this.name});

  factory BusInfo.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse integers
    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) {
        return int.tryParse(value);
      }
      if (value is double) return value.toInt();
      return null;
    }

    return BusInfo(id: parseInt(json['id']), name: json['name']);
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name};
  }

  @override
  List<Object?> get props => [id, name];
}

/// Attendant model for previous trips
class Attendant extends Equatable {
  final int? id;
  final String? type;
  final String? name;
  final String? logoPath;
  final AttendantPivot? pivot;

  const Attendant({this.id, this.type, this.name, this.logoPath, this.pivot});

  factory Attendant.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse integers
    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) {
        return int.tryParse(value);
      }
      if (value is double) return value.toInt();
      return null;
    }

    return Attendant(
      id: parseInt(json['id']),
      type: json['type'],
      name: json['name'],
      logoPath: json['logo_path'],
      pivot:
          json['pivot'] != null ? AttendantPivot.fromJson(json['pivot']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'name': name,
      'logo_path': logoPath,
      'pivot': pivot?.toJson(),
    };
  }

  @override
  List<Object?> get props => [id, type, name, logoPath, pivot];
}

/// Attendant pivot model for previous trips
class AttendantPivot extends Equatable {
  final int? tripId;
  final int? attendantId;

  const AttendantPivot({this.tripId, this.attendantId});

  factory AttendantPivot.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse integers
    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) {
        return int.tryParse(value);
      }
      if (value is double) return value.toInt();
      return null;
    }

    return AttendantPivot(
      tripId: parseInt(json['trip_id']),
      attendantId: parseInt(json['attendant_id']),
    );
  }

  Map<String, dynamic> toJson() {
    return {'trip_id': tripId, 'attendant_id': attendantId};
  }

  @override
  List<Object?> get props => [tripId, attendantId];
}

/// Route point model for previous trips
class RoutePoint extends Equatable {
  final int? id;
  final int? tripId;
  final String? latitude;
  final String? longitude;
  final String? createdAt;
  final String? updatedAt;

  const RoutePoint({
    this.id,
    this.tripId,
    this.latitude,
    this.longitude,
    this.createdAt,
    this.updatedAt,
  });

  factory RoutePoint.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse integers
    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) {
        return int.tryParse(value);
      }
      if (value is double) return value.toInt();
      return null;
    }

    return RoutePoint(
      id: parseInt(json['id']),
      tripId: parseInt(json['trip_id']),
      latitude: json['latitude'],
      longitude: json['longitude'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trip_id': tripId,
      'latitude': latitude,
      'longitude': longitude,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  List<Object?> get props => [
    id,
    tripId,
    latitude,
    longitude,
    createdAt,
    updatedAt,
  ];
}

/// Previous trip model
class PreviousTripModel extends Equatable {
  final int? id;
  final int? schoolId;
  final int? busId;
  final String? tripsDate;
  final String? tripType;
  final int? status;
  final String? latitude;
  final String? longitude;
  final String? attendanceType;
  final String? endAt;
  final String? createdAt;
  final String? updatedAt;
  final BusInfo? bus;
  final List<Attendant>? attendants;
  final List<RoutePoint>? routes;

  // Student attendance data
  final List<dynamic>? presentStudents;
  final List<dynamic>? absentStudents;

  // UI display fields (may need to be calculated from API data)
  final String? busName;
  final String? startTime;
  final String? endTime;
  final String? supervisorName;
  final String? date;
  final int? supervisorId;

  const PreviousTripModel({
    this.id,
    this.schoolId,
    this.busId,
    this.tripsDate,
    this.tripType,
    this.status,
    this.latitude,
    this.longitude,
    this.attendanceType,
    this.endAt,
    this.createdAt,
    this.updatedAt,
    this.bus,
    this.attendants,
    this.routes,
    this.presentStudents,
    this.absentStudents,
    this.busName,
    this.startTime,
    this.endTime,
    this.supervisorName,
    this.date,
    this.supervisorId,
  });

  factory PreviousTripModel.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse integers
    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) {
        return int.tryParse(value);
      }
      if (value is double) return value.toInt();
      return null;
    }

    // Parse routes if available
    List<RoutePoint>? routesList;
    if (json['routes'] != null) {
      routesList =
          (json['routes'] as List)
              .map((routeJson) => RoutePoint.fromJson(routeJson))
              .toList();
    }

    // Parse attendants if available
    List<Attendant>? attendantsList;
    if (json['attendants'] != null) {
      attendantsList =
          (json['attendants'] as List)
              .map((attendantJson) => Attendant.fromJson(attendantJson))
              .toList();
    }

    // Parse bus info if available
    BusInfo? busInfo;
    if (json['bus'] != null) {
      busInfo = BusInfo.fromJson(json['bus']);
    }

    // Extract supervisor information from attendants
    String? supervisorName;
    int? supervisorId;
    if (attendantsList != null) {
      for (var attendant in attendantsList) {
        if (attendant.type == 'admins') {
          supervisorName = attendant.name;
          supervisorId = attendant.id;
          break;
        }
      }
    }

    // Calculate display fields
    String? startTimeValue;
    String? endTimeValue;
    String? dateValue;

    if (json['trips_date'] != null) {
      try {
        final dateTime = DateTime.parse(json['trips_date']);
        dateValue = "${dateTime.day}/${dateTime.month}/${dateTime.year}";
        startTimeValue =
            "${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}";
      } catch (e) {
        dateValue = json['trips_date'];
      }
    }

    if (json['end_at'] != null) {
      try {
        final endDateTime = DateTime.parse(json['end_at']);
        endTimeValue =
            "${endDateTime.hour.toString().padLeft(2, '0')}:${endDateTime.minute.toString().padLeft(2, '0')}";
      } catch (e) {
        endTimeValue = json['end_at'];
      }
    }

    return PreviousTripModel(
      id: parseInt(json['id']),
      schoolId: parseInt(json['school_id']),
      busId: parseInt(json['bus_id']),
      tripsDate: json['trips_date'],
      tripType: json['trip_type'],
      status:
          json['status'] is bool
              ? (json['status'] as bool ? 1 : 0)
              : parseInt(json['status']),
      latitude: json['latitude']?.toString(),
      longitude: json['longitude']?.toString(),
      attendanceType: json['attendance_type'],
      endAt: json['end_at'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      bus: busInfo,
      attendants: attendantsList,
      routes: routesList,
      presentStudents: json['present_students'],
      absentStudents: json['absent_students'],

      // UI display fields
      // Generate bus name from bus object or bus_id if not available
      busName:
          busInfo?.name ??
          json['bus_name'] ??
          "Bus ${json['bus_id'] ?? 'Unknown'}",
      startTime: startTimeValue,
      endTime: endTimeValue,
      supervisorName: supervisorName ?? json['supervisor_name'] ?? "Supervisor",
      date: dateValue,
      supervisorId: supervisorId ?? parseInt(json['supervisor_id']),
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'id': id,
      'school_id': schoolId,
      'bus_id': busId,
      'trips_date': tripsDate,
      'trip_type': tripType,
      'status': status,
      'latitude': latitude,
      'longitude': longitude,
      'attendance_type': attendanceType,
      'end_at': endAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };

    // Add optional fields only if they exist
    if (bus != null) {
      data['bus'] = bus!.toJson();
    }
    if (attendants != null) {
      data['attendants'] =
          attendants!.map((attendant) => attendant.toJson()).toList();
    }
    if (routes != null) {
      data['routes'] = routes!.map((route) => route.toJson()).toList();
    }
    if (presentStudents != null) {
      data['present_students'] = presentStudents;
    }
    if (absentStudents != null) {
      data['absent_students'] = absentStudents;
    }
    if (busName != null) {
      data['bus_name'] = busName;
    }
    if (startTime != null) {
      data['start_time'] = startTime;
    }
    if (endTime != null) {
      data['end_time'] = endTime;
    }
    if (supervisorName != null) {
      data['supervisor_name'] = supervisorName;
    }
    if (date != null) {
      data['date'] = date;
    }
    if (supervisorId != null) {
      data['supervisor_id'] = supervisorId;
    }

    return data;
  }

  @override
  List<Object?> get props => [
    id,
    schoolId,
    busId,
    tripsDate,
    tripType,
    status,
    latitude,
    longitude,
    attendanceType,
    endAt,
    createdAt,
    updatedAt,
    bus,
    attendants,
    routes,
    presentStudents,
    absentStudents,
    busName,
    startTime,
    endTime,
    supervisorName,
    date,
    supervisorId,
  ];
}

/// Previous trips response model
// ignore: must_be_immutable
class PreviousTripsResponse extends Equatable {
  final bool? status;
  final String? message;
  final bool? errors;
  List<PreviousTripModel>? data; // Removed 'final' to allow modification

  PreviousTripsResponse({this.status, this.message, this.errors, this.data});

  factory PreviousTripsResponse.fromJson(Map<String, dynamic> json) {
    // Check for the new API response format with 'previous_trips'
    if (json.containsKey('previous_trips')) {
      final previousTrips = json['previous_trips'];
      List<PreviousTripModel>? tripsList;

      if (previousTrips is List) {
        // Direct list format
        tripsList =
            previousTrips
                .map(
                  (e) => PreviousTripModel.fromJson(e as Map<String, dynamic>),
                )
                .toList();
      } else if (previousTrips is Map && previousTrips['data'] is List) {
        // Nested data format
        tripsList =
            (previousTrips['data'] as List)
                .map(
                  (e) => PreviousTripModel.fromJson(e as Map<String, dynamic>),
                )
                .toList();
      }

      return PreviousTripsResponse(
        status: json['errors'] == false,
        message: json['message'] as String?,
        errors: json['errors'] as bool? ?? false,
        data: tripsList,
      );
    }

    // Fallback to the old format with 'data'
    return PreviousTripsResponse(
      status: json['status'] is bool ? json['status'] as bool? : null,
      message: json['message'] as String?,
      errors: json['errors'] as bool? ?? false,
      data:
          (json['data'] as List<dynamic>?)
              ?.map(
                (e) => PreviousTripModel.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'errors': errors,
      'data': data?.map((trip) => trip.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [status, message, errors, data];
}

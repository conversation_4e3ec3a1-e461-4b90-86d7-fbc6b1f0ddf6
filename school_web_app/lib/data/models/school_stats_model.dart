import '../../domain/entities/school_stats.dart';

/// SchoolStatsModel model
/// Following Single Responsibility Principle by focusing only on school stats data
class SchoolStatsModel extends SchoolStats {
  const SchoolStatsModel({
    required super.id,
    required super.name,
    required super.studentCount,
    required super.busCount,
    required super.tripCount,
    required super.supervisorCount,
    required super.driverCount,
    required super.lastTripDate,
  });

  /// Create SchoolStatsModel from JSON
  factory SchoolStatsModel.fromJson(Map<String, dynamic> json) {
    return SchoolStatsModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      studentCount: json['student_count'] ?? 0,
      busCount: json['bus_count'] ?? 0,
      tripCount: json['trip_count'] ?? 0,
      supervisorCount: json['supervisor_count'] ?? 0,
      driverCount: json['driver_count'] ?? 0,
      lastTripDate: json['last_trip_date'] != null
          ? DateTime.parse(json['last_trip_date'])
          : DateTime.now(),
    );
  }

  /// Convert SchoolStatsModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'student_count': studentCount,
      'bus_count': busCount,
      'trip_count': tripCount,
      'supervisor_count': supervisorCount,
      'driver_count': driverCount,
      'last_trip_date': lastTripDate.toIso8601String(),
    };
  }
}

import '../../domain/entities/supervisor.dart';

/// Supervisor model for data layer
/// Following Single Responsibility Principle by focusing only on supervisor data mapping
class SupervisorModel extends Supervisor {
  const SupervisorModel({
    super.id,
    super.email,
    super.username,
    super.name,
    super.genderId,
    super.schoolId,
    super.religionId,
    super.joiningDate,
    super.address,
    super.busId,
    super.cityName,
    super.status,
    super.logo,
    super.type,
    super.phone,
    super.birthDate,
    super.emailVerifiedAt,
    super.deletedAt,
    super.createdAt,
    super.updatedAt,
    super.typeAuth,
    super.logoPath,
    super.schoolName,
    super.genderName,
    super.religionName,
    super.busName,
    super.busCarNumber,
    super.drivers,
  });

  /// Create SupervisorModel from JSON
  factory SupervisorModel.fromJson(Map<String, dynamic> json) {
    return SupervisorModel(
      id: json['id'] as int?,
      email: json['email'] as String?,
      username: json['username'] as String?,
      name: json['name'] as String?,
      genderId: json['gender_id'] as int?,
      schoolId: json['school_id'] as int?,
      religionId: json['religion_id'] as int?,
      joiningDate: json['Joining_Date'] as String?,
      address: json['address'] as String?,
      busId: json['bus_id'] as int?,
      cityName: json['city_name'] as String?,
      status: json['status'] as int?,
      logo: json['logo'] as String?,
      type: json['type'] as String?,
      phone: json['phone'] as String?,
      birthDate: json['birth_date'] as String?,
      emailVerifiedAt: json['email_verified_at'] as String?,
      deletedAt: json['deleted_at'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      typeAuth: json['typeAuth'] as String?,
      logoPath: json['logo_path'] as String?,
      drivers: json['drivers'] as String?,
      // Related data
      schoolName: json['schools']?['name'] as String?,
      genderName: json['gender']?['name'] as String?,
      religionName: json['religion']?['name'] as String?,
      busName: json['bus']?['name'] as String?,
      busCarNumber: json['bus']?['car_number'] as String?,
    );
  }

  /// Convert SupervisorModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'username': username,
      'name': name,
      'gender_id': genderId,
      'school_id': schoolId,
      'religion_id': religionId,
      'Joining_Date': joiningDate,
      'address': address,
      'bus_id': busId,
      'city_name': cityName,
      'status': status,
      'logo': logo,
      'type': type,
      'phone': phone,
      'birth_date': birthDate,
      'email_verified_at': emailVerifiedAt,
      'deleted_at': deletedAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'typeAuth': typeAuth,
      'logo_path': logoPath,
      'drivers': drivers,
    };
  }

  /// Convert to domain entity
  Supervisor toEntity() {
    return Supervisor(
      id: id,
      email: email,
      username: username,
      name: name,
      genderId: genderId,
      schoolId: schoolId,
      religionId: religionId,
      joiningDate: joiningDate,
      address: address,
      busId: busId,
      cityName: cityName,
      status: status,
      logo: logo,
      type: type,
      phone: phone,
      birthDate: birthDate,
      emailVerifiedAt: emailVerifiedAt,
      deletedAt: deletedAt,
      createdAt: createdAt,
      updatedAt: updatedAt,
      typeAuth: typeAuth,
      logoPath: logoPath,
      schoolName: schoolName,
      genderName: genderName,
      religionName: religionName,
      busName: busName,
      busCarNumber: busCarNumber,
      drivers: drivers,
    );
  }

  /// Create SupervisorModel from domain entity
  factory SupervisorModel.fromEntity(Supervisor supervisor) {
    return SupervisorModel(
      id: supervisor.id,
      email: supervisor.email,
      username: supervisor.username,
      name: supervisor.name,
      genderId: supervisor.genderId,
      schoolId: supervisor.schoolId,
      religionId: supervisor.religionId,
      joiningDate: supervisor.joiningDate,
      address: supervisor.address,
      busId: supervisor.busId,
      cityName: supervisor.cityName,
      status: supervisor.status,
      logo: supervisor.logo,
      type: supervisor.type,
      phone: supervisor.phone,
      birthDate: supervisor.birthDate,
      emailVerifiedAt: supervisor.emailVerifiedAt,
      deletedAt: supervisor.deletedAt,
      createdAt: supervisor.createdAt,
      updatedAt: supervisor.updatedAt,
      typeAuth: supervisor.typeAuth,
      logoPath: supervisor.logoPath,
      schoolName: supervisor.schoolName,
      genderName: supervisor.genderName,
      religionName: supervisor.religionName,
      busName: supervisor.busName,
      busCarNumber: supervisor.busCarNumber,
      drivers: supervisor.drivers,
    );
  }
}

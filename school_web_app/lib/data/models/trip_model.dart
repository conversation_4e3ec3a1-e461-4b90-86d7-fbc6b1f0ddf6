import '../../domain/entities/trip.dart';

/// TripModel model
/// Following Single Responsibility Principle by focusing only on trip data
class TripModel extends Trip {
  const TripModel({
    required super.id,
    required super.name,
    required super.schoolId,
    required super.schoolName,
    required super.busId,
    required super.busNumber,
    required super.driverId,
    required super.driverName,
    required super.supervisorId,
    required super.supervisorName,
    required super.startTime,
    super.endTime,
    required super.status,
    required super.studentIds,
    required super.attendedStudentIds,
    required super.startLocation,
    required super.endLocation,
    required super.distance,
    required super.duration,
    required super.stops,
    required super.events,
  });

  /// Create TripModel from JSON
  factory TripModel.fromJson(Map<String, dynamic> json) {
    return TripModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      schoolId: json['school_id'] ?? '',
      schoolName: json['school_name'] ?? '',
      busId: json['bus_id'] ?? '',
      busNumber: json['bus_number'] ?? '',
      driverId: json['driver_id'] ?? '',
      driverName: json['driver_name'] ?? '',
      supervisorId: json['supervisor_id'] ?? '',
      supervisorName: json['supervisor_name'] ?? '',
      startTime: json['start_time'] != null
          ? DateTime.parse(json['start_time'])
          : DateTime.now(),
      endTime: json['end_time'] != null
          ? DateTime.parse(json['end_time'])
          : null,
      status: json['status'] ?? 'scheduled',
      studentIds: List<String>.from(json['student_ids'] ?? []),
      attendedStudentIds: List<String>.from(json['attended_student_ids'] ?? []),
      startLocation: json['start_location'] ?? '',
      endLocation: json['end_location'] ?? '',
      distance: (json['distance'] ?? 0).toDouble(),
      duration: json['duration'] ?? 0,
      stops: json['stops'] != null
          ? List<TripStopModel>.from(
              json['stops'].map((x) => TripStopModel.fromJson(x)))
          : [],
      events: json['events'] != null
          ? List<TripEventModel>.from(
              json['events'].map((x) => TripEventModel.fromJson(x)))
          : [],
    );
  }

  /// Convert TripModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'school_id': schoolId,
      'school_name': schoolName,
      'bus_id': busId,
      'bus_number': busNumber,
      'driver_id': driverId,
      'driver_name': driverName,
      'supervisor_id': supervisorId,
      'supervisor_name': supervisorName,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'status': status,
      'student_ids': studentIds,
      'attended_student_ids': attendedStudentIds,
      'start_location': startLocation,
      'end_location': endLocation,
      'distance': distance,
      'duration': duration,
      'stops': stops.map((x) => (x as TripStopModel).toJson()).toList(),
      'events': events.map((x) => (x as TripEventModel).toJson()).toList(),
    };
  }
}

/// TripStopModel model
class TripStopModel extends TripStop {
  const TripStopModel({
    required super.id,
    required super.name,
    required super.location,
    required super.scheduledTime,
    super.actualTime,
    required super.status,
    required super.studentIds,
    required super.attendedStudentIds,
  });

  /// Create TripStopModel from JSON
  factory TripStopModel.fromJson(Map<String, dynamic> json) {
    return TripStopModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      location: json['location'] ?? '',
      scheduledTime: json['scheduled_time'] != null
          ? DateTime.parse(json['scheduled_time'])
          : DateTime.now(),
      actualTime: json['actual_time'] != null
          ? DateTime.parse(json['actual_time'])
          : null,
      status: json['status'] ?? 'scheduled',
      studentIds: List<String>.from(json['student_ids'] ?? []),
      attendedStudentIds: List<String>.from(json['attended_student_ids'] ?? []),
    );
  }

  /// Convert TripStopModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'location': location,
      'scheduled_time': scheduledTime.toIso8601String(),
      'actual_time': actualTime?.toIso8601String(),
      'status': status,
      'student_ids': studentIds,
      'attended_student_ids': attendedStudentIds,
    };
  }
}

/// TripEventModel model
class TripEventModel extends TripEvent {
  const TripEventModel({
    required super.id,
    required super.type,
    required super.description,
    required super.time,
    required super.location,
    required super.userId,
    required super.userName,
  });

  /// Create TripEventModel from JSON
  factory TripEventModel.fromJson(Map<String, dynamic> json) {
    return TripEventModel(
      id: json['id'] ?? '',
      type: json['type'] ?? '',
      description: json['description'] ?? '',
      time: json['time'] != null
          ? DateTime.parse(json['time'])
          : DateTime.now(),
      location: json['location'] ?? '',
      userId: json['user_id'] ?? '',
      userName: json['user_name'] ?? '',
    );
  }

  /// Convert TripEventModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'description': description,
      'time': time.toIso8601String(),
      'location': location,
      'user_id': userId,
      'user_name': userName,
    };
  }
}

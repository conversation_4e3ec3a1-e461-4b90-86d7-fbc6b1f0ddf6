import '../../domain/entities/trip_tracking_data.dart';

/// Trip tracking data model for API communication
/// Following Single Responsibility Principle by focusing only on tracking data mapping
class TripTrackingDataModel extends TripTrackingData {
  const TripTrackingDataModel({
    required super.tripId,
    required super.currentLatitude,
    required super.currentLongitude,
    required super.speed,
    required super.direction,
    required super.lastUpdate,
    required super.status,
    required super.currentStopIndex,
    required super.trackingHistory,
    required super.distanceTraveled,
    required super.remainingDistance,
    required super.estimatedArrivalTime,
    required super.onboardStudents,
    required super.absentStudents,
  });

  /// Create TripTrackingDataModel from JSON
  factory TripTrackingDataModel.fromJson(Map<String, dynamic> json) {
    return TripTrackingDataModel(
      tripId: json['trip_id']?.toString() ?? '',
      currentLatitude: _parseDouble(json['current_latitude']),
      currentLongitude: _parseDouble(json['current_longitude']),
      speed: _parseDouble(json['speed']),
      direction: json['direction']?.toString() ?? '',
      lastUpdate: json['last_update'] != null
          ? DateTime.parse(json['last_update'])
          : DateTime.now(),
      status: json['status']?.toString() ?? 'unknown',
      currentStopIndex: _parseInt(json['current_stop_index']),
      trackingHistory: _parseTrackingHistory(json['tracking_history']),
      distanceTraveled: _parseDouble(json['distance_traveled']),
      remainingDistance: _parseDouble(json['remaining_distance']),
      estimatedArrivalTime: _parseInt(json['estimated_arrival_time']),
      onboardStudents: _parseStringList(json['onboard_students']),
      absentStudents: _parseStringList(json['absent_students']),
    );
  }

  /// Convert TripTrackingDataModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'trip_id': tripId,
      'current_latitude': currentLatitude,
      'current_longitude': currentLongitude,
      'speed': speed,
      'direction': direction,
      'last_update': lastUpdate.toIso8601String(),
      'status': status,
      'current_stop_index': currentStopIndex,
      'tracking_history': trackingHistory.map((point) => (point as TrackingPointModel).toJson()).toList(),
      'distance_traveled': distanceTraveled,
      'remaining_distance': remainingDistance,
      'estimated_arrival_time': estimatedArrivalTime,
      'onboard_students': onboardStudents,
      'absent_students': absentStudents,
    };
  }

  // Helper methods for safe parsing
  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  static int _parseInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  static List<String> _parseStringList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((item) => item.toString()).toList();
    }
    return [];
  }

  static List<TrackingPoint> _parseTrackingHistory(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value
          .map((item) => item is Map<String, dynamic>
              ? TrackingPointModel.fromJson(item)
              : null)
          .whereType<TrackingPoint>()
          .toList();
    }
    return [];
  }
}

/// Tracking point model for API communication
class TrackingPointModel extends TrackingPoint {
  const TrackingPointModel({
    required super.latitude,
    required super.longitude,
    required super.timestamp,
    required super.speed,
    required super.status,
  });

  /// Create TrackingPointModel from JSON
  factory TrackingPointModel.fromJson(Map<String, dynamic> json) {
    return TrackingPointModel(
      latitude: TripTrackingDataModel._parseDouble(json['latitude']),
      longitude: TripTrackingDataModel._parseDouble(json['longitude']),
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'])
          : DateTime.now(),
      speed: TripTrackingDataModel._parseDouble(json['speed']),
      status: json['status']?.toString() ?? 'unknown',
    );
  }

  /// Convert TrackingPointModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'timestamp': timestamp.toIso8601String(),
      'speed': speed,
      'status': status,
    };
  }
}

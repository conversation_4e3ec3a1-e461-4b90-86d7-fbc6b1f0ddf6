import '../../domain/entities/type_blood.dart';

/// TypeBloodModel model
class TypeBloodModel extends TypeBlood {
  const TypeBloodModel({
    super.id,
    super.name,
    super.createdAt,
    super.updatedAt,
  });

  factory TypeBloodModel.fromJson(Map<String, dynamic> json) {
    return TypeBloodModel(
      id: json['id'],
      name: json['name'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}

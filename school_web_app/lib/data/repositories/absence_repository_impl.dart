import 'package:dartz/dartz.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../../core/network/api_service.dart';
import '../../domain/entities/absence_request.dart';
import '../../domain/repositories/absence_repository.dart';
import '../models/absence_request_model.dart';

/// Implementation of AbsenceRepository
class AbsenceRepositoryImpl implements AbsenceRepository {
  final ApiService _apiService;

  AbsenceRepositoryImpl(this._apiService);

  @override
  Future<Either<Failure, List<AbsenceRequest>>> getAbsenceRequests({
    int page = 1,
    int limit = 10,
    String? busId,
    String? attendanceType,
    String? studentName,
    String? date,
  }) async {
    try {
      // Build query parameters
      final queryParams = <String, dynamic>{'page': page};

      if (busId != null && busId.isNotEmpty) {
        queryParams['bus_id'] = busId;
      }

      if (attendanceType != null && attendanceType.isNotEmpty) {
        queryParams['attendence_type'] = attendanceType;
      }

      if (studentName != null && studentName.isNotEmpty) {
        queryParams['text'] = studentName;
      }

      if (date != null && date.isNotEmpty) {
        queryParams['attendence_date'] = date;
      }

      final response = await _apiService.get(
        AppConstants.attendanceEndpoint,
        queryParameters: queryParams,
        isAuth: true,
      );

      if (response.statusCode == 200) {
        final data = response.data;

        // Handle different response structures
        List<dynamic> absencesData;
        if (data['data'] != null &&
            data['data']['absences'] != null &&
            data['data']['absences']['data'] != null) {
          absencesData = data['data']['absences']['data'] as List<dynamic>;
        } else if (data['data'] != null && data['data'] is List) {
          absencesData = data['data'] as List<dynamic>;
        } else {
          absencesData = [];
        }

        final absences =
            absencesData
                .map((json) {
                  try {
                    return AbsenceRequestModel.fromJson(
                      json as Map<String, dynamic>,
                    );
                  } catch (e) {
                    // Skip invalid records instead of crashing
                    return null;
                  }
                })
                .where((element) => element != null)
                .cast<AbsenceRequest>()
                .toList();

        return Right(absences);
      } else {
        return Left(ServerFailure(message: 'Failed to load absence requests'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, AbsenceRequest>> getAbsenceRequestById(int id) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.attendanceEndpoint}/$id',
        isAuth: true,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final requestData = data['data'] ?? data;

        final request = AbsenceRequestModel.fromJson(requestData);
        return Right(request);
      } else {
        return Left(ServerFailure(message: 'Failed to load absence request'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, AbsenceRequest>> createAbsenceRequest({
    required String studentId,
    required String attendanceDate,
    required String attendanceType,
    String? notes,
  }) async {
    try {
      final requestData = {
        'student_id': studentId,
        'attendence_date': attendanceDate,
        'attendence_type': attendanceType,
        if (notes != null) 'notes': notes,
      };

      final response = await _apiService.post(
        url: AppConstants.attendanceEndpoint,
        body: requestData,
        isAuth: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = response.data;
        final requestData = data['data'] ?? data;

        final request = AbsenceRequestModel.fromJson(requestData);
        return Right(request);
      } else {
        return Left(ServerFailure(message: 'Failed to create absence request'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, AbsenceRequest>> updateAbsenceRequest({
    required int id,
    String? attendanceDate,
    String? attendanceType,
    String? notes,
  }) async {
    try {
      final requestData = <String, dynamic>{};

      if (attendanceDate != null) {
        requestData['attendence_date'] = attendanceDate;
      }
      if (attendanceType != null) {
        requestData['attendence_type'] = attendanceType;
      }
      if (notes != null) requestData['notes'] = notes;

      final response = await _apiService.put(
        url: '${AppConstants.attendanceEndpoint}/$id',
        body: requestData,
        isAuth: true,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final requestData = data['data'] ?? data;

        final request = AbsenceRequestModel.fromJson(requestData);
        return Right(request);
      } else {
        return Left(ServerFailure(message: 'Failed to update absence request'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteAbsenceRequest(int id) async {
    try {
      final response = await _apiService.delete(
        url: '${AppConstants.attendanceEndpoint}/$id',
        isAuth: true,
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        return const Right(true);
      } else {
        return Left(ServerFailure(message: 'Failed to delete absence request'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getAttendanceTypes() async {
    try {
      // This is a mock implementation as the API doesn't seem to have an endpoint for this
      // In a real implementation, you would call the API to get the attendance types
      return const Right(['حاضر', 'غائب', 'متأخر', 'مريض', 'إجازة']);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

import 'package:dartz/dartz.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../../core/network/api_service.dart';
import '../../domain/entities/address_change_request.dart';
import '../../domain/repositories/address_change_repository.dart';
import '../models/address_change_request_model.dart';

/// Implementation of AddressChangeRepository
class AddressChangeRepositoryImpl implements AddressChangeRepository {
  final ApiService _apiService;

  AddressChangeRepositoryImpl(this._apiService);

  @override
  Future<Either<Failure, List<AddressChangeRequest>>> getAddressChangeRequests({
    int page = 1,
    int limit = 10,
    String? status,
    String? search,
  }) async {
    try {
      // Build query parameters
      final queryParams = <String, dynamic>{'page': page, 'limit': limit};

      if (status != null && status.isNotEmpty) {
        queryParams['status'] = status;
      }

      if (search != null && search.isNotEmpty) {
        queryParams['text'] = search;
      }

      final response = await _apiService.get(
        AppConstants.changeAddressRequestsEndpoint,
        queryParameters: queryParams,
        isAuth: true,
      );

      if (response.statusCode == 200) {
        final data = response.data;

        // Handle different response structures
        List<dynamic> requestsData;
        if (data['data'] != null && data['data']['data'] != null) {
          requestsData = data['data']['data'] as List<dynamic>;
        } else if (data['data'] != null && data['data'] is List) {
          requestsData = data['data'] as List<dynamic>;
        } else {
          requestsData = [];
        }

        final requests =
            requestsData
                .map((json) => AddressChangeRequestModel.fromJson(json))
                .toList();

        return Right(requests);
      } else {
        return Left(
          ServerFailure(message: 'Failed to load address change requests'),
        );
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, AddressChangeRequest>> getAddressChangeRequestById(
    int id,
  ) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.changeAddressRequestsEndpoint}/$id',
        isAuth: true,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final requestData = data['data'] ?? data;

        final request = AddressChangeRequestModel.fromJson(requestData);
        return Right(request);
      } else {
        return Left(
          ServerFailure(message: 'Failed to load address change request'),
        );
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> acceptAddressChangeRequest(
    int requestId,
  ) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.acceptChangeAddressRequestsEndpoint}$requestId',
        isAuth: true,
      );

      if (response.statusCode == 200) {
        return const Right(true);
      } else {
        return Left(
          ServerFailure(message: 'Failed to accept address change request'),
        );
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> refuseAddressChangeRequest({
    required int requestId,
    required String reason,
  }) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.refuseChangeAddressRequestsEndpoint}$requestId',
        isAuth: true,
      );

      if (response.statusCode == 200) {
        return const Right(true);
      } else {
        return Left(
          ServerFailure(message: 'Failed to refuse address change request'),
        );
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<AddressChangeRequest>>> getTemporaryAddresses({
    int page = 1,
    int limit = 10,
    String? search,
  }) async {
    try {
      final queryParams = <String, dynamic>{'page': page, 'limit': limit};

      if (search != null && search.isNotEmpty) {
        queryParams['text'] = search;
      }

      final response = await _apiService.get(
        AppConstants.temporaryAddressesEndpoint,
        queryParameters: queryParams,
        isAuth: true,
      );

      if (response.statusCode == 200) {
        final data = response.data;

        List<dynamic> addressesData;
        if (data['data'] != null && data['data']['data'] != null) {
          addressesData = data['data']['data'] as List<dynamic>;
        } else if (data['data'] != null && data['data'] is List) {
          addressesData = data['data'] as List<dynamic>;
        } else {
          addressesData = [];
        }

        final addresses =
            addressesData
                .map((json) => AddressChangeRequestModel.fromJson(json))
                .toList();

        return Right(addresses);
      } else {
        return Left(
          ServerFailure(message: 'Failed to load temporary addresses'),
        );
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, AddressChangeRequest>> createAddressChangeRequest({
    required String address,
    required String latitude,
    required String longitude,
    required int studentId,
    String? notes,
  }) async {
    try {
      final requestData = {
        'address': address,
        'latitude': latitude,
        'longitude': longitude,
        'student_id': studentId,
        if (notes != null) 'notes': notes,
      };

      final response = await _apiService.post(
        url: AppConstants.changeAddressRequestsEndpoint,
        body: requestData,
        isAuth: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = response.data;
        final requestData = data['data'] ?? data;

        final request = AddressChangeRequestModel.fromJson(requestData);
        return Right(request);
      } else {
        return Left(
          ServerFailure(message: 'Failed to create address change request'),
        );
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, AddressChangeRequest>> updateAddressChangeRequest({
    required int id,
    String? address,
    String? latitude,
    String? longitude,
    String? notes,
  }) async {
    try {
      final requestData = <String, dynamic>{};

      if (address != null) requestData['address'] = address;
      if (latitude != null) requestData['latitude'] = latitude;
      if (longitude != null) requestData['longitude'] = longitude;
      if (notes != null) requestData['notes'] = notes;

      final response = await _apiService.put(
        url: '${AppConstants.changeAddressRequestsEndpoint}/$id',
        body: requestData,
        isAuth: true,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final requestData = data['data'] ?? data;

        final request = AddressChangeRequestModel.fromJson(requestData);
        return Right(request);
      } else {
        return Left(
          ServerFailure(message: 'Failed to update address change request'),
        );
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteAddressChangeRequest(int id) async {
    try {
      final response = await _apiService.delete(
        url: '${AppConstants.changeAddressRequestsEndpoint}/$id',
        isAuth: true,
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        return const Right(true);
      } else {
        return Left(
          ServerFailure(message: 'Failed to delete address change request'),
        );
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

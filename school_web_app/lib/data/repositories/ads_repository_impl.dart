import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../../core/utils/logger.dart';
import '../../core/constants/app_constants.dart';
import '../../core/network/api_service.dart';
import '../../domain/repositories/ads_repository.dart';
import '../../domain/entities/ad.dart';
import '../models/ad_model.dart';

/// Ads Repository Implementation
/// Following Repository Pattern for ads management
class AdsRepositoryImpl implements AdsRepository {
  final ApiService _apiService;

  AdsRepositoryImpl({
    required ApiService apiService,
  }) : _apiService = apiService;

  @override
  Future<Either<Failure, List<Ad>>> getAds() async {
    try {
      LoggerService.info('Getting ads from API');
      
      final response = await _apiService.get(
        AppConstants.adsEndpoint,
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to get ads';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse ads data
      final adsData = responseData['data'] as List<dynamic>?;
      if (adsData == null) {
        return const Right([]);
      }

      final ads = adsData
          .map((json) {
            try {
              return AdModel.fromJson(json as Map<String, dynamic>);
            } catch (e) {
              // Skip invalid records instead of crashing
              return null;
            }
          })
          .where((element) => element != null)
          .cast<Ad>()
          .toList();

      LoggerService.success('Successfully loaded ${ads.length} ads');
      return Right(ads);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when getting ads', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error('Unauthorized exception when getting ads', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when getting ads', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Ad>> getAdById(int id) async {
    try {
      LoggerService.info('Getting ad by ID from API', data: {'id': id});
      
      final response = await _apiService.get(
        '${AppConstants.adsEndpoint}/$id',
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to get ad';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse ad data
      final adData = responseData['data'] as Map<String, dynamic>?;
      if (adData == null) {
        return Left(ServerFailure(message: 'No ad data received'));
      }

      final ad = AdModel.fromJson(adData);

      LoggerService.success('Successfully loaded ad');
      return Right(ad);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when getting ad by ID', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error('Unauthorized exception when getting ad by ID', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when getting ad by ID', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Ad>> createAd(Map<String, dynamic> adData) async {
    try {
      LoggerService.info('Creating new ad via API');
      
      final response = await _apiService.post(
        url: AppConstants.adsEndpoint,
        body: adData,
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to create ad';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse created ad data
      final createdAdData = responseData['data'] as Map<String, dynamic>?;
      if (createdAdData == null) {
        return Left(ServerFailure(message: 'No ad data received'));
      }

      final ad = AdModel.fromJson(createdAdData);

      LoggerService.success('Successfully created ad');
      return Right(ad);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when creating ad', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error('Unauthorized exception when creating ad', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when creating ad', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Ad>> updateAd(int id, Map<String, dynamic> adData) async {
    try {
      LoggerService.info('Updating ad via API', data: {'id': id});
      
      final response = await _apiService.put(
        url: '${AppConstants.adsEndpoint}/$id',
        body: adData,
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to update ad';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse updated ad data
      final updatedAdData = responseData['data'] as Map<String, dynamic>?;
      if (updatedAdData == null) {
        return Left(ServerFailure(message: 'No ad data received'));
      }

      final ad = AdModel.fromJson(updatedAdData);

      LoggerService.success('Successfully updated ad');
      return Right(ad);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when updating ad', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error('Unauthorized exception when updating ad', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when updating ad', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteAd(int id) async {
    try {
      LoggerService.info('Deleting ad via API', data: {'id': id});
      
      final response = await _apiService.delete(
        url: '${AppConstants.adsEndpoint}/$id',
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to delete ad';
        return Left(ServerFailure(message: errorMessage));
      }

      LoggerService.success('Successfully deleted ad');
      return const Right(true);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when deleting ad', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error('Unauthorized exception when deleting ad', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when deleting ad', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../../core/utils/logger.dart';
import '../../core/constants/app_constants.dart';
import '../../core/network/api_service.dart';
import '../../domain/repositories/file_repository.dart';

/// File Repository Implementation
/// Following Repository Pattern for file management operations
class FileRepositoryImpl implements FileRepository {
  final ApiService _apiService;

  FileRepositoryImpl({required ApiService apiService})
    : _apiService = apiService;

  @override
  Future<Either<Failure, String>> downloadStudentsExampleFile() async {
    try {
      LoggerService.info('Downloading students example file');

      final response = await _apiService.get(
        AppConstants.downloadStudentsExampleFile,
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to download file';
        return Left(ServerFailure(message: errorMessage));
      }

      // Extract download URL or file content
      final downloadUrl = responseData['data']?['download_url'] as String?;
      if (downloadUrl == null || downloadUrl.isEmpty) {
        return Left(ServerFailure(message: 'No download URL received'));
      }

      LoggerService.success(
        'Successfully got download URL for students example file',
      );
      return Right(downloadUrl);
    } on ServerException catch (e) {
      LoggerService.error(
        'Server exception when downloading students example file',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when downloading students example file',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when downloading students example file',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> downloadStudentsPDFFile() async {
    try {
      LoggerService.info('Downloading students PDF file');

      final response = await _apiService.get(
        AppConstants.downloadStudentsPDFFile,
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to download PDF file';
        return Left(ServerFailure(message: errorMessage));
      }

      // Extract download URL or file content
      final downloadUrl = responseData['data']?['download_url'] as String?;
      if (downloadUrl == null || downloadUrl.isEmpty) {
        return Left(ServerFailure(message: 'No download URL received'));
      }

      LoggerService.success(
        'Successfully got download URL for students PDF file',
      );
      return Right(downloadUrl);
    } on ServerException catch (e) {
      LoggerService.error(
        'Server exception when downloading students PDF file',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when downloading students PDF file',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when downloading students PDF file',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>>
  getFileNameAndExtension() async {
    try {
      LoggerService.info('Getting file name and extension info');

      final response = await _apiService.get(
        AppConstants.fileNameAndExtension,
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to get file info';
        return Left(ServerFailure(message: errorMessage));
      }

      // Extract file info
      final fileInfo = responseData['data'] as Map<String, dynamic>?;
      if (fileInfo == null) {
        return const Right({});
      }

      LoggerService.success('Successfully got file name and extension info');
      return Right(fileInfo);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when getting file info', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when getting file info',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when getting file info', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> downloadFile(
    String url,
    String fileName,
  ) async {
    try {
      LoggerService.info(
        'Downloading file',
        data: {'url': url, 'fileName': fileName},
      );

      final response = await _apiService.get(url, isAuth: true);

      // For file downloads, the response might be different
      // This is a generic implementation that can be customized based on actual API response
      if (response.statusCode == 200) {
        LoggerService.success('Successfully downloaded file');
        return Right('File downloaded successfully');
      } else {
        return Left(ServerFailure(message: 'Failed to download file'));
      }
    } on ServerException catch (e) {
      LoggerService.error('Server exception when downloading file', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when downloading file',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when downloading file', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../../core/utils/logger.dart';
import '../../core/constants/app_constants.dart';
import '../../core/network/api_service.dart';
import '../../domain/repositories/help_repository.dart';

/// Help Repository Implementation
/// Following Repository Pattern for help/questions management
class HelpRepositoryImpl implements HelpRepository {
  final ApiService _apiService;

  HelpRepositoryImpl({required ApiService apiService})
    : _apiService = apiService;

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getHelpQuestions() async {
    try {
      LoggerService.info('Getting help questions from API');

      final response = await _apiService.get(
        AppConstants.questionHelpEndpoint,
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to get help questions';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse questions data
      final questionsData = responseData['data'] as List<dynamic>?;
      if (questionsData == null) {
        return const Right([]);
      }

      final questions =
          questionsData.map((json) => json as Map<String, dynamic>).toList();

      LoggerService.success(
        'Successfully loaded ${questions.length} help questions',
      );
      return Right(questions);
    } on ServerException catch (e) {
      LoggerService.error(
        'Server exception when getting help questions',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when getting help questions',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when getting help questions',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getHelpQuestionById(
    int id,
  ) async {
    try {
      LoggerService.info(
        'Getting help question by ID from API',
        data: {'id': id},
      );

      final response = await _apiService.get(
        '${AppConstants.questionHelpEndpoint}/$id',
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to get help question';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse question data
      final questionData = responseData['data'] as Map<String, dynamic>?;
      if (questionData == null) {
        return Left(ServerFailure(message: 'No question data received'));
      }

      LoggerService.success('Successfully loaded help question');
      return Right(questionData);
    } on ServerException catch (e) {
      LoggerService.error(
        'Server exception when getting help question by ID',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when getting help question by ID',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when getting help question by ID',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> submitHelpQuestion(
    Map<String, dynamic> questionData,
  ) async {
    try {
      LoggerService.info('Submitting help question via API');

      final response = await _apiService.post(
        url: AppConstants.questionHelpEndpoint,
        body: questionData,
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to submit help question';
        return Left(ServerFailure(message: errorMessage));
      }

      LoggerService.success('Successfully submitted help question');
      return const Right(true);
    } on ServerException catch (e) {
      LoggerService.error(
        'Server exception when submitting help question',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when submitting help question',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when submitting help question',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getFAQ() async {
    try {
      LoggerService.info('Getting FAQ from API');

      final response = await _apiService.get(
        '${AppConstants.questionHelpEndpoint}/faq',
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to get FAQ';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse FAQ data
      final faqData = responseData['data'] as List<dynamic>?;
      if (faqData == null) {
        return const Right([]);
      }

      final faq = faqData.map((json) => json as Map<String, dynamic>).toList();

      LoggerService.success('Successfully loaded ${faq.length} FAQ items');
      return Right(faq);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when getting FAQ', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error('Unauthorized exception when getting FAQ', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when getting FAQ', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

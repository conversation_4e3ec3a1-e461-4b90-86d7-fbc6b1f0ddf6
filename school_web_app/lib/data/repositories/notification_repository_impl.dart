import 'package:dartz/dartz.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../../core/network/api_service.dart';
import '../../domain/entities/notification.dart';
import '../../domain/repositories/notification_repository.dart';
import '../models/notification_model.dart';

/// Implementation of NotificationRepository
class NotificationRepositoryImpl implements NotificationRepository {
  final ApiService _apiService;

  NotificationRepositoryImpl(this._apiService);

  @override
  Future<Either<Failure, List<NotificationEntity>>> getNotifications({
    int page = 1,
    int limit = 20,
    bool? isRead,
    String? type,
  }) async {
    try {
      // Build query parameters
      final queryParams = <String, dynamic>{'page': page, 'limit': limit};

      if (isRead != null) {
        queryParams['is_read'] = isRead ? 1 : 0;
      }

      if (type != null && type.isNotEmpty) {
        queryParams['type'] = type;
      }

      final response = await _apiService.get(
        AppConstants.notificationsEndpoint,
        queryParameters: queryParams,
        isAuth: true,
      );

      if (response.statusCode == 200) {
        final data = response.data;

        // Handle different response structures
        List<dynamic> notificationsData;
        if (data['data'] != null && data['data']['data'] != null) {
          notificationsData = data['data']['data'] as List<dynamic>;
        } else if (data['data'] != null && data['data'] is List) {
          notificationsData = data['data'] as List<dynamic>;
        } else {
          notificationsData = [];
        }

        final notifications =
            notificationsData
                .map((json) => NotificationModel.fromJson(json))
                .toList();

        return Right(notifications);
      } else {
        return Left(ServerFailure(message: 'Failed to load notifications'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, NotificationEntity>> getNotificationById(
    int id,
  ) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.notificationsEndpoint}/$id',
        isAuth: true,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final notificationData = data['data'] ?? data;

        final notification = NotificationModel.fromJson(notificationData);
        return Right(notification);
      } else {
        return Left(ServerFailure(message: 'Failed to load notification'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> markAsRead(int notificationId) async {
    try {
      final response = await _apiService.put(
        url: '${AppConstants.notificationsEndpoint}/$notificationId/read',
        isAuth: true,
      );

      if (response.statusCode == 200) {
        return const Right(true);
      } else {
        return Left(
          ServerFailure(message: 'Failed to mark notification as read'),
        );
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> markAllAsRead() async {
    try {
      final response = await _apiService.put(
        url: '${AppConstants.notificationsEndpoint}/read-all',
        isAuth: true,
      );

      if (response.statusCode == 200) {
        return const Right(true);
      } else {
        return Left(
          ServerFailure(message: 'Failed to mark all notifications as read'),
        );
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteNotification(int notificationId) async {
    try {
      final response = await _apiService.delete(
        url: '${AppConstants.notificationsEndpoint}/$notificationId',
        isAuth: true,
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        return const Right(true);
      } else {
        return Left(ServerFailure(message: 'Failed to delete notification'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteAllNotifications() async {
    try {
      final response = await _apiService.delete(
        url: '${AppConstants.notificationsEndpoint}/all',
        isAuth: true,
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        return const Right(true);
      } else {
        return Left(
          ServerFailure(message: 'Failed to delete all notifications'),
        );
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, int>> getUnreadCount() async {
    try {
      final response = await _apiService.get(
        '${AppConstants.notificationsEndpoint}/unread-count',
        isAuth: true,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final count = data['data']?['count'] ?? data['count'] ?? 0;
        return Right(count as int);
      } else {
        return Left(ServerFailure(message: 'Failed to get unread count'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> registerFCMToken({
    required String token,
    required String deviceType,
    required String deviceId,
  }) async {
    try {
      final requestData = {
        'token': token,
        'device_type': deviceType,
        'device_id': deviceId,
      };

      final response = await _apiService.post(
        url: AppConstants.fcmTokensEndpoint,
        body: requestData,
        isAuth: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return const Right(true);
      } else {
        return Left(ServerFailure(message: 'Failed to register FCM token'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> updateFCMToken({
    required String oldToken,
    required String newToken,
  }) async {
    try {
      final requestData = {'old_token': oldToken, 'new_token': newToken};

      final response = await _apiService.put(
        url: '${AppConstants.fcmTokensEndpoint}/update',
        body: requestData,
        isAuth: true,
      );

      if (response.statusCode == 200) {
        return const Right(true);
      } else {
        return Left(ServerFailure(message: 'Failed to update FCM token'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> unregisterFCMToken(String token) async {
    try {
      final response = await _apiService.delete(
        url: '${AppConstants.fcmTokensEndpoint}/$token',
        isAuth: true,
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        return const Right(true);
      } else {
        return Left(ServerFailure(message: 'Failed to unregister FCM token'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<FCMToken>>> getUserFCMTokens() async {
    try {
      final response = await _apiService.get(
        AppConstants.fcmTokensEndpoint,
        isAuth: true,
      );

      if (response.statusCode == 200) {
        final data = response.data;

        List<dynamic> tokensData;
        if (data['data'] != null && data['data'] is List) {
          tokensData = data['data'] as List<dynamic>;
        } else {
          tokensData = [];
        }

        final tokens =
            tokensData.map((json) => FCMTokenModel.fromJson(json)).toList();

        return Right(tokens);
      } else {
        return Left(ServerFailure(message: 'Failed to load FCM tokens'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> sendNotification({
    required String title,
    required String body,
    String? type,
    String? image,
    Map<String, dynamic>? data,
    List<String>? userIds,
    List<String>? tokens,
  }) async {
    try {
      final requestData = {
        'title': title,
        'body': body,
        if (type != null) 'type': type,
        if (image != null) 'image': image,
        if (data != null) 'data': data,
        if (userIds != null) 'user_ids': userIds,
        if (tokens != null) 'tokens': tokens,
      };

      final response = await _apiService.post(
        url: '${AppConstants.notificationsEndpoint}/send',
        body: requestData,
        isAuth: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return const Right(true);
      } else {
        return Left(ServerFailure(message: 'Failed to send notification'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

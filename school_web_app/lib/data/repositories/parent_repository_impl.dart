import 'package:dartz/dartz.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/failures.dart';
import '../../core/network/api_service.dart';
import '../../domain/entities/parent.dart';
import '../../domain/repositories/parent_repository.dart';
import '../models/parent_model.dart';

/// Parent repository implementation
/// Following Clean Architecture principles
class ParentRepositoryImpl implements ParentRepository {
  final ApiService _apiService;

  ParentRepositoryImpl(this._apiService);

  @override
  Future<Either<Failure, List<Parent>>> getParents({
    int page = 1,
    int limit = 10,
    String? search,
  }) async {
    try {
      // Use the correct endpoint matching the original SchoolX app
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
        if (search != null && search.isNotEmpty)
          'text': search, // Use 'text' parameter like original
      };

      final response = await _apiService.get(
        AppConstants.parentEndpoint, // Use parents/index endpoint
        queryParameters: queryParams,
        isAuth: true,
      );

      final responseData = response.data;

      // Handle different response formats
      List<dynamic> parentsData;

      if (responseData is Map<String, dynamic>) {
        // Check for API errors
        if (responseData['errors'] == true || responseData['status'] == false) {
          final errorMessage =
              responseData['message'] ?? 'Failed to fetch parents';
          return Left(ServerFailure(message: errorMessage));
        }

        // Try different data structures
        if (responseData['data'] != null) {
          if (responseData['data'] is List) {
            parentsData = responseData['data'] as List<dynamic>;
          } else if (responseData['data']['data'] is List) {
            parentsData = responseData['data']['data'] as List<dynamic>;
          } else {
            parentsData = [];
          }
        } else {
          parentsData = [];
        }
      } else if (responseData is List) {
        parentsData = responseData;
      } else {
        parentsData = [];
      }

      final parents =
          parentsData
              .map((json) => ParentModel.fromJson(json as Map<String, dynamic>))
              .map((model) => model.toEntity())
              .toList();

      return Right(parents);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Parent>> getParentById(int id) async {
    try {
      // Use the parentShow endpoint from constants
      final response = await _apiService.get(
        '${AppConstants.parentShowEndpoint}/$id', // Use correct endpoint
        isAuth: true,
      );

      final responseData = response.data;

      // Handle different response formats
      Map<String, dynamic>? parentData;

      if (responseData is Map<String, dynamic>) {
        // Check for API errors
        if (responseData['errors'] == true || responseData['status'] == false) {
          final errorMessage = responseData['message'] ?? 'Parent not found';
          return Left(ServerFailure(message: errorMessage));
        }

        // Try different data structures
        if (responseData['data'] != null) {
          parentData = responseData['data'] as Map<String, dynamic>?;
        }
      }

      if (parentData == null) {
        return Left(ServerFailure(message: 'Parent not found'));
      }

      final parent = ParentModel.fromJson(parentData).toEntity();
      return Right(parent);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Parent>> createParent(Parent parent) async {
    try {
      final parentModel = ParentModel.fromEntity(parent);
      final response = await _apiService.post(
        url: 'parents', // Simplified endpoint
        body: parentModel.toJson(),
        isAuth: true,
      );

      final responseData = response.data;

      // Handle different response formats
      Map<String, dynamic>? createdParentData;

      if (responseData is Map<String, dynamic>) {
        // Check for API errors
        if (responseData['errors'] == true || responseData['status'] == false) {
          final errorMessage =
              responseData['message'] ?? 'Failed to create parent';
          return Left(ServerFailure(message: errorMessage));
        }

        // Try different data structures
        if (responseData['data'] != null) {
          createdParentData = responseData['data'] as Map<String, dynamic>?;
        }
      }

      if (createdParentData == null) {
        return Left(ServerFailure(message: 'Failed to create parent'));
      }

      final createdParent = ParentModel.fromJson(createdParentData).toEntity();
      return Right(createdParent);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Parent>> updateParent(Parent parent) async {
    try {
      final parentModel = ParentModel.fromEntity(parent);
      final response = await _apiService.put(
        url: 'parents/${parent.id}', // Simplified endpoint
        body: parentModel.toJson(),
        isAuth: true,
      );

      final responseData = response.data;

      // Handle different response formats
      Map<String, dynamic>? updatedParentData;

      if (responseData is Map<String, dynamic>) {
        // Check for API errors
        if (responseData['errors'] == true || responseData['status'] == false) {
          final errorMessage =
              responseData['message'] ?? 'Failed to update parent';
          return Left(ServerFailure(message: errorMessage));
        }

        // Try different data structures
        if (responseData['data'] != null) {
          updatedParentData = responseData['data'] as Map<String, dynamic>?;
        }
      }

      if (updatedParentData == null) {
        return Left(ServerFailure(message: 'Failed to update parent'));
      }

      final updatedParent = ParentModel.fromJson(updatedParentData).toEntity();
      return Right(updatedParent);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteParent(int id) async {
    try {
      final response = await _apiService.delete(
        url: 'parents/$id', // Simplified endpoint
        isAuth: true,
      );

      final responseData = response.data;

      // Handle different response formats
      if (responseData is Map<String, dynamic>) {
        // Check for API errors
        if (responseData['errors'] == true || responseData['status'] == false) {
          final errorMessage =
              responseData['message'] ?? 'Failed to delete parent';
          return Left(ServerFailure(message: errorMessage));
        }
      }

      return const Right(true);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<int>>> getStudentsByParentId(int parentId) async {
    try {
      final response = await _apiService.get(
        'parents/$parentId/students', // Simplified endpoint
        isAuth: true,
      );

      final responseData = response.data;

      // Handle different response formats
      List<dynamic> studentsData;

      if (responseData is Map<String, dynamic>) {
        // Check for API errors
        if (responseData['errors'] == true || responseData['status'] == false) {
          final errorMessage =
              responseData['message'] ?? 'Failed to get students';
          return Left(ServerFailure(message: errorMessage));
        }

        // Try different data structures
        if (responseData['data'] != null) {
          if (responseData['data'] is List) {
            studentsData = responseData['data'] as List<dynamic>;
          } else {
            studentsData = [];
          }
        } else {
          studentsData = [];
        }
      } else if (responseData is List) {
        studentsData = responseData;
      } else {
        studentsData = [];
      }

      final studentIds =
          studentsData
              .map((json) => (json as Map<String, dynamic>)['id'] as int)
              .toList();

      return Right(studentIds);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>>
  getAvailableSchools() async {
    try {
      final response = await _apiService.get(
        'schools', // Simplified endpoint
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to get schools';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse schools data
      final schoolsData = responseData['data'] as List<dynamic>?;
      if (schoolsData == null) {
        return const Right([]);
      }

      final schools =
          schoolsData
              .map(
                (json) => {
                  'id': (json as Map<String, dynamic>)['id'],
                  'name': json['name'],
                },
              )
              .toList();

      return Right(schools);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getGenderOptions() async {
    try {
      final response = await _apiService.get(
        'genders', // Simplified endpoint
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to get genders';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse genders data
      final gendersData = responseData['data'] as List<dynamic>?;
      if (gendersData == null) {
        return const Right([]);
      }

      final genders =
          gendersData
              .map(
                (json) => {
                  'id': (json as Map<String, dynamic>)['id'],
                  'name': json['name'],
                },
              )
              .toList();

      return Right(genders);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>>
  getReligionOptions() async {
    try {
      final response = await _apiService.get(
        'religions', // Simplified endpoint
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage =
            responseData['message'] ?? 'Failed to get religions';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse religions data
      final religionsData = responseData['data'] as List<dynamic>?;
      if (religionsData == null) {
        return const Right([]);
      }

      final religions =
          religionsData
              .map(
                (json) => {
                  'id': (json as Map<String, dynamic>)['id'],
                  'name': json['name'],
                },
              )
              .toList();

      return Right(religions);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

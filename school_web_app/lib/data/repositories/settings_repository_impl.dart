import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../../core/utils/logger.dart';
import '../../core/constants/app_constants.dart';
import '../../core/network/api_service.dart';
import '../../domain/repositories/settings_repository.dart';

/// Settings Repository Implementation
/// Following Repository Pattern for settings management
class SettingsRepositoryImpl implements SettingsRepository {
  final ApiService _apiService;

  SettingsRepositoryImpl({
    required ApiService apiService,
  }) : _apiService = apiService;

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getGrades() async {
    try {
      LoggerService.info('Getting grades from API');
      
      final response = await _apiService.get(
        AppConstants.gradeEndpoint,
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to get grades';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse grades data
      final gradesData = responseData['data'] as List<dynamic>?;
      if (gradesData == null) {
        return const Right([]);
      }

      final grades = gradesData
          .map((json) => {
                'id': (json as Map<String, dynamic>)['id'],
                'name': json['name'],
              })
          .toList();

      LoggerService.success('Successfully loaded ${grades.length} grades');
      return Right(grades);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when getting grades', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error('Unauthorized exception when getting grades', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when getting grades', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getReligions() async {
    try {
      LoggerService.info('Getting religions from API');
      
      final response = await _apiService.get(
        AppConstants.religionEndpoint,
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to get religions';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse religions data
      final religionsData = responseData['data'] as List<dynamic>?;
      if (religionsData == null) {
        return const Right([]);
      }

      final religions = religionsData
          .map((json) => {
                'id': (json as Map<String, dynamic>)['id'],
                'name': json['name'],
              })
          .toList();

      LoggerService.success('Successfully loaded ${religions.length} religions');
      return Right(religions);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when getting religions', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error('Unauthorized exception when getting religions', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when getting religions', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getGenders() async {
    try {
      LoggerService.info('Getting genders from API');
      
      final response = await _apiService.get(
        AppConstants.genderEndpoint,
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to get genders';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse genders data
      final gendersData = responseData['data'] as List<dynamic>?;
      if (gendersData == null) {
        return const Right([]);
      }

      final genders = gendersData
          .map((json) => {
                'id': (json as Map<String, dynamic>)['id'],
                'name': json['name'],
              })
          .toList();

      LoggerService.success('Successfully loaded ${genders.length} genders');
      return Right(genders);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when getting genders', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error('Unauthorized exception when getting genders', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when getting genders', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getBloodTypes() async {
    try {
      LoggerService.info('Getting blood types from API');
      
      final response = await _apiService.get(
        AppConstants.typeBloodEndpoint,
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to get blood types';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse blood types data
      final bloodTypesData = responseData['data'] as List<dynamic>?;
      if (bloodTypesData == null) {
        return const Right([]);
      }

      final bloodTypes = bloodTypesData
          .map((json) => {
                'id': (json as Map<String, dynamic>)['id'],
                'name': json['name'],
              })
          .toList();

      LoggerService.success('Successfully loaded ${bloodTypes.length} blood types');
      return Right(bloodTypes);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when getting blood types', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error('Unauthorized exception when getting blood types', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when getting blood types', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getSettings() async {
    try {
      LoggerService.info('Getting app settings from API');
      
      final response = await _apiService.get(
        AppConstants.settingsEndpoint,
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to get settings';
        return Left(ServerFailure(message: errorMessage));
      }

      // Parse settings data
      final settingsData = responseData['data'] as Map<String, dynamic>?;
      if (settingsData == null) {
        return const Right({});
      }

      LoggerService.success('Successfully loaded app settings');
      return Right(settingsData);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when getting settings', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error('Unauthorized exception when getting settings', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when getting settings', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> updateSettings(Map<String, dynamic> settings) async {
    try {
      LoggerService.info('Updating app settings via API');
      
      final response = await _apiService.post(
        url: AppConstants.settingsEndpoint,
        body: settings,
        isAuth: true,
      );

      final responseData = response.data;

      // Check for API errors
      if (responseData['errors'] == true) {
        final errorMessage = responseData['message'] ?? 'Failed to update settings';
        return Left(ServerFailure(message: errorMessage));
      }

      LoggerService.success('Successfully updated app settings');
      return const Right(true);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when updating settings', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error('Unauthorized exception when updating settings', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when updating settings', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

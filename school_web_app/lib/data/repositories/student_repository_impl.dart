import 'package:dartz/dartz.dart';

import '../../core/errors/exceptions.dart';
import '../../core/errors/failures.dart';
import '../../core/network/api_service.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/student.dart';
import '../../domain/repositories/student_repository.dart';
import '../models/student_response_model.dart';

/// Student repository implementation
class StudentRepositoryImpl implements StudentRepository {
  final ApiService _apiService;

  StudentRepositoryImpl(this._apiService);

  @override
  Future<Either<Failure, List<Student>>> getStudents({
    int page = 1,
    int perPage = 10,
    String? search,
  }) async {
    try {
      final queryParams = {
        'page': page.toString(),
        'limit': perPage.toString(),
      };

      if (search != null && search.isNotEmpty) {
        queryParams['text'] = search;
      }

      LoggerService.debug('Sending API request for students', data: {
        'endpoint': 'students/index',
        'queryParams': queryParams,
      });

      final response = await _apiService.get(
        'students/index',
        queryParameters: queryParams,
        isAuth: true,
      );

      // تسجيل البيانات المستلمة للتشخيص
      LoggerService.debug(
        'Raw API response for students',
        data: {
          'statusCode': response.statusCode,
          'responseType': response.data.runtimeType.toString(),
          'hasData': response.data is Map && response.data.containsKey('data'),
        },
      );

      // تسجيل عينة من البيانات
      if (response.data is Map &&
          response.data.containsKey('data') &&
          response.data['data'] is Map &&
          response.data['data'].containsKey('students') &&
          response.data['data']['students'] is Map &&
          response.data['data']['students'].containsKey('data') &&
          response.data['data']['students']['data'] is List) {

        final List studentsData = response.data['data']['students']['data'];
        LoggerService.debug('Students data sample', data: {
          'count': studentsData.length,
          'firstStudent': studentsData.isNotEmpty ? studentsData.first : null,
        });
      }

      // تسجيل كامل الاستجابة للتشخيص
      LoggerService.debug('Full API response structure', data: {
        'keys': response.data.keys.toList(),
        'hasData': response.data.containsKey('data'),
        'hasErrors': response.data.containsKey('errors'),
        'hasMessage': response.data.containsKey('message'),
      });

      // التحقق من وجود بيانات الطلاب مباشرة
      if (response.data.containsKey('data') &&
          response.data['data'] != null &&
          response.data['data'].containsKey('students') &&
          response.data['data']['students'] != null &&
          response.data['data']['students'].containsKey('data') &&
          response.data['data']['students']['data'] is List) {

        LoggerService.debug('Direct data extraction path found');

        // استخراج البيانات مباشرة
        final List<dynamic> studentsJson = response.data['data']['students']['data'] as List<dynamic>;
        final List<Student> students = [];

        // معالجة كل طالب بشكل آمن
        for (var studentData in studentsJson) {
          if (studentData is Map<String, dynamic>) {
            try {
              students.add(StudentModel.fromJson(studentData));
            } catch (e) {
              LoggerService.warning('Error parsing student', error: e, data: studentData);
              // نستمر في المعالجة حتى مع وجود خطأ في طالب واحد
            }
          }
        }

        LoggerService.debug('Successfully extracted students', data: {'count': students.length});
        return Right(students);
      } else {
        // إذا لم نتمكن من العثور على البيانات بالطريقة المباشرة، نحاول استخدام الطريقة القياسية
        try {
          final studentsResponse = StudentsResponse.fromJson(response.data);

          // نعتبر الاستجابة ناجحة إذا كان هناك طلاب، بغض النظر عن حالة الاستجابة
          if (studentsResponse.data.students.isNotEmpty) {
            return Right(studentsResponse.data.students);
          } else if (!studentsResponse.status) {
            return Left(ServerFailure(message: studentsResponse.message));
          } else {
            // حالة نجاح ولكن لا توجد بيانات
            return const Right([]);
          }
        } catch (e) {
          LoggerService.error('Error parsing students response', error: e);

          // رسالة خطأ أكثر وضوحاً للمستخدم
          return Left(ServerFailure(message: 'لا يمكن تحميل بيانات الطلاب. يرجى المحاولة مرة أخرى.'));
        }
      }
    } on ServerException catch (e) {
      LoggerService.error('Error getting students', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error getting students', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Student>> getStudentById(String id) async {
    try {
      final response = await _apiService.get('students/$id', isAuth: true);

      final responseData = response.data;
      if (responseData['status'] == false) {
        return Left(ServerFailure(message: responseData['message']));
      }

      final student = StudentModel.fromJson(responseData['data']);
      return Right(student);
    } on ServerException catch (e) {
      LoggerService.error('Error getting student by id', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error getting student by id', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Student>> createStudent(Student student) async {
    try {
      final studentModel = student as StudentModel;
      final response = await _apiService.post(
        url: 'students',
        body: studentModel.toJson(),
        isAuth: true,
      );

      final responseData = response.data;
      if (responseData['status'] == false) {
        return Left(ServerFailure(message: responseData['message']));
      }

      final createdStudent = StudentModel.fromJson(responseData['data']);
      return Right(createdStudent);
    } on ServerException catch (e) {
      LoggerService.error('Error creating student', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error creating student', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Student>> updateStudent(Student student) async {
    try {
      final studentModel = student as StudentModel;
      final response = await _apiService.put(
        url: 'students/${student.id}',
        body: studentModel.toJson(),
        isAuth: true,
      );

      final responseData = response.data;
      if (responseData['status'] == false) {
        return Left(ServerFailure(message: responseData['message']));
      }

      final updatedStudent = StudentModel.fromJson(responseData['data']);
      return Right(updatedStudent);
    } on ServerException catch (e) {
      LoggerService.error('Error updating student', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error updating student', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteStudent(String id) async {
    try {
      final response = await _apiService.delete(
        url: 'students/$id',
        isAuth: true,
      );

      final responseData = response.data;
      if (responseData['status'] == false) {
        return Left(ServerFailure(message: responseData['message']));
      }

      return const Right(true);
    } on ServerException catch (e) {
      LoggerService.error('Error deleting student', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error deleting student', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Student>>> getStudentsByBusId(String busId) async {
    try {
      final response = await _apiService.get(
        'general/buses/show/$busId',
        isAuth: true,
      );

      final responseData = response.data;
      if (responseData['errors'] != false) {
        return Left(ServerFailure(message: responseData['message'] ?? 'Failed to get students by bus ID'));
      }

      final List<dynamic> studentsJson = responseData['data']['students'] ?? [];
      final List<Student> students = studentsJson
          .map((student) => StudentModel.fromJson(student))
          .toList();

      return Right(students);
    } on ServerException catch (e) {
      LoggerService.error('Error getting students by bus ID', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error getting students by bus ID', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Student>>> getStudentsByParentId(String parentId) async {
    try {
      final response = await _apiService.get(
        'parents/$parentId',
        isAuth: true,
      );

      final responseData = response.data;
      if (responseData['errors'] != false) {
        return Left(ServerFailure(message: responseData['message'] ?? 'Failed to get students by parent ID'));
      }

      final List<dynamic> studentsJson = responseData['data']['children'] ?? [];
      final List<Student> students = studentsJson
          .map((student) => StudentModel.fromJson(student))
          .toList();

      return Right(students);
    } on ServerException catch (e) {
      LoggerService.error('Error getting students by parent ID', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unexpected error getting students by parent ID', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

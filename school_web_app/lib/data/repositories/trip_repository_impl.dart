import 'package:dartz/dartz.dart';

import '../../core/errors/exceptions.dart';
import '../../core/errors/failures.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/trip.dart';
import '../../domain/entities/trip_tracking_data.dart';
import '../../domain/repositories/trip_repository.dart';
import '../datasources/trip_remote_data_source.dart';
import '../models/trip_tracking_data_model.dart';

/// TripRepositoryImpl class for implementing TripRepository
/// Following Repository Pattern and Dependency Inversion Principle
class TripRepositoryImpl implements TripRepository {
  final TripRemoteDataSource remoteDataSource;

  TripRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, Trip?>> getCurrentTrip() async {
    try {
      LoggerService.info('Getting current trip from remote data source');
      final trip = await remoteDataSource.getCurrentTrip();
      return Right(trip);
    } on ServerException catch (e) {
      LoggerService.error(
        'Server exception when getting current trip',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when getting current trip',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when getting current trip',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Trip>> getTripById(String id) async {
    try {
      LoggerService.info(
        'Getting trip by id from remote data source',
        data: {'id': id},
      );
      final trip = await remoteDataSource.getTripById(id);
      return Right(trip);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when getting trip by id', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when getting trip by id',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when getting trip by id',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Trip>>> getUpcomingTrips() async {
    try {
      LoggerService.info('Getting upcoming trips from remote data source');
      final trips = await remoteDataSource.getUpcomingTrips();
      return Right(trips);
    } on ServerException catch (e) {
      LoggerService.error(
        'Server exception when getting upcoming trips',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when getting upcoming trips',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when getting upcoming trips',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Trip>>> getRecentTrips({
    int page = 1,
    int limit = 10,
    String? busId,
    String? date,
    String? search,
  }) async {
    try {
      LoggerService.info(
        'Getting recent trips from remote data source',
        data: {
          'page': page,
          'limit': limit,
          'busId': busId,
          'date': date,
          'search': search,
        },
      );
      final trips = await remoteDataSource.getRecentTrips(
        page: page,
        limit: limit,
        busId: busId,
        date: date,
        search: search,
      );
      return Right(trips);
    } on ServerException catch (e) {
      LoggerService.error(
        'Server exception when getting recent trips',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when getting recent trips',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when getting recent trips',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Trip>>> getTrips({
    int page = 1,
    int limit = 10,
    String? search,
    String? status,
    String? busId,
    String? driverId,
    String? supervisorId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      LoggerService.info('Getting trips with filters from remote data source');
      final trips = await remoteDataSource.getTrips(
        page: page,
        limit: limit,
        search: search,
        status: status,
        busId: busId,
        driverId: driverId,
        supervisorId: supervisorId,
        startDate: startDate,
        endDate: endDate,
      );
      return Right(trips);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when getting trips', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when getting trips',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when getting trips', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Trip>> createTrip(Trip trip) async {
    try {
      LoggerService.info('Creating trip from remote data source');
      final tripData = {
        'name': trip.name,
        'school_id': trip.schoolId,
        'bus_id': trip.busId,
        'driver_id': trip.driverId,
        'supervisor_id': trip.supervisorId,
        'start_time': trip.startTime.toIso8601String(),
        'start_location': trip.startLocation,
        'end_location': trip.endLocation,
        'student_ids': trip.studentIds,
      };
      final createdTrip = await remoteDataSource.createTrip(tripData);
      return Right(createdTrip);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when creating trip', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when creating trip',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when creating trip', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Trip>> updateTrip(Trip trip) async {
    try {
      LoggerService.info('Updating trip from remote data source');
      final tripData = {
        'name': trip.name,
        'school_id': trip.schoolId,
        'bus_id': trip.busId,
        'driver_id': trip.driverId,
        'supervisor_id': trip.supervisorId,
        'start_time': trip.startTime.toIso8601String(),
        'start_location': trip.startLocation,
        'end_location': trip.endLocation,
        'student_ids': trip.studentIds,
        'status': trip.status,
      };
      final updatedTrip = await remoteDataSource.updateTrip(trip.id, tripData);
      return Right(updatedTrip);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when updating trip', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when updating trip',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when updating trip', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteTrip(String id) async {
    try {
      LoggerService.info('Deleting trip from remote data source');
      final result = await remoteDataSource.deleteTrip(id);
      return Right(result);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when deleting trip', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when deleting trip',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when deleting trip', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Trip>> startTrip(String id) async {
    try {
      LoggerService.info('Starting trip from remote data source');
      final trip = await remoteDataSource.startTrip(id);
      return Right(trip);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when starting trip', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when starting trip',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when starting trip', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Trip>> endTrip(String id) async {
    try {
      LoggerService.info('Ending trip from remote data source');
      final trip = await remoteDataSource.endTrip(id);
      return Right(trip);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when ending trip', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error('Unauthorized exception when ending trip', error: e);
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error('Unknown exception when ending trip', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> updateTripLocation(
    String id,
    double latitude,
    double longitude,
  ) async {
    try {
      LoggerService.info('Updating trip location from remote data source');
      final result = await remoteDataSource.updateTripLocation(
        id,
        latitude,
        longitude,
      );
      return Right(result);
    } on ServerException catch (e) {
      LoggerService.error(
        'Server exception when updating trip location',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when updating trip location',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when updating trip location',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<TripStop>>> getTripRoute(String id) async {
    try {
      LoggerService.info('Getting trip route from remote data source');
      await remoteDataSource.getTripRoute(id);
      // Convert route data to TripStop entities
      // This would need to be implemented based on the actual API response structure
      return const Right([]);
    } on ServerException catch (e) {
      LoggerService.error('Server exception when getting trip route', error: e);
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when getting trip route',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when getting trip route',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> updateTripRoute(
    String id,
    List<TripStop> stops,
  ) async {
    try {
      LoggerService.info('Updating trip route from remote data source');
      // This would need to be implemented based on the actual API requirements
      return const Right(true);
    } on ServerException catch (e) {
      LoggerService.error(
        'Server exception when updating trip route',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when updating trip route',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when updating trip route',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, TripTrackingData>> getTripTracking(String id) async {
    try {
      LoggerService.info('Getting trip tracking data from remote data source');
      final trackingData = await remoteDataSource.getTripTracking(id);
      final trackingModel = TripTrackingDataModel.fromJson(trackingData);
      return Right(trackingModel);
    } on ServerException catch (e) {
      LoggerService.error(
        'Server exception when getting trip tracking',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when getting trip tracking',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when getting trip tracking',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Trip>>> getMorningTrips({
    DateTime? date,
    String? busId,
  }) async {
    try {
      LoggerService.info('Getting morning trips from remote data source');
      final trips = await remoteDataSource.getMorningTrips(
        date: date,
        busId: busId,
      );
      return Right(trips);
    } on ServerException catch (e) {
      LoggerService.error(
        'Server exception when getting morning trips',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when getting morning trips',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when getting morning trips',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Trip>>> getEveningTrips({
    DateTime? date,
    String? busId,
  }) async {
    try {
      LoggerService.info('Getting evening trips from remote data source');
      final trips = await remoteDataSource.getEveningTrips(
        date: date,
        busId: busId,
      );
      return Right(trips);
    } on ServerException catch (e) {
      LoggerService.error(
        'Server exception when getting evening trips',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      LoggerService.error(
        'Unauthorized exception when getting evening trips',
        error: e,
      );
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      LoggerService.error(
        'Unknown exception when getting evening trips',
        error: e,
      );
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

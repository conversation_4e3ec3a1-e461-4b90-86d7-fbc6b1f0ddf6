import 'package:equatable/equatable.dart';

/// Entity class for absence requests
class AbsenceRequest extends Equatable {
  final int? id;
  final String? gradeId;
  final String? schoolId;
  final String? classroomId;
  final String? busId;
  final String? parentId;
  final String? studentId;
  final String? attendanceDate;
  final String? attendanceType;
  final String? status;
  final String? createdAt;
  final String? updatedAt;
  final School? school;
  final Bus? bus;
  final Grade? grade;
  final Classroom? classroom;
  final Parent? parent;
  final Student? student;

  const AbsenceRequest({
    this.id,
    this.gradeId,
    this.schoolId,
    this.classroomId,
    this.busId,
    this.parentId,
    this.studentId,
    this.attendanceDate,
    this.attendanceType,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.school,
    this.bus,
    this.grade,
    this.classroom,
    this.parent,
    this.student,
  });

  @override
  List<Object?> get props => [
    id,
    gradeId,
    schoolId,
    classroomId,
    busId,
    parentId,
    studentId,
    attendanceDate,
    attendanceType,
    status,
    createdAt,
    updatedAt,
    school,
    bus,
    grade,
    classroom,
    parent,
    student,
  ];
}

/// School entity
class School extends Equatable {
  final int? id;
  final String? name;
  final String? email;
  final String? phone;
  final String? address;
  final String? logo;

  const School({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.address,
    this.logo,
  });

  @override
  List<Object?> get props => [id, name, email, phone, address, logo];
}

/// Bus entity
class Bus extends Equatable {
  final int? id;
  final String? name;
  final String? carNumber;
  final String? notes;

  const Bus({this.id, this.name, this.carNumber, this.notes});

  @override
  List<Object?> get props => [id, name, carNumber, notes];
}

/// Grade entity
class Grade extends Equatable {
  final int? id;
  final String? name;

  const Grade({this.id, this.name});

  @override
  List<Object?> get props => [id, name];
}

/// Classroom entity
class Classroom extends Equatable {
  final int? id;
  final String? name;

  const Classroom({this.id, this.name});

  @override
  List<Object?> get props => [id, name];
}

/// Parent entity for absence request
class Parent extends Equatable {
  final int? id;
  final String? name;
  final String? phone;
  final String? email;

  const Parent({this.id, this.name, this.phone, this.email});

  @override
  List<Object?> get props => [id, name, phone, email];
}

/// Student entity for absence request
class Student extends Equatable {
  final int? id;
  final String? name;
  final String? phone;
  final String? gradeId;
  final String? genderId;
  final String? schoolId;
  final String? religionId;
  final String? typeBloodId;
  final String? classroomId;
  final String? busId;
  final String? address;
  final String? cityName;
  final String? status;
  final String? tripType;
  final String? attendanceType;
  final String? latitude;
  final String? longitude;

  const Student({
    this.id,
    this.name,
    this.phone,
    this.gradeId,
    this.genderId,
    this.schoolId,
    this.religionId,
    this.typeBloodId,
    this.classroomId,
    this.busId,
    this.address,
    this.cityName,
    this.status,
    this.tripType,
    this.attendanceType,
    this.latitude,
    this.longitude,
  });

  @override
  List<Object?> get props => [
    id,
    name,
    phone,
    gradeId,
    genderId,
    schoolId,
    religionId,
    typeBloodId,
    classroomId,
    busId,
    address,
    cityName,
    status,
    tripType,
    attendanceType,
    latitude,
    longitude,
  ];
}

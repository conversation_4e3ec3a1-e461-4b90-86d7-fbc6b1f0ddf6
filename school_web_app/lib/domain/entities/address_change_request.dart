import 'package:equatable/equatable.dart';

/// Entity class for address change requests
class AddressChangeRequest extends Equatable {
  final int id;
  final String? oldAddress;
  final String? oldLatitude;
  final String? oldLongitude;
  final String? address;
  final String? longitude;
  final String? latitude;
  final String? parentName;
  final String schoolName;
  final String studentName;
  final String gradeName;
  final String busName;
  final int status;
  final StatusText? statusText;
  final int? parentId;
  final int? schoolId;
  final int? busId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const AddressChangeRequest({
    required this.id,
    this.oldAddress,
    this.oldLatitude,
    this.oldLongitude,
    this.address,
    this.longitude,
    this.latitude,
    this.parentName,
    required this.schoolName,
    required this.studentName,
    required this.gradeName,
    required this.busName,
    required this.status,
    this.statusText,
    this.parentId,
    this.schoolId,
    this.busId,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        oldAddress,
        oldLatitude,
        oldLongitude,
        address,
        longitude,
        latitude,
        parentName,
        schoolName,
        studentName,
        gradeName,
        busName,
        status,
        statusText,
        parentId,
        schoolId,
        busId,
        createdAt,
        updatedAt,
      ];
}

/// Status text with color information
class StatusText extends Equatable {
  final String? text;
  final String? color;

  const StatusText({
    this.text,
    this.color,
  });

  @override
  List<Object?> get props => [text, color];
}

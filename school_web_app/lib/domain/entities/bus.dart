import 'package:equatable/equatable.dart';

/// Bus entity
class Bus extends Equatable {
  final int? id;
  final String? name;
  final String? carNumber;
  final String? notes;
  final String? createdAt;
  final String? updatedAt;

  const Bus({
    this.id,
    this.name,
    this.carNumber,
    this.notes,
    this.createdAt,
    this.updatedAt,
  });

  /// Create a copy of this Bus with the given fields replaced with the new values
  Bus copyWith({
    int? id,
    String? name,
    String? carNumber,
    String? notes,
    String? createdAt,
    String? updatedAt,
  }) {
    return Bus(
      id: id ?? this.id,
      name: name ?? this.name,
      carNumber: carNumber ?? this.carNumber,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        carNumber,
        notes,
        createdAt,
        updatedAt,
      ];
}

import 'package:equatable/equatable.dart';

/// Course entity
/// Following Single Responsibility Principle by focusing only on course data
class Course extends Equatable {
  final String id;
  final String title;
  final String description;
  final String teacherName;
  final String? teacherId;
  final String? imageUrl;
  final List<String>? schedule;
  final double? grade;
  
  const Course({
    required this.id,
    required this.title,
    required this.description,
    required this.teacherName,
    this.teacherId,
    this.imageUrl,
    this.schedule,
    this.grade,
  });
  
  @override
  List<Object?> get props => [
    id,
    title,
    description,
    teacherName,
    teacherId,
    imageUrl,
    schedule,
    grade,
  ];
}

import 'package:equatable/equatable.dart';

/// DashboardStats entity
/// Following Single Responsibility Principle by focusing only on dashboard stats data
class DashboardStats extends Equatable {
  final int totalStudents;
  final int totalBuses;
  final int totalDrivers;
  final int totalSupervisors;
  final int totalTrips;
  final int completedTrips;
  final int cancelledTrips;
  final int inProgressTrips;
  final int upcomingTrips;
  final double attendanceRate;
  final double onTimeRate;

  const DashboardStats({
    required this.totalStudents,
    required this.totalBuses,
    required this.totalDrivers,
    required this.totalSupervisors,
    required this.totalTrips,
    required this.completedTrips,
    required this.cancelledTrips,
    required this.inProgressTrips,
    required this.upcomingTrips,
    required this.attendanceRate,
    required this.onTimeRate,
  });

  @override
  List<Object?> get props => [
        totalStudents,
        totalBuses,
        totalDrivers,
        totalSupervisors,
        totalTrips,
        completedTrips,
        cancelledTrips,
        inProgressTrips,
        upcomingTrips,
        attendanceRate,
        onTimeRate,
      ];
}

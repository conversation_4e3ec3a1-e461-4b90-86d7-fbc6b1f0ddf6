import 'package:equatable/equatable.dart';

/// Entity class for notifications
class NotificationEntity extends Equatable {
  final int? id;
  final String? title;
  final String? body;
  final String? type;
  final String? data;
  final String? image;
  final bool? isRead;
  final String? createdAt;
  final String? updatedAt;
  final NotificationData? notificationData;

  const NotificationEntity({
    this.id,
    this.title,
    this.body,
    this.type,
    this.data,
    this.image,
    this.isRead,
    this.createdAt,
    this.updatedAt,
    this.notificationData,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        body,
        type,
        data,
        image,
        isRead,
        createdAt,
        updatedAt,
        notificationData,
      ];

  /// Create a copy with updated fields
  NotificationEntity copyWith({
    int? id,
    String? title,
    String? body,
    String? type,
    String? data,
    String? image,
    bool? isRead,
    String? createdAt,
    String? updatedAt,
    NotificationData? notificationData,
  }) {
    return NotificationEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      data: data ?? this.data,
      image: image ?? this.image,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      notificationData: notificationData ?? this.notificationData,
    );
  }
}

/// Notification data entity
class NotificationData extends Equatable {
  final String? tripId;
  final String? studentId;
  final String? busId;
  final String? driverId;
  final String? route;
  final Map<String, dynamic>? additionalData;

  const NotificationData({
    this.tripId,
    this.studentId,
    this.busId,
    this.driverId,
    this.route,
    this.additionalData,
  });

  @override
  List<Object?> get props => [
        tripId,
        studentId,
        busId,
        driverId,
        route,
        additionalData,
      ];
}

/// Enum for notification types
enum NotificationType {
  tripStarted,
  tripEnded,
  studentPickedUp,
  studentDroppedOff,
  busBreakdown,
  routeChanged,
  general,
}

/// Extension for notification type display names
extension NotificationTypeExtension on NotificationType {
  String get displayName {
    switch (this) {
      case NotificationType.tripStarted:
        return 'بدء الرحلة';
      case NotificationType.tripEnded:
        return 'انتهاء الرحلة';
      case NotificationType.studentPickedUp:
        return 'استلام الطالب';
      case NotificationType.studentDroppedOff:
        return 'توصيل الطالب';
      case NotificationType.busBreakdown:
        return 'عطل في الحافلة';
      case NotificationType.routeChanged:
        return 'تغيير المسار';
      case NotificationType.general:
        return 'إشعار عام';
    }
  }

  String get icon {
    switch (this) {
      case NotificationType.tripStarted:
        return 'play_arrow';
      case NotificationType.tripEnded:
        return 'stop';
      case NotificationType.studentPickedUp:
        return 'person_add';
      case NotificationType.studentDroppedOff:
        return 'person_remove';
      case NotificationType.busBreakdown:
        return 'warning';
      case NotificationType.routeChanged:
        return 'route';
      case NotificationType.general:
        return 'notifications';
    }
  }

  static NotificationType fromString(String type) {
    switch (type.toLowerCase()) {
      case 'trip_started':
        return NotificationType.tripStarted;
      case 'trip_ended':
        return NotificationType.tripEnded;
      case 'student_picked_up':
        return NotificationType.studentPickedUp;
      case 'student_dropped_off':
        return NotificationType.studentDroppedOff;
      case 'bus_breakdown':
        return NotificationType.busBreakdown;
      case 'route_changed':
        return NotificationType.routeChanged;
      default:
        return NotificationType.general;
    }
  }
}

/// FCM Token entity
class FCMToken extends Equatable {
  final int? id;
  final String? token;
  final String? deviceType;
  final String? deviceId;
  final String? userId;
  final bool? isActive;
  final String? createdAt;
  final String? updatedAt;

  const FCMToken({
    this.id,
    this.token,
    this.deviceType,
    this.deviceId,
    this.userId,
    this.isActive,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        token,
        deviceType,
        deviceId,
        userId,
        isActive,
        createdAt,
        updatedAt,
      ];
}

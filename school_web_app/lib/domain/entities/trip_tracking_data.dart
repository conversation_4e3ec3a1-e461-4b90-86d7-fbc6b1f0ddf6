import 'package:equatable/equatable.dart';

/// Trip tracking data entity for real-time trip monitoring
/// Following Single Responsibility Principle by focusing only on tracking data
class TripTrackingData extends Equatable {
  final String tripId;
  final double currentLatitude;
  final double currentLongitude;
  final double speed;
  final String direction;
  final DateTime lastUpdate;
  final String status;
  final int currentStopIndex;
  final List<TrackingPoint> trackingHistory;
  final double distanceTraveled;
  final double remainingDistance;
  final int estimatedArrivalTime;
  final List<String> onboardStudents;
  final List<String> absentStudents;

  const TripTrackingData({
    required this.tripId,
    required this.currentLatitude,
    required this.currentLongitude,
    required this.speed,
    required this.direction,
    required this.lastUpdate,
    required this.status,
    required this.currentStopIndex,
    required this.trackingHistory,
    required this.distanceTraveled,
    required this.remainingDistance,
    required this.estimatedArrivalTime,
    required this.onboardStudents,
    required this.absentStudents,
  });

  @override
  List<Object?> get props => [
        tripId,
        currentLatitude,
        currentLongitude,
        speed,
        direction,
        lastUpdate,
        status,
        currentStopIndex,
        trackingHistory,
        distanceTraveled,
        remainingDistance,
        estimatedArrivalTime,
        onboardStudents,
        absentStudents,
      ];

  /// Create a copy with updated values
  TripTrackingData copyWith({
    String? tripId,
    double? currentLatitude,
    double? currentLongitude,
    double? speed,
    String? direction,
    DateTime? lastUpdate,
    String? status,
    int? currentStopIndex,
    List<TrackingPoint>? trackingHistory,
    double? distanceTraveled,
    double? remainingDistance,
    int? estimatedArrivalTime,
    List<String>? onboardStudents,
    List<String>? absentStudents,
  }) {
    return TripTrackingData(
      tripId: tripId ?? this.tripId,
      currentLatitude: currentLatitude ?? this.currentLatitude,
      currentLongitude: currentLongitude ?? this.currentLongitude,
      speed: speed ?? this.speed,
      direction: direction ?? this.direction,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      status: status ?? this.status,
      currentStopIndex: currentStopIndex ?? this.currentStopIndex,
      trackingHistory: trackingHistory ?? this.trackingHistory,
      distanceTraveled: distanceTraveled ?? this.distanceTraveled,
      remainingDistance: remainingDistance ?? this.remainingDistance,
      estimatedArrivalTime: estimatedArrivalTime ?? this.estimatedArrivalTime,
      onboardStudents: onboardStudents ?? this.onboardStudents,
      absentStudents: absentStudents ?? this.absentStudents,
    );
  }
}

/// Individual tracking point in the trip history
class TrackingPoint extends Equatable {
  final double latitude;
  final double longitude;
  final DateTime timestamp;
  final double speed;
  final String status;

  const TrackingPoint({
    required this.latitude,
    required this.longitude,
    required this.timestamp,
    required this.speed,
    required this.status,
  });

  @override
  List<Object?> get props => [
        latitude,
        longitude,
        timestamp,
        speed,
        status,
      ];
}

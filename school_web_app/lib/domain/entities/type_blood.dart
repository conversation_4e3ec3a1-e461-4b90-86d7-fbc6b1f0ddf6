import 'package:equatable/equatable.dart';

/// TypeBlood entity
class Type<PERSON>lood extends Equatable {
  final int? id;
  final String? name;
  final String? createdAt;
  final String? updatedAt;

  const TypeBlood({
    this.id,
    this.name,
    this.createdAt,
    this.updatedAt,
  });

  /// Create a copy of this TypeBlood with the given fields replaced with the new values
  TypeBlood copyWith({
    int? id,
    String? name,
    String? createdAt,
    String? updatedAt,
  }) {
    return TypeBlood(
      id: id ?? this.id,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        createdAt,
        updatedAt,
      ];
}

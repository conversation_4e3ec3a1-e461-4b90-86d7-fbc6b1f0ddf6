import 'package:equatable/equatable.dart';

/// User entity
/// Following Single Responsibility Principle by focusing only on user data
class User extends Equatable {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String? type;
  final String? role;
  final String? address;
  final String? cityName;
  final String? logo;
  final String? logoPath;
  final String? imageUrl;
  final String? token;
  final double? latitude;
  final double? longitude;
  final int? schoolId;
  final bool? isVerified;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const User({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.type,
    this.role,
    this.address,
    this.cityName,
    this.logo,
    this.logoPath,
    this.imageUrl,
    this.token,
    this.latitude,
    this.longitude,
    this.schoolId,
    this.isVerified,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
    id,
    name,
    email,
    phone,
    type,
    role,
    address,
    cityName,
    logo,
    logoPath,
    imageUrl,
    token,
    latitude,
    longitude,
    schoolId,
    isVerified,
    createdAt,
    updatedAt,
  ];

  /// Check if the user is authenticated
  bool get isAuthenticated => token != null && token!.isNotEmpty;

  /// Check if the user is an admin
  bool get isAdmin => type == 'admin' || role == 'admin';

  /// Check if the user is a school
  bool get isSchool => type == 'school';

  /// Check if the user is a parent
  bool get isParent => type == 'parent';

  /// Check if the user is a driver
  bool get isDriver => type == 'driver';

  /// Check if the user is a supervisor
  bool get isSupervisor => type == 'supervisor';

  /// Create a copy of this User with the given fields replaced with the new values
  User copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? type,
    String? role,
    String? address,
    String? cityName,
    String? logo,
    String? logoPath,
    String? imageUrl,
    String? token,
    double? latitude,
    double? longitude,
    int? schoolId,
    bool? isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      type: type ?? this.type,
      role: role ?? this.role,
      address: address ?? this.address,
      cityName: cityName ?? this.cityName,
      logo: logo ?? this.logo,
      logoPath: logoPath ?? this.logoPath,
      imageUrl: imageUrl ?? this.imageUrl,
      token: token ?? this.token,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      schoolId: schoolId ?? this.schoolId,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/absence_request.dart';

/// Repository interface for absence requests
/// Following Dependency Inversion Principle
abstract class AbsenceRepository {
  /// Get absence requests with pagination and filtering
  Future<Either<Failure, List<AbsenceRequest>>> getAbsenceRequests({
    int page = 1,
    int limit = 10,
    String? busId,
    String? attendanceType,
    String? studentName,
    String? date,
  });

  /// Get absence request by ID
  Future<Either<Failure, AbsenceRequest>> getAbsenceRequestById(int id);

  /// Create new absence request
  Future<Either<Failure, AbsenceRequest>> createAbsenceRequest({
    required String studentId,
    required String attendanceDate,
    required String attendanceType,
    String? notes,
  });

  /// Update absence request
  Future<Either<Failure, AbsenceRequest>> updateAbsenceRequest({
    required int id,
    String? attendanceDate,
    String? attendanceType,
    String? notes,
  });

  /// Delete absence request
  Future<Either<Failure, bool>> deleteAbsenceRequest(int id);

  /// Get attendance types
  Future<Either<Failure, List<String>>> getAttendanceTypes();
}

import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/address_change_request.dart';

/// Repository interface for address change requests
/// Following Dependency Inversion Principle
abstract class AddressChangeRepository {
  /// Get address change requests with pagination and filtering
  Future<Either<Failure, List<AddressChangeRequest>>> getAddressChangeRequests({
    int page = 1,
    int limit = 10,
    String? status,
    String? search,
  });

  /// Get address change request by ID
  Future<Either<Failure, AddressChangeRequest>> getAddressChangeRequestById(
    int id,
  );

  /// Accept address change request
  Future<Either<Failure, bool>> acceptAddressChangeRequest(int requestId);

  /// Refuse address change request
  Future<Either<Failure, bool>> refuseAddressChangeRequest({
    required int requestId,
    required String reason,
  });

  /// Get temporary addresses
  Future<Either<Failure, List<AddressChangeRequest>>> getTemporaryAddresses({
    int page = 1,
    int limit = 10,
    String? search,
  });

  /// Create new address change request
  Future<Either<Failure, AddressChangeRequest>> createAddressChangeRequest({
    required String address,
    required String latitude,
    required String longitude,
    required int studentId,
    String? notes,
  });

  /// Update address change request
  Future<Either<Failure, AddressChangeRequest>> updateAddressChangeRequest({
    required int id,
    String? address,
    String? latitude,
    String? longitude,
    String? notes,
  });

  /// Delete address change request
  Future<Either<Failure, bool>> deleteAddressChangeRequest(int id);
}

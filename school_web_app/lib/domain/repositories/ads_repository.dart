import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/ad.dart';

/// Ads Repository interface
/// Following Repository Pattern for ads management
abstract class AdsRepository {
  /// Get all ads
  Future<Either<Failure, List<Ad>>> getAds();
  
  /// Get ad by ID
  Future<Either<Failure, Ad>> getAdById(int id);
  
  /// Create new ad
  Future<Either<Failure, Ad>> createAd(Map<String, dynamic> adData);
  
  /// Update existing ad
  Future<Either<Failure, Ad>> updateAd(int id, Map<String, dynamic> adData);
  
  /// Delete ad
  Future<Either<Failure, bool>> deleteAd(int id);
}

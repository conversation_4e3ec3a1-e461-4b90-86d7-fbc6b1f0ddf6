import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';

/// Base repository interface
/// Following Dependency Inversion Principle by depending on abstractions
abstract class BaseRepository<T> {
  /// Get all items
  Future<Either<Failure, List<T>>> getAll();
  
  /// Get item by id
  Future<Either<Failure, T>> getById(String id);
  
  /// Create a new item
  Future<Either<Failure, T>> create(T item);
  
  /// Update an existing item
  Future<Either<Failure, T>> update(T item);
  
  /// Delete an item by id
  Future<Either<Failure, bool>> delete(String id);
}

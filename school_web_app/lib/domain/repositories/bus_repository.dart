import 'package:dartz/dartz.dart';

import '../entities/bus.dart';
import '../../core/errors/failures.dart';

/// Bus repository interface
/// Following Dependency Inversion Principle by defining contracts
abstract class BusRepository {
  /// Get buses with pagination and search
  Future<Either<Failure, List<Bus>>> getBuses({
    int page = 1,
    int perPage = 10,
    String? search,
  });

  /// Get bus by ID
  Future<Either<Failure, Bus>> getBusById(int id);

  /// Create new bus
  Future<Either<Failure, Bus>> createBus(Bus bus);

  /// Update existing bus
  Future<Either<Failure, Bus>> updateBus(Bus bus);

  /// Delete bus
  Future<Either<Failure, bool>> deleteBus(int id);

  /// Get buses by driver ID
  Future<Either<Failure, List<Bus>>> getBusesByDriverId(String driverId);

  /// Get available buses for assignment
  Future<Either<Failure, List<Bus>>> getAvailableBuses();
}

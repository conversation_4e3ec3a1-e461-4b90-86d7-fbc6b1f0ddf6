import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';

/// File Repository interface
/// Following Repository Pattern for file management operations
abstract class FileRepository {
  /// Download students example file
  Future<Either<Failure, String>> downloadStudentsExampleFile();
  
  /// Download students PDF file
  Future<Either<Failure, String>> downloadStudentsPDFFile();
  
  /// Get file name and extension
  Future<Either<Failure, Map<String, dynamic>>> getFileNameAndExtension();
  
  /// Download file by URL
  Future<Either<Failure, String>> downloadFile(String url, String fileName);
}

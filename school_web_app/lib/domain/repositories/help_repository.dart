import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';

/// Help Repository interface
/// Following Repository Pattern for help/questions management
abstract class HelpRepository {
  /// Get all help questions
  Future<Either<Failure, List<Map<String, dynamic>>>> getHelpQuestions();
  
  /// Get help question by ID
  Future<Either<Failure, Map<String, dynamic>>> getHelpQuestionById(int id);
  
  /// Submit help question
  Future<Either<Failure, bool>> submitHelpQuestion(Map<String, dynamic> questionData);
  
  /// Get FAQ
  Future<Either<Failure, List<Map<String, dynamic>>>> getFAQ();
}

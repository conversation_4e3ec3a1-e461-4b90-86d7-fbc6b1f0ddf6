import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/notification.dart';

/// Repository interface for notifications
/// Following Dependency Inversion Principle
abstract class NotificationRepository {
  /// Get notifications with pagination
  Future<Either<Failure, List<NotificationEntity>>> getNotifications({
    int page = 1,
    int limit = 20,
    bool? isRead,
    String? type,
  });

  /// Get notification by ID
  Future<Either<Failure, NotificationEntity>> getNotificationById(int id);

  /// Mark notification as read
  Future<Either<Failure, bool>> markAsRead(int notificationId);

  /// Mark all notifications as read
  Future<Either<Failure, bool>> markAllAsRead();

  /// Delete notification
  Future<Either<Failure, bool>> deleteNotification(int notificationId);

  /// Delete all notifications
  Future<Either<Failure, bool>> deleteAllNotifications();

  /// Get unread notifications count
  Future<Either<Failure, int>> getUnreadCount();

  /// Register FCM token
  Future<Either<Failure, bool>> registerFCMToken({
    required String token,
    required String deviceType,
    required String deviceId,
  });

  /// Update FCM token
  Future<Either<Failure, bool>> updateFCMToken({
    required String oldToken,
    required String newToken,
  });

  /// Unregister FCM token
  Future<Either<Failure, bool>> unregisterFCMToken(String token);

  /// Get user's FCM tokens
  Future<Either<Failure, List<FCMToken>>> getUserFCMTokens();

  /// Send notification (admin only)
  Future<Either<Failure, bool>> sendNotification({
    required String title,
    required String body,
    String? type,
    String? image,
    Map<String, dynamic>? data,
    List<String>? userIds,
    List<String>? tokens,
  });
}

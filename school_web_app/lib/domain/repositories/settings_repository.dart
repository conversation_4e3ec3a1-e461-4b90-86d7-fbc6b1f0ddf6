import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';

/// Settings Repository interface
/// Following Repository Pattern for settings management
abstract class SettingsRepository {
  /// Get all grades
  Future<Either<Failure, List<Map<String, dynamic>>>> getGrades();
  
  /// Get all religions
  Future<Either<Failure, List<Map<String, dynamic>>>> getReligions();
  
  /// Get all genders
  Future<Either<Failure, List<Map<String, dynamic>>>> getGenders();
  
  /// Get all blood types
  Future<Either<Failure, List<Map<String, dynamic>>>> getBloodTypes();
  
  /// Get app settings
  Future<Either<Failure, Map<String, dynamic>>> getSettings();
  
  /// Update app settings
  Future<Either<Failure, bool>> updateSettings(Map<String, dynamic> settings);
}

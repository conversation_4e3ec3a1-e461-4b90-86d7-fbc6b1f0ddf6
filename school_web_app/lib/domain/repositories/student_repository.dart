import '../../core/errors/failures.dart';
import 'package:dartz/dartz.dart';

import '../entities/student.dart';

/// Student repository interface
abstract class StudentRepository {
  /// Get students
  Future<Either<Failure, List<Student>>> getStudents({
    int page = 1,
    int perPage = 10,
    String? search,
  });

  /// Get student by id
  Future<Either<Failure, Student>> getStudentById(String id);

  /// Create student
  Future<Either<Failure, Student>> createStudent(Student student);

  /// Update student
  Future<Either<Failure, Student>> updateStudent(Student student);

  /// Delete student
  Future<Either<Failure, bool>> deleteStudent(String id);

  /// Get students by bus id
  Future<Either<Failure, List<Student>>> getStudentsByBusId(String busId);

  /// Get students by parent id
  Future<Either<Failure, List<Student>>> getStudentsByParentId(String parentId);
}

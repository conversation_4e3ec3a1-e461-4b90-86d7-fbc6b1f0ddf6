import 'package:dartz/dartz.dart';

import '../../core/errors/failures.dart';
import '../entities/trip.dart';
import '../entities/trip_tracking_data.dart';

/// TripRepository interface
/// Following Dependency Inversion Principle
abstract class TripRepository {
  /// Get current trip
  Future<Either<Failure, Trip?>> getCurrentTrip();

  /// Get trip by id
  Future<Either<Failure, Trip>> getTripById(String id);

  /// Get upcoming trips
  Future<Either<Failure, List<Trip>>> getUpcomingTrips();

  /// Get recent trips
  Future<Either<Failure, List<Trip>>> getRecentTrips({
    int page = 1,
    int limit = 10,
    String? busId,
    String? date,
    String? search,
  });

  /// Get trips with pagination and filtering
  Future<Either<Failure, List<Trip>>> getTrips({
    int page = 1,
    int limit = 10,
    String? search,
    String? status,
    String? busId,
    String? driverId,
    String? supervisorId,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Create new trip
  Future<Either<Failure, Trip>> createTrip(Trip trip);

  /// Update existing trip
  Future<Either<Failure, Trip>> updateTrip(Trip trip);

  /// Delete trip
  Future<Either<Failure, bool>> deleteTrip(String id);

  /// Start trip
  Future<Either<Failure, Trip>> startTrip(String id);

  /// End trip
  Future<Either<Failure, Trip>> endTrip(String id);

  /// Update trip location
  Future<Either<Failure, bool>> updateTripLocation(
    String id,
    double latitude,
    double longitude,
  );

  /// Get trip route
  Future<Either<Failure, List<TripStop>>> getTripRoute(String id);

  /// Update trip route
  Future<Either<Failure, bool>> updateTripRoute(
    String id,
    List<TripStop> stops,
  );

  /// Get real-time trip tracking data
  Future<Either<Failure, TripTrackingData>> getTripTracking(String id);

  /// Get morning trips
  Future<Either<Failure, List<Trip>>> getMorningTrips({
    DateTime? date,
    String? busId,
  });

  /// Get evening trips
  Future<Either<Failure, List<Trip>>> getEveningTrips({
    DateTime? date,
    String? busId,
  });
}

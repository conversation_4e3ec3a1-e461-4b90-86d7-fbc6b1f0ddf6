import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/user.dart';
import 'base_repository.dart';

/// User repository interface
/// Following Interface Segregation Principle by creating a specific interface for user operations
abstract class UserRepository extends BaseRepository<User> {
  /// Authenticate user with email and password
  Future<Either<Failure, User>> login(String email, String password);

  /// Register a new user
  Future<Either<Failure, User>> register(User user, String password);

  /// Get current authenticated user
  Future<Either<Failure, User>> getCurrentUser();

  /// Logout current user
  Future<Either<Failure, bool>> logout();

  /// Forgot password
  Future<Either<Failure, bool>> forgotPassword(String email);

  /// Reset password
  Future<Either<Failure, bool>> resetPassword(
    String email,
    String code,
    String password,
  );

  /// Verify email
  Future<Either<Failure, bool>> verifyEmail(String email, String code);

  /// Resend verification code
  Future<Either<Failure, bool>> resendVerificationCode(String email);

  /// Refresh authentication token
  Future<Either<Failure, String>> refreshToken();

  /// Update user profile
  Future<Either<Failure, User>> updateProfile(User user);

  /// Change password
  Future<Either<Failure, bool>> changePassword(
    String currentPassword,
    String newPassword,
  );

  /// Check if user is authenticated
  Future<Either<Failure, bool>> isAuthenticated();

  /// Upload profile image
  Future<Either<Failure, String>> uploadProfileImage(
    String userId,
    String imagePath,
  );
}

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../repositories/address_change_repository.dart';

/// Use case for accepting address change requests
class AcceptAddressChangeRequestUseCase
    implements UseCase<bool, AcceptAddressChangeRequestParams> {
  final AddressChangeRepository repository;

  AcceptAddressChangeRequestUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(
    AcceptAddressChangeRequestParams params,
  ) async {
    return await repository.acceptAddressChangeRequest(params.requestId);
  }
}

/// Parameters for AcceptAddressChangeRequestUseCase
class AcceptAddressChangeRequestParams extends Equatable {
  final int requestId;

  const AcceptAddressChangeRequestParams({
    required this.requestId,
  });

  @override
  List<Object?> get props => [requestId];
}

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../repositories/user_repository.dart';

/// ForgotPassword use case
/// Following Single Responsibility Principle by focusing only on forgot password functionality
class ForgotPassword implements UseCase<bool, ForgotPasswordParams> {
  final UserRepository repository;

  ForgotPassword(this.repository);

  @override
  Future<Either<Failure, bool>> call(ForgotPasswordParams params) async {
    return await repository.forgotPassword(params.email);
  }
}

/// ForgotPasswordParams class for forgot password use case parameters
class ForgotPasswordParams extends Equatable {
  final String email;

  const ForgotPasswordParams({
    required this.email,
  });

  @override
  List<Object> get props => [email];
}

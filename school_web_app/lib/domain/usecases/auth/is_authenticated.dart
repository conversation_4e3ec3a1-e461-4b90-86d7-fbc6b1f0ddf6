import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../repositories/user_repository.dart';

/// IsAuthenticated use case
/// Following Single Responsibility Principle by focusing only on checking authentication status
class IsAuthenticated implements NoParamsUseCase<bool> {
  final UserRepository repository;

  IsAuthenticated(this.repository);

  @override
  Future<Either<Failure, bool>> call() async {
    return await repository.isAuthenticated();
  }
}

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/user.dart';
import '../../repositories/user_repository.dart';

/// RegisterUser use case
/// Following Single Responsibility Principle by focusing only on user registration
class RegisterUser implements UseCase<User, RegisterParams> {
  final UserRepository repository;

  RegisterUser(this.repository);

  @override
  Future<Either<Failure, User>> call(RegisterParams params) async {
    return await repository.register(params.user, params.password);
  }
}

/// RegisterParams class for register use case parameters
class RegisterParams extends Equatable {
  final User user;
  final String password;

  const RegisterParams({
    required this.user,
    required this.password,
  });

  @override
  List<Object> get props => [user, password];
}

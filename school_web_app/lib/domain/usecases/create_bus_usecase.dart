import 'package:dartz/dartz.dart';

import '../entities/bus.dart';
import '../repositories/bus_repository.dart';
import '../../core/errors/failures.dart';

/// Create bus use case
/// Following Single Responsibility Principle by focusing only on creating buses
class CreateBusUseCase {
  final BusRepository _repository;

  CreateBusUseCase(this._repository);

  Future<Either<Failure, Bus>> call(Bus bus) async {
    return await _repository.createBus(bus);
  }
}

import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/trip.dart';
import '../../repositories/dashboard_repository.dart';

/// GetUpcomingTrips use case
/// Following Single Responsibility Principle by focusing only on getting upcoming trips
class GetUpcomingTrips implements NoParamsUseCase<List<Trip>> {
  final DashboardRepository repository;

  GetUpcomingTrips(this.repository);

  @override
  Future<Either<Failure, List<Trip>>> call() async {
    return await repository.getUpcomingTrips();
  }
}

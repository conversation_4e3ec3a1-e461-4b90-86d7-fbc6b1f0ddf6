import 'package:dartz/dartz.dart';

import '../repositories/bus_repository.dart';
import '../../core/errors/failures.dart';

/// Delete bus use case
/// Following Single Responsibility Principle by focusing only on deleting buses
class DeleteBusUseCase {
  final BusRepository _repository;

  DeleteBusUseCase(this._repository);

  Future<Either<Failure, bool>> call(int id) async {
    return await _repository.deleteBus(id);
  }
}

import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../repositories/file_repository.dart';

/// Download Students File Use Case
/// Following Single Responsibility Principle by focusing only on downloading students file
class DownloadStudentsFileUseCase {
  final FileRepository repository;

  DownloadStudentsFileUseCase(this.repository);

  Future<Either<Failure, String>> call() async {
    return await repository.downloadStudentsExampleFile();
  }
}

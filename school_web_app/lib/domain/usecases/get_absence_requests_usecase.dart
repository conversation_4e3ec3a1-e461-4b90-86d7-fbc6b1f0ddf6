import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../entities/absence_request.dart';
import '../repositories/absence_repository.dart';

/// Use case for getting absence requests
class GetAbsenceRequestsUseCase
    implements UseCase<List<AbsenceRequest>, GetAbsenceRequestsParams> {
  final AbsenceRepository repository;

  GetAbsenceRequestsUseCase(this.repository);

  @override
  Future<Either<Failure, List<AbsenceRequest>>> call(
    GetAbsenceRequestsParams params,
  ) async {
    return await repository.getAbsenceRequests(
      page: params.page,
      limit: params.limit,
      busId: params.busId,
      attendanceType: params.attendanceType,
      studentName: params.studentName,
      date: params.date,
    );
  }
}

/// Parameters for GetAbsenceRequestsUseCase
class GetAbsenceRequestsParams extends Equatable {
  final int page;
  final int limit;
  final String? busId;
  final String? attendanceType;
  final String? studentName;
  final String? date;

  const GetAbsenceRequestsParams({
    this.page = 1,
    this.limit = 10,
    this.busId,
    this.attendanceType,
    this.studentName,
    this.date,
  });

  @override
  List<Object?> get props => [
        page,
        limit,
        busId,
        attendanceType,
        studentName,
        date,
      ];
}

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../entities/address_change_request.dart';
import '../repositories/address_change_repository.dart';

/// Use case for getting address change requests
class GetAddressChangeRequestsUseCase
    implements UseCase<List<AddressChangeRequest>, GetAddressChangeRequestsParams> {
  final AddressChangeRepository repository;

  GetAddressChangeRequestsUseCase(this.repository);

  @override
  Future<Either<Failure, List<AddressChangeRequest>>> call(
    GetAddressChangeRequestsParams params,
  ) async {
    return await repository.getAddressChangeRequests(
      page: params.page,
      limit: params.limit,
      status: params.status,
      search: params.search,
    );
  }
}

/// Parameters for GetAddressChangeRequestsUseCase
class GetAddressChangeRequestsParams extends Equatable {
  final int page;
  final int limit;
  final String? status;
  final String? search;

  const GetAddressChangeRequestsParams({
    this.page = 1,
    this.limit = 10,
    this.status,
    this.search,
  });

  @override
  List<Object?> get props => [page, limit, status, search];
}

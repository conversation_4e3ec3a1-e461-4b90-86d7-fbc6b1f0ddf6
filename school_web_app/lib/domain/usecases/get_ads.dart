import 'package:dartz/dartz.dart';

import '../../core/errors/failures.dart';
import '../entities/ad.dart';
import '../repositories/ad_repository.dart';

/// GetAds use case
/// Following Single Responsibility Principle by focusing only on getting ads
class GetAds {
  final AdRepository repository;

  GetAds(this.repository);

  /// Call method to execute the use case
  Future<Either<Failure, List<Ad>>> call() async {
    return await repository.getAds();
  }
}

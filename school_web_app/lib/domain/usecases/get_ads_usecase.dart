import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/ad.dart';
import '../repositories/ads_repository.dart';

/// Get Ads Use Case
/// Following Single Responsibility Principle by focusing only on getting ads
class GetAdsUseCase {
  final AdsRepository repository;

  GetAdsUseCase(this.repository);

  Future<Either<Failure, List<Ad>>> call() async {
    return await repository.getAds();
  }
}

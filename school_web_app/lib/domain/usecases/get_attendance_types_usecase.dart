import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../repositories/absence_repository.dart';

/// Use case for getting attendance types
class GetAttendanceTypesUseCase implements UseCase<List<String>, NoParams> {
  final AbsenceRepository repository;

  GetAttendanceTypesUseCase(this.repository);

  @override
  Future<Either<Failure, List<String>>> call(NoParams params) async {
    return await repository.getAttendanceTypes();
  }
}

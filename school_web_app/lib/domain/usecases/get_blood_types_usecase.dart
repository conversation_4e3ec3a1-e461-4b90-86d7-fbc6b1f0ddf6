import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../repositories/settings_repository.dart';

/// Get Blood Types Use Case
/// Following Single Responsibility Principle by focusing only on getting blood types
class GetBloodTypesUseCase {
  final SettingsRepository repository;

  GetBloodTypesUseCase(this.repository);

  Future<Either<Failure, List<Map<String, dynamic>>>> call() async {
    return await repository.getBloodTypes();
  }
}

import 'package:dartz/dartz.dart';

import '../entities/bus.dart';
import '../repositories/bus_repository.dart';
import '../../core/errors/failures.dart';

/// Get bus by ID use case
/// Following Single Responsibility Principle by focusing only on getting bus details
class GetBusByIdUseCase {
  final BusRepository _repository;

  GetBusByIdUseCase(this._repository);

  Future<Either<Failure, Bus>> call(int id) async {
    return await _repository.getBusById(id);
  }
}

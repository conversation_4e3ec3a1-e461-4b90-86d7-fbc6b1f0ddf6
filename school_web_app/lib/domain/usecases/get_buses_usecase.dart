import 'package:dartz/dartz.dart';

import '../entities/bus.dart';
import '../repositories/bus_repository.dart';
import '../../core/errors/failures.dart';

/// Get buses use case
/// Following Single Responsibility Principle by focusing only on getting buses
class GetBusesUseCase {
  final BusRepository _repository;

  GetBusesUseCase(this._repository);

  Future<Either<Failure, List<Bus>>> call({
    int page = 1,
    int perPage = 10,
    String? search,
  }) async {
    return await _repository.getBuses(
      page: page,
      perPage: perPage,
      search: search,
    );
  }
}

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../entities/driver.dart';
import '../repositories/driver_repository.dart';

/// Get drivers use case
/// Following Single Responsibility Principle by focusing only on getting drivers
class GetDriversUseCase implements UseCase<List<Driver>, GetDriversParams> {
  final DriverRepository repository;

  GetDriversUseCase(this.repository);

  @override
  Future<Either<Failure, List<Driver>>> call(GetDriversParams params) async {
    return repository.getDrivers(
      page: params.page,
      limit: params.limit,
      search: params.search,
    );
  }
}

/// Parameters for GetDriversUseCase
class GetDriversParams extends Equatable {
  final int page;
  final int limit;
  final String? search;

  const GetDriversParams({
    this.page = 1,
    this.limit = 100, // Get all drivers for dropdown
    this.search,
  });

  @override
  List<Object?> get props => [page, limit, search];
}

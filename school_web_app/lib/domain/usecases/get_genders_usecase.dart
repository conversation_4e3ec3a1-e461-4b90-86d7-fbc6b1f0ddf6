import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../repositories/settings_repository.dart';

/// Get Genders Use Case
/// Following Single Responsibility Principle by focusing only on getting genders
class GetGendersUseCase {
  final SettingsRepository repository;

  GetGendersUseCase(this.repository);

  Future<Either<Failure, List<Map<String, dynamic>>>> call() async {
    return await repository.getGenders();
  }
}

import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../repositories/settings_repository.dart';

/// Get Grades Use Case
/// Following Single Responsibility Principle by focusing only on getting grades
class GetGradesUseCase {
  final SettingsRepository repository;

  GetGradesUseCase(this.repository);

  Future<Either<Failure, List<Map<String, dynamic>>>> call() async {
    return await repository.getGrades();
  }
}

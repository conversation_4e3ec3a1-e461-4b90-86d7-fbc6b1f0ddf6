import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../repositories/help_repository.dart';

/// Get Help Questions Use Case
/// Following Single Responsibility Principle by focusing only on getting help questions
class GetHelpQuestionsUseCase {
  final HelpRepository repository;

  GetHelpQuestionsUseCase(this.repository);

  Future<Either<Failure, List<Map<String, dynamic>>>> call() async {
    return await repository.getHelpQuestions();
  }
}

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../entities/notification.dart';
import '../repositories/notification_repository.dart';

/// Use case for getting notifications
class GetNotificationsUseCase
    implements UseCase<List<NotificationEntity>, GetNotificationsParams> {
  final NotificationRepository repository;

  GetNotificationsUseCase(this.repository);

  @override
  Future<Either<Failure, List<NotificationEntity>>> call(
    GetNotificationsParams params,
  ) async {
    return await repository.getNotifications(
      page: params.page,
      limit: params.limit,
      isRead: params.isRead,
      type: params.type,
    );
  }
}

/// Parameters for GetNotificationsUseCase
class GetNotificationsParams extends Equatable {
  final int page;
  final int limit;
  final bool? isRead;
  final String? type;

  const GetNotificationsParams({
    this.page = 1,
    this.limit = 20,
    this.isRead,
    this.type,
  });

  @override
  List<Object?> get props => [page, limit, isRead, type];
}

import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../entities/parent.dart';
import '../repositories/parent_repository.dart';

/// Get parents use case
/// Following Clean Architecture principles
class GetParentsUseCase implements UseCase<List<Parent>, GetParentsParams> {
  final ParentRepository repository;

  GetParentsUseCase(this.repository);

  @override
  Future<Either<Failure, List<Parent>>> call(GetParentsParams params) async {
    return await repository.getParents(
      page: params.page,
      limit: params.limit,
      search: params.search,
    );
  }
}

/// Parameters for GetParentsUseCase
class GetParentsParams {
  final int page;
  final int limit;
  final String? search;

  const GetParentsParams({this.page = 1, this.limit = 10, this.search});
}

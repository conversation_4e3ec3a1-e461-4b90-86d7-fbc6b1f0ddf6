import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../repositories/settings_repository.dart';

/// Get Religions Use Case
/// Following Single Responsibility Principle by focusing only on getting religions
class GetReligionsUseCase {
  final SettingsRepository repository;

  GetReligionsUseCase(this.repository);

  Future<Either<Failure, List<Map<String, dynamic>>>> call() async {
    return await repository.getReligions();
  }
}

import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../repositories/parent_repository.dart';

/// Get students by parent ID use case
/// Following Clean Architecture principles
class GetStudentsByParentIdUseCase implements UseCase<List<int>, int> {
  final ParentRepository repository;

  GetStudentsByParentIdUseCase(this.repository);

  @override
  Future<Either<Failure, List<int>>> call(int params) async {
    return await repository.getStudentsByParentId(params);
  }
}

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../entities/address_change_request.dart';
import '../repositories/address_change_repository.dart';

/// Use case for getting temporary addresses
class GetTemporaryAddressesUseCase
    implements UseCase<List<AddressChangeRequest>, GetTemporaryAddressesParams> {
  final AddressChangeRepository repository;

  GetTemporaryAddressesUseCase(this.repository);

  @override
  Future<Either<Failure, List<AddressChangeRequest>>> call(
    GetTemporaryAddressesParams params,
  ) async {
    return await repository.getTemporaryAddresses(
      page: params.page,
      limit: params.limit,
      search: params.search,
    );
  }
}

/// Parameters for GetTemporaryAddressesUseCase
class GetTemporaryAddressesParams extends Equatable {
  final int page;
  final int limit;
  final String? search;

  const GetTemporaryAddressesParams({
    this.page = 1,
    this.limit = 10,
    this.search,
  });

  @override
  List<Object?> get props => [page, limit, search];
}

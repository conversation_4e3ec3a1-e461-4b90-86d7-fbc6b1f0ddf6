import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../entities/trip.dart';
import '../repositories/trip_repository.dart';

/// GetTripById use case
/// Following Single Responsibility Principle by focusing only on getting trip by ID
class GetTripById implements UseCase<Trip, String> {
  final TripRepository repository;

  GetTripById(this.repository);

  @override
  Future<Either<Failure, Trip>> call(String tripId) async {
    return await repository.getTripById(tripId);
  }
}

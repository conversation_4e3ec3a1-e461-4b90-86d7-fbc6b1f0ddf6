import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../repositories/notification_repository.dart';

/// Use case for getting unread notifications count
class GetUnreadNotificationsCountUseCase implements UseCase<int, NoParams> {
  final NotificationRepository repository;

  GetUnreadNotificationsCountUseCase(this.repository);

  @override
  Future<Either<Failure, int>> call(NoParams params) async {
    return await repository.getUnreadCount();
  }
}

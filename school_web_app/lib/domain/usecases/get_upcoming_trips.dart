import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../entities/trip.dart';
import '../repositories/trip_repository.dart';

/// GetUpcomingTripsUseCase use case
/// Following Single Responsibility Principle by focusing only on getting upcoming trips
class GetUpcomingTripsUseCase implements NoParamsUseCase<List<Trip>> {
  final TripRepository repository;

  GetUpcomingTripsUseCase(this.repository);

  @override
  Future<Either<Failure, List<Trip>>> call() async {
    return await repository.getUpcomingTrips();
  }
}

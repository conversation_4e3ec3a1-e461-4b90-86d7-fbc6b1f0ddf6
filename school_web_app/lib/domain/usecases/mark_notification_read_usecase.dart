import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../repositories/notification_repository.dart';

/// Use case for marking notification as read
class MarkNotificationReadUseCase
    implements UseCase<bool, MarkNotificationReadParams> {
  final NotificationRepository repository;

  MarkNotificationReadUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(
    MarkNotificationReadParams params,
  ) async {
    return await repository.markAsRead(params.notificationId);
  }
}

/// Parameters for MarkNotificationReadUseCase
class MarkNotificationReadParams extends Equatable {
  final int notificationId;

  const MarkNotificationReadParams({
    required this.notificationId,
  });

  @override
  List<Object?> get props => [notificationId];
}

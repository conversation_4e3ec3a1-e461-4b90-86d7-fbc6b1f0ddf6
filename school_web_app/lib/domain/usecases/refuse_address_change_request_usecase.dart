import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../repositories/address_change_repository.dart';

/// Use case for refusing address change requests
class RefuseAddressChangeRequestUseCase
    implements UseCase<bool, RefuseAddressChangeRequestParams> {
  final AddressChangeRepository repository;

  RefuseAddressChangeRequestUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(
    RefuseAddressChangeRequestParams params,
  ) async {
    return await repository.refuseAddressChangeRequest(
      requestId: params.requestId,
      reason: params.reason,
    );
  }
}

/// Parameters for RefuseAddressChangeRequestUseCase
class RefuseAddressChangeRequestParams extends Equatable {
  final int requestId;
  final String reason;

  const RefuseAddressChangeRequestParams({
    required this.requestId,
    required this.reason,
  });

  @override
  List<Object?> get props => [requestId, reason];
}

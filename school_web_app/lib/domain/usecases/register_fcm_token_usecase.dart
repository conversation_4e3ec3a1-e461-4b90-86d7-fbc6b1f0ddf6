import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../repositories/notification_repository.dart';

/// Use case for registering FCM token
class RegisterFCMTokenUseCase implements UseCase<bool, RegisterFCMTokenParams> {
  final NotificationRepository repository;

  RegisterFCMTokenUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(RegisterFCMTokenParams params) async {
    return await repository.registerFCMToken(
      token: params.token,
      deviceType: params.deviceType,
      deviceId: params.deviceId,
    );
  }
}

/// Parameters for RegisterFCMTokenUseCase
class RegisterFCMTokenParams extends Equatable {
  final String token;
  final String deviceType;
  final String deviceId;

  const RegisterFCMTokenParams({
    required this.token,
    required this.deviceType,
    required this.deviceId,
  });

  @override
  List<Object?> get props => [token, deviceType, deviceId];
}

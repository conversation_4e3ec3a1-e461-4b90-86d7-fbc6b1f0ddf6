import 'package:dartz/dartz.dart';
import '../../entities/trip.dart';
import '../../repositories/trip_repository.dart';
import '../../../core/errors/failures.dart';
import '../../../core/utils/logger.dart';

/// Start trip use case
/// Following Single Responsibility Principle by focusing only on trip starting
class StartTripUseCase {
  final TripRepository repository;

  StartTripUseCase(this.repository);

  Future<Either<Failure, Trip>> call(String tripId) async {
    try {
      LoggerService.debug('Starting trip', data: {'tripId': tripId});

      if (tripId.trim().isEmpty) {
        return Left(ValidationFailure(message: 'معرف الرحلة مطلوب'));
      }

      final result = await repository.startTrip(tripId);

      return result.fold(
        (failure) {
          LoggerService.error('Failed to start trip', error: failure);
          return Left(failure);
        },
        (startedTrip) {
          LoggerService.info(
            'Trip started successfully',
            data: {'tripId': startedTrip.id, 'status': startedTrip.status},
          );
          return Right(startedTrip);
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error starting trip', error: e);
      return Left(ServerFailure(message: e.toString()));
    }
  }
}

/// Validation failure class
class ValidationFailure extends Failure {
  const ValidationFailure({required super.message});
}

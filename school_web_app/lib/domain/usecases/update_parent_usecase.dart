import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../entities/parent.dart';
import '../repositories/parent_repository.dart';

/// Update parent use case
/// Following Clean Architecture principles
class UpdateParentUseCase implements UseCase<Parent, Parent> {
  final ParentRepository repository;

  UpdateParentUseCase(this.repository);

  @override
  Future<Either<Failure, Parent>> call(Parent params) async {
    return await repository.updateParent(params);
  }
}

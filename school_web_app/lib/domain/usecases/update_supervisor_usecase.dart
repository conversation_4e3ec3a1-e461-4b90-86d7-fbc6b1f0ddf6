import 'package:dartz/dartz.dart';
import '../entities/supervisor.dart';
import '../repositories/supervisor_repository.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';

/// Use case for updating supervisor
/// Following Single Responsibility Principle by focusing only on updating supervisor
class UpdateSupervisorUseCase implements UseCase<Supervisor, Supervisor> {
  final SupervisorRepository repository;

  UpdateSupervisorUseCase(this.repository);

  @override
  Future<Either<Failure, Supervisor>> call(Supervisor supervisor) {
    return repository.updateSupervisor(supervisor);
  }
}

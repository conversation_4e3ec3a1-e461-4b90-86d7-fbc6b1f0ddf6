import 'package:get/get.dart';
import '../../domain/usecases/get_absence_requests_usecase.dart';
import '../../domain/usecases/get_attendance_types_usecase.dart';
import '../controllers/absence_controller.dart';

/// Binding for absence feature
class AbsenceBinding extends Bindings {
  @override
  void dependencies() {
    // Controller - use existing dependencies from DI
    Get.lazyPut(
      () => AbsenceController(
        getAbsenceRequestsUseCase: Get.find<GetAbsenceRequestsUseCase>(),
        getAttendanceTypesUseCase: Get.find<GetAttendanceTypesUseCase>(),
      ),
    );
  }
}

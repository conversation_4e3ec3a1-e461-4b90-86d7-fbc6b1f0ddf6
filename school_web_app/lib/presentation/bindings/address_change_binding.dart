import 'package:get/get.dart';
import '../../domain/usecases/get_address_change_requests_usecase.dart';
import '../../domain/usecases/accept_address_change_request_usecase.dart';
import '../../domain/usecases/refuse_address_change_request_usecase.dart';
import '../../domain/usecases/get_temporary_addresses_usecase.dart';
import '../controllers/address_change_controller.dart';

/// Binding for address change feature
class AddressChangeBinding extends Bindings {
  @override
  void dependencies() {
    // Controller - use existing dependencies from DI
    Get.lazyPut(
      () => AddressChangeController(
        getAddressChangeRequestsUseCase:
            Get.find<GetAddressChangeRequestsUseCase>(),
        acceptAddressChangeRequestUseCase:
            Get.find<AcceptAddressChangeRequestUseCase>(),
        refuseAddressChangeRequestUseCase:
            Get.find<RefuseAddressChangeRequestUseCase>(),
        getTemporaryAddressesUseCase: Get.find<GetTemporaryAddressesUseCase>(),
      ),
    );
  }
}

import 'package:get/get.dart';
import '../../domain/repositories/dashboard_repository.dart';
import '../../domain/usecases/dashboard/get_dashboard_stats.dart';
import '../../domain/usecases/dashboard/get_recent_trips.dart';
import '../../domain/usecases/dashboard/get_school_stats.dart';
import '../../domain/usecases/dashboard/get_upcoming_trips.dart';
import '../controllers/dashboard_controller.dart';

/// DashboardBinding class for binding dashboard dependencies
/// Following Dependency Inversion Principle by injecting dependencies
class DashboardBinding extends Bindings {
  @override
  void dependencies() {
    // Use cases
    Get.lazyPut(() => GetDashboardStats(Get.find<DashboardRepository>()), fenix: true);
    Get.lazyPut(() => GetSchoolStats(Get.find<DashboardRepository>()), fenix: true);
    Get.lazyPut(() => GetRecentTrips(Get.find<DashboardRepository>()), fenix: true);
    Get.lazyPut(() => GetUpcomingTrips(Get.find<DashboardRepository>()), fenix: true);
    
    // Controller
    Get.lazyPut(
      () => DashboardController(
        getDashboardStatsUseCase: Get.find<GetDashboardStats>(),
        getSchoolStatsUseCase: Get.find<GetSchoolStats>(),
        getRecentTripsUseCase: Get.find<GetRecentTrips>(),
        getUpcomingTripsUseCase: Get.find<GetUpcomingTrips>(),
      ),
      fenix: true,
    );
  }
}

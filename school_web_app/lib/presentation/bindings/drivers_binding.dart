import 'package:get/get.dart';
import '../../core/network/api_service.dart';
import '../../data/repositories/driver_repository_impl.dart';
import '../../domain/repositories/driver_repository.dart';
import '../controllers/drivers_controller.dart';

/// Drivers binding for dependency injection
/// Following Single Responsibility Principle by focusing only on drivers dependencies
class DriversBinding extends Bindings {
  @override
  void dependencies() {
    // Register driver repository
    Get.lazyPut<DriverRepository>(
      () => DriverRepositoryImpl(
        apiService: Get.find<ApiService>(),
      ),
    );

    // Register drivers controller
    Get.lazyPut<DriversController>(
      () => DriversController(
        driverRepository: Get.find<DriverRepository>(),
      ),
    );
  }
}

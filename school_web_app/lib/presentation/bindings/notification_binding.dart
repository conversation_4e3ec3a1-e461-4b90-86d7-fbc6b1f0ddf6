import 'package:get/get.dart';
import '../../core/network/api_service.dart';
import '../../core/services/notification_service.dart';
import '../../data/repositories/notification_repository_impl.dart';
import '../../domain/repositories/notification_repository.dart';
import '../../domain/usecases/get_notifications_usecase.dart';
import '../../domain/usecases/mark_notification_read_usecase.dart';
import '../../domain/usecases/get_unread_notifications_count_usecase.dart';
import '../../domain/usecases/register_fcm_token_usecase.dart';
import '../controllers/notification_controller.dart';

/// Binding for notification feature
class NotificationBinding extends Bindings {
  @override
  void dependencies() {
    // Service
    Get.lazyPut<NotificationService>(
      () => NotificationService(),
      fenix: true,
    );

    // Repository
    Get.lazyPut<NotificationRepository>(
      () => NotificationRepositoryImpl(Get.find<ApiService>()),
    );

    // Use cases
    Get.lazyPut(
      () => GetNotificationsUseCase(Get.find<NotificationRepository>()),
    );
    
    Get.lazyPut(
      () => MarkNotificationReadUseCase(Get.find<NotificationRepository>()),
    );
    
    Get.lazyPut(
      () => GetUnreadNotificationsCountUseCase(Get.find<NotificationRepository>()),
    );
    
    Get.lazyPut(
      () => RegisterFCMTokenUseCase(Get.find<NotificationRepository>()),
    );

    // Controller
    Get.lazyPut(
      () => NotificationController(
        getNotificationsUseCase: Get.find<GetNotificationsUseCase>(),
        markNotificationReadUseCase: Get.find<MarkNotificationReadUseCase>(),
        getUnreadNotificationsCountUseCase: Get.find<GetUnreadNotificationsCountUseCase>(),
        registerFCMTokenUseCase: Get.find<RegisterFCMTokenUseCase>(),
      ),
    );
  }
}

import 'package:get/get.dart';
import '../../core/services/search_service.dart';
import '../controllers/search_controller.dart' as app_search;

/// Binding for search feature
class SearchBinding extends Bindings {
  @override
  void dependencies() {
    // Service
    Get.lazyPut<SearchService>(() => SearchService(), fenix: true);

    // Controller
    Get.lazyPut(() => app_search.SearchController(), fenix: true);
  }
}

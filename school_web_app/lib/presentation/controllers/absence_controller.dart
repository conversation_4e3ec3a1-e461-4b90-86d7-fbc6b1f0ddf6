import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/usecases/usecase.dart';
import '../../domain/entities/absence_request.dart';
import '../../domain/usecases/get_absence_requests_usecase.dart';
import '../../domain/usecases/get_attendance_types_usecase.dart';

/// Controller for managing absence requests
class Absence<PERSON>ontroller extends GetxController {
  final GetAbsenceRequestsUseCase _getAbsenceRequestsUseCase;
  final GetAttendanceTypesUseCase _getAttendanceTypesUseCase;

  AbsenceController({
    required GetAbsenceRequestsUseCase getAbsenceRequestsUseCase,
    required GetAttendanceTypesUseCase getAttendanceTypesUseCase,
  }) : _getAbsenceRequestsUseCase = getAbsenceRequestsUseCase,
       _getAttendanceTypesUseCase = getAttendanceTypesUseCase;

  // Observable variables
  final _isLoading = false.obs;
  final _absenceRequests = <AbsenceRequest>[].obs;
  final _attendanceTypes = <String>[].obs;
  final _currentPage = 1.obs;
  final _hasMoreData = true.obs;
  final _searchQuery = ''.obs;
  final _selectedBusId = ''.obs;
  final _selectedAttendanceType = ''.obs;
  final _selectedDate = ''.obs;
  final _errorMessage = ''.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  List<AbsenceRequest> get absenceRequests => _absenceRequests;
  List<String> get attendanceTypes => _attendanceTypes;
  int get currentPage => _currentPage.value;
  bool get hasMoreData => _hasMoreData.value;
  String get searchQuery => _searchQuery.value;
  String get selectedBusId => _selectedBusId.value;
  String get selectedAttendanceType => _selectedAttendanceType.value;
  String get selectedDate => _selectedDate.value;
  String get errorMessage => _errorMessage.value;

  @override
  void onInit() {
    super.onInit();
    loadAttendanceTypes();
    loadAbsenceRequests();
  }

  /// Load attendance types
  Future<void> loadAttendanceTypes() async {
    try {
      final result = await _getAttendanceTypesUseCase(NoParams());

      result.fold(
        (failure) {
          // Use default types if API fails
          _attendanceTypes.assignAll([
            'حاضر',
            'غائب',
            'متأخر',
            'مريض',
            'إجازة',
          ]);
        },
        (types) {
          _attendanceTypes.assignAll(types);
        },
      );
    } catch (e) {
      // Use default types if error occurs
      _attendanceTypes.assignAll(['حاضر', 'غائب', 'متأخر', 'مريض', 'إجازة']);
    }
  }

  /// Load absence requests
  Future<void> loadAbsenceRequests({bool refresh = false}) async {
    try {
      if (refresh) {
        _currentPage.value = 1;
        _hasMoreData.value = true;
        _absenceRequests.clear();
      }

      if (!_hasMoreData.value && !refresh) return;

      _isLoading.value = true;
      _errorMessage.value = '';

      final result = await _getAbsenceRequestsUseCase(
        GetAbsenceRequestsParams(
          page: _currentPage.value,
          limit: 10,
          busId: _selectedBusId.value.isNotEmpty ? _selectedBusId.value : null,
          attendanceType:
              _selectedAttendanceType.value.isNotEmpty
                  ? _selectedAttendanceType.value
                  : null,
          studentName:
              _searchQuery.value.isNotEmpty ? _searchQuery.value : null,
          date: _selectedDate.value.isNotEmpty ? _selectedDate.value : null,
        ),
      );

      result.fold(
        (failure) {
          _errorMessage.value =
              failure.message.isNotEmpty
                  ? failure.message
                  : 'حدث خطأ في تحميل البيانات';
        },
        (requests) {
          if (refresh) {
            _absenceRequests.assignAll(requests);
          } else {
            _absenceRequests.addAll(requests);
          }

          _currentPage.value++;
          _hasMoreData.value = requests.length >= 10;
        },
      );
    } catch (e) {
      _errorMessage.value = 'حدث خطأ غير متوقع: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  /// Search absence requests
  void searchRequests(String query) {
    _searchQuery.value = query;
    loadAbsenceRequests(refresh: true);
  }

  /// Filter by bus
  void filterByBus(String busId) {
    _selectedBusId.value = busId;
    loadAbsenceRequests(refresh: true);
  }

  /// Filter by attendance type
  void filterByAttendanceType(String attendanceType) {
    _selectedAttendanceType.value = attendanceType;
    loadAbsenceRequests(refresh: true);
  }

  /// Filter by date
  void filterByDate(String date) {
    _selectedDate.value = date;
    loadAbsenceRequests(refresh: true);
  }

  /// Clear filters
  void clearFilters() {
    _searchQuery.value = '';
    _selectedBusId.value = '';
    _selectedAttendanceType.value = '';
    _selectedDate.value = '';
    loadAbsenceRequests(refresh: true);
  }

  /// Load more data (pagination)
  Future<void> loadMoreData() async {
    if (!_hasMoreData.value || _isLoading.value) return;
    await loadAbsenceRequests();
  }

  /// Get attendance type display name
  String getAttendanceTypeDisplayName(String? type) {
    if (type == null || type.isEmpty) return 'غير محدد';

    switch (type.toLowerCase()) {
      case 'present':
      case 'حاضر':
        return 'حاضر';
      case 'absent':
      case 'غائب':
        return 'غائب';
      case 'late':
      case 'متأخر':
        return 'متأخر';
      case 'sick':
      case 'مريض':
        return 'مريض';
      case 'leave':
      case 'إجازة':
        return 'إجازة';
      default:
        return type;
    }
  }

  /// Get attendance type color
  Color getAttendanceTypeColor(String? type) {
    if (type == null || type.isEmpty) return Colors.grey;

    switch (type.toLowerCase()) {
      case 'present':
      case 'حاضر':
        return Colors.green;
      case 'absent':
      case 'غائب':
        return Colors.red;
      case 'late':
      case 'متأخر':
        return Colors.orange;
      case 'sick':
      case 'مريض':
        return Colors.purple;
      case 'leave':
      case 'إجازة':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  /// Format date for display
  String formatDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return 'غير محدد';

    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  /// Approve absence request
  Future<void> approveAbsenceRequest(int requestId) async {
    try {
      _isLoading.value = true;

      // For now, just show success message
      // TODO: Implement actual API call when backend is ready
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      Get.snackbar(
        'نجح',
        'تم قبول طلب الغياب بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      // Update the request status locally
      final requestIndex = _absenceRequests.indexWhere(
        (req) => req.id == requestId,
      );
      if (requestIndex != -1) {
        final updatedRequest = AbsenceRequest(
          id: _absenceRequests[requestIndex].id,
          gradeId: _absenceRequests[requestIndex].gradeId,
          schoolId: _absenceRequests[requestIndex].schoolId,
          classroomId: _absenceRequests[requestIndex].classroomId,
          busId: _absenceRequests[requestIndex].busId,
          parentId: _absenceRequests[requestIndex].parentId,
          studentId: _absenceRequests[requestIndex].studentId,
          attendanceDate: _absenceRequests[requestIndex].attendanceDate,
          attendanceType: _absenceRequests[requestIndex].attendanceType,
          status: 'approved',
          createdAt: _absenceRequests[requestIndex].createdAt,
          updatedAt: _absenceRequests[requestIndex].updatedAt,
          school: _absenceRequests[requestIndex].school,
          bus: _absenceRequests[requestIndex].bus,
          grade: _absenceRequests[requestIndex].grade,
          classroom: _absenceRequests[requestIndex].classroom,
          parent: _absenceRequests[requestIndex].parent,
          student: _absenceRequests[requestIndex].student,
        );
        _absenceRequests[requestIndex] = updatedRequest;
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء قبول الطلب',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Reject absence request
  Future<void> rejectAbsenceRequest(int requestId) async {
    try {
      _isLoading.value = true;

      // For now, just show success message
      // TODO: Implement actual API call when backend is ready
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      Get.snackbar(
        'نجح',
        'تم رفض طلب الغياب بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      // Update the request status locally
      final requestIndex = _absenceRequests.indexWhere(
        (req) => req.id == requestId,
      );
      if (requestIndex != -1) {
        final updatedRequest = AbsenceRequest(
          id: _absenceRequests[requestIndex].id,
          gradeId: _absenceRequests[requestIndex].gradeId,
          schoolId: _absenceRequests[requestIndex].schoolId,
          classroomId: _absenceRequests[requestIndex].classroomId,
          busId: _absenceRequests[requestIndex].busId,
          parentId: _absenceRequests[requestIndex].parentId,
          studentId: _absenceRequests[requestIndex].studentId,
          attendanceDate: _absenceRequests[requestIndex].attendanceDate,
          attendanceType: _absenceRequests[requestIndex].attendanceType,
          status: 'rejected',
          createdAt: _absenceRequests[requestIndex].createdAt,
          updatedAt: _absenceRequests[requestIndex].updatedAt,
          school: _absenceRequests[requestIndex].school,
          bus: _absenceRequests[requestIndex].bus,
          grade: _absenceRequests[requestIndex].grade,
          classroom: _absenceRequests[requestIndex].classroom,
          parent: _absenceRequests[requestIndex].parent,
          student: _absenceRequests[requestIndex].student,
        );
        _absenceRequests[requestIndex] = updatedRequest;
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء رفض الطلب',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }
}

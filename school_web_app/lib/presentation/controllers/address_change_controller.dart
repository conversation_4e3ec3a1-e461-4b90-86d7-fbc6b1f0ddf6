import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../domain/entities/address_change_request.dart';
import '../../domain/usecases/get_address_change_requests_usecase.dart';
import '../../domain/usecases/accept_address_change_request_usecase.dart';
import '../../domain/usecases/refuse_address_change_request_usecase.dart';
import '../../domain/usecases/get_temporary_addresses_usecase.dart';

/// Controller for managing address change requests
class AddressChangeController extends GetxController {
  final GetAddressChangeRequestsUseCase _getAddressChangeRequestsUseCase;
  final AcceptAddressChangeRequestUseCase _acceptAddressChangeRequestUseCase;
  final RefuseAddressChangeRequestUseCase _refuseAddressChangeRequestUseCase;
  final GetTemporaryAddressesUseCase _getTemporaryAddressesUseCase;

  AddressChangeController({
    required GetAddressChangeRequestsUseCase getAddressChangeRequestsUseCase,
    required AcceptAddressChangeRequestUseCase
    acceptAddressChangeRequestUseCase,
    required RefuseAddressChangeRequestUseCase
    refuseAddressChangeRequestUseCase,
    required GetTemporaryAddressesUseCase getTemporaryAddressesUseCase,
  }) : _getAddressChangeRequestsUseCase = getAddressChangeRequestsUseCase,
       _acceptAddressChangeRequestUseCase = acceptAddressChangeRequestUseCase,
       _refuseAddressChangeRequestUseCase = refuseAddressChangeRequestUseCase,
       _getTemporaryAddressesUseCase = getTemporaryAddressesUseCase;

  // Observable variables
  final _isLoading = false.obs;
  final _addressChangeRequests = <AddressChangeRequest>[].obs;
  final _temporaryAddresses = <AddressChangeRequest>[].obs;
  final _currentPage = 1.obs;
  final _hasMoreData = true.obs;
  final _searchQuery = ''.obs;
  final _selectedStatus = ''.obs;
  final _errorMessage = ''.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  List<AddressChangeRequest> get addressChangeRequests =>
      _addressChangeRequests;
  List<AddressChangeRequest> get temporaryAddresses => _temporaryAddresses;
  int get currentPage => _currentPage.value;
  bool get hasMoreData => _hasMoreData.value;
  String get searchQuery => _searchQuery.value;
  String get selectedStatus => _selectedStatus.value;
  String get errorMessage => _errorMessage.value;

  @override
  void onInit() {
    super.onInit();
    loadAddressChangeRequests();
  }

  /// Load address change requests
  Future<void> loadAddressChangeRequests({bool refresh = false}) async {
    try {
      if (refresh) {
        _currentPage.value = 1;
        _hasMoreData.value = true;
        _addressChangeRequests.clear();
      }

      if (!_hasMoreData.value && !refresh) return;

      _isLoading.value = true;
      _errorMessage.value = '';

      final result = await _getAddressChangeRequestsUseCase(
        GetAddressChangeRequestsParams(
          page: _currentPage.value,
          limit: 10,
          status:
              _selectedStatus.value.isNotEmpty ? _selectedStatus.value : null,
          search: _searchQuery.value.isNotEmpty ? _searchQuery.value : null,
        ),
      );

      result.fold(
        (failure) {
          _errorMessage.value = failure.message;
        },
        (requests) {
          if (refresh) {
            _addressChangeRequests.assignAll(requests);
          } else {
            _addressChangeRequests.addAll(requests);
          }

          _currentPage.value++;
          _hasMoreData.value = requests.length >= 10;
        },
      );
    } catch (e) {
      _errorMessage.value = 'حدث خطأ غير متوقع';
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load temporary addresses
  Future<void> loadTemporaryAddresses({bool refresh = false}) async {
    try {
      if (refresh) {
        _currentPage.value = 1;
        _hasMoreData.value = true;
        _temporaryAddresses.clear();
      }

      if (!_hasMoreData.value && !refresh) return;

      _isLoading.value = true;
      _errorMessage.value = '';

      final result = await _getTemporaryAddressesUseCase(
        GetTemporaryAddressesParams(
          page: _currentPage.value,
          limit: 10,
          search: _searchQuery.value.isNotEmpty ? _searchQuery.value : null,
        ),
      );

      result.fold(
        (failure) {
          _errorMessage.value = failure.message;
        },
        (addresses) {
          if (refresh) {
            _temporaryAddresses.assignAll(addresses);
          } else {
            _temporaryAddresses.addAll(addresses);
          }

          _currentPage.value++;
          _hasMoreData.value = addresses.length >= 10;
        },
      );
    } catch (e) {
      _errorMessage.value = 'حدث خطأ غير متوقع';
    } finally {
      _isLoading.value = false;
    }
  }

  /// Accept address change request
  Future<void> acceptRequest(int requestId) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      final result = await _acceptAddressChangeRequestUseCase(
        AcceptAddressChangeRequestParams(requestId: requestId),
      );

      result.fold(
        (failure) {
          _errorMessage.value = failure.message;
          Get.snackbar(
            'خطأ',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        },
        (success) {
          if (success) {
            Get.snackbar(
              'نجح',
              'تم قبول طلب تغيير العنوان بنجاح',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.green,
              colorText: Colors.white,
            );

            // Refresh the list
            loadAddressChangeRequests(refresh: true);
          }
        },
      );
    } catch (e) {
      _errorMessage.value = 'حدث خطأ غير متوقع';
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Refuse address change request
  Future<void> refuseRequest(int requestId, String reason) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      final result = await _refuseAddressChangeRequestUseCase(
        RefuseAddressChangeRequestParams(requestId: requestId, reason: reason),
      );

      result.fold(
        (failure) {
          _errorMessage.value = failure.message;
          Get.snackbar(
            'خطأ',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        },
        (success) {
          if (success) {
            Get.snackbar(
              'نجح',
              'تم رفض طلب تغيير العنوان بنجاح',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.orange,
              colorText: Colors.white,
            );

            // Refresh the list
            loadAddressChangeRequests(refresh: true);
          }
        },
      );
    } catch (e) {
      _errorMessage.value = 'حدث خطأ غير متوقع';
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Search address change requests
  void searchRequests(String query) {
    _searchQuery.value = query;
    loadAddressChangeRequests(refresh: true);
  }

  /// Filter by status
  void filterByStatus(String status) {
    _selectedStatus.value = status;
    loadAddressChangeRequests(refresh: true);
  }

  /// Clear filters
  void clearFilters() {
    _searchQuery.value = '';
    _selectedStatus.value = '';
    loadAddressChangeRequests(refresh: true);
  }

  /// Load more data (pagination)
  Future<void> loadMoreData() async {
    if (!_hasMoreData.value || _isLoading.value) return;
    await loadAddressChangeRequests();
  }

  /// Get status color based on status value
  Color getStatusColor(int status) {
    switch (status) {
      case 1:
        return Colors.green; // Accepted
      case 2:
        return Colors.red; // Refused
      case 3:
        return Colors.orange; // Pending
      default:
        return Colors.grey;
    }
  }

  /// Get status text based on status value
  String getStatusText(int status) {
    switch (status) {
      case 1:
        return 'مقبول';
      case 2:
        return 'مرفوض';
      case 3:
        return 'في الانتظار';
      default:
        return 'غير محدد';
    }
  }
}

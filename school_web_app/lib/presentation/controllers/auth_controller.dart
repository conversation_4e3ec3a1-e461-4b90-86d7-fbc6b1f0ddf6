import 'package:get/get.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/user.dart';
import '../../domain/usecases/auth/change_password.dart';
import '../../domain/usecases/auth/forgot_password.dart';
import '../../domain/usecases/auth/get_current_user.dart';
import '../../domain/usecases/auth/is_authenticated.dart';
import '../../domain/usecases/auth/login_user.dart';
import '../../domain/usecases/auth/logout_user.dart';
import '../../domain/usecases/auth/register_user.dart';
import '../../domain/usecases/auth/reset_password.dart';

/// AuthController class for managing authentication state
/// Following Single Responsibility Principle by focusing only on authentication
class AuthController extends GetxController {
  final GetCurrentUser getCurrentUserUseCase;
  final LoginUser loginUserUseCase;
  final LogoutUser logoutUserUseCase;
  final RegisterUser registerUserUseCase;
  final ForgotPassword forgotPasswordUseCase;
  final ResetPassword resetPasswordUseCase;
  final IsAuthenticated isAuthenticatedUseCase;
  final ChangePassword changePasswordUseCase;

  AuthController({
    required this.getCurrentUserUseCase,
    required this.loginUserUseCase,
    required this.logoutUserUseCase,
    required this.registerUserUseCase,
    required this.forgotPasswordUseCase,
    required this.resetPasswordUseCase,
    required this.isAuthenticatedUseCase,
    required this.changePasswordUseCase,
  });

  // Observable state
  final Rx<User?> _user = Rx<User?>(null);
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;

  // Getters
  User? get user => _user.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  bool get isAuthenticated => _user.value != null;

  @override
  void onInit() {
    super.onInit();
    checkAuthStatus();
  }

  /// Check if user is authenticated
  Future<void> checkAuthStatus() async {
    _isLoading.value = true;
    final result = await getCurrentUserUseCase();
    result.fold(
      (failure) {
        _user.value = null;
        _errorMessage.value = failure.message;
      },
      (user) {
        _user.value = user;
        _errorMessage.value = '';
      },
    );
    _isLoading.value = false;
  }

  /// Login with email and password
  Future<bool> login(String email, String password) async {
    LoggerService.info('Login attempt with email: $email');
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      final params = LoginParams(email: email, password: password);
      LoggerService.debug(
        'Calling loginUserUseCase with params',
        data: {'email': email},
      );

      final result = await loginUserUseCase(params);

      return result.fold(
        (failure) {
          LoggerService.error('Login failed', error: failure.message);
          _errorMessage.value = failure.message;
          _isLoading.value = false;
          return false;
        },
        (user) {
          LoggerService.success(
            'Login successful',
            data: {'userId': user.id, 'email': user.email},
          );
          _user.value = user;
          _isLoading.value = false;
          return true;
        },
      );
    } catch (e, stackTrace) {
      LoggerService.error(
        'Unexpected error during login',
        error: e,
        stackTrace: stackTrace,
      );
      _errorMessage.value = 'An unexpected error occurred: ${e.toString()}';
      _isLoading.value = false;
      return false;
    }
  }

  /// Logout
  Future<bool> logout() async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final result = await logoutUserUseCase();

    return result.fold(
      (failure) {
        _errorMessage.value = failure.message;
        _isLoading.value = false;
        return false;
      },
      (success) {
        _user.value = null;
        _isLoading.value = false;
        return true;
      },
    );
  }

  /// Register a new user
  Future<bool> register(User user, String password) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final params = RegisterParams(user: user, password: password);
    final result = await registerUserUseCase(params);

    return result.fold(
      (failure) {
        _errorMessage.value = failure.message;
        _isLoading.value = false;
        return false;
      },
      (user) {
        _user.value = user;
        _isLoading.value = false;
        return true;
      },
    );
  }

  /// Forgot password
  Future<bool> forgotPassword(String email) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final params = ForgotPasswordParams(email: email);
    final result = await forgotPasswordUseCase(params);

    return result.fold(
      (failure) {
        _errorMessage.value = failure.message;
        _isLoading.value = false;
        return false;
      },
      (success) {
        _isLoading.value = false;
        return true;
      },
    );
  }

  /// Reset password
  Future<bool> resetPassword(String email, String code, String password) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final params = ResetPasswordParams(
      email: email,
      code: code,
      password: password,
    );
    final result = await resetPasswordUseCase(params);

    return result.fold(
      (failure) {
        _errorMessage.value = failure.message;
        _isLoading.value = false;
        return false;
      },
      (success) {
        _isLoading.value = false;
        return true;
      },
    );
  }

  /// Change password
  Future<bool> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final params = ChangePasswordParams(
      currentPassword: currentPassword,
      newPassword: newPassword,
    );
    final result = await changePasswordUseCase(params);

    return result.fold(
      (failure) {
        _errorMessage.value = failure.message;
        _isLoading.value = false;
        return false;
      },
      (success) {
        _isLoading.value = false;
        return true;
      },
    );
  }
}

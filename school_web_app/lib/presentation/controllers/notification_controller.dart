import 'package:get/get.dart';
import '../../domain/entities/notification.dart';

/// Simple notification controller matching SchoolX functionality
class NotificationController extends GetxController {
  // Observable variables
  final _isLoading = false.obs;
  final _notifications = <NotificationEntity>[].obs;

  // Getters
  RxBool get isLoading => _isLoading;
  List<NotificationEntity> get notifications => _notifications;

  @override
  void onInit() {
    super.onInit();
    loadNotifications();
  }

  /// Load notifications - simple implementation
  Future<void> loadNotifications() async {
    try {
      _isLoading.value = true;

      // Mock data for now - in real implementation, this would call the repository
      await Future.delayed(const Duration(seconds: 1));

      // Add some mock notifications
      _notifications.assignAll([
        const NotificationEntity(
          id: 1,
          title: 'إشعار تجريبي',
          body: 'هذا إشعار تجريبي للاختبار',
          createdAt: '2024-01-01T10:00:00Z',
          isRead: false,
        ),
        const NotificationEntity(
          id: 2,
          title: 'إشعار آخر',
          body: 'إشعار آخر للاختبار',
          createdAt: '2024-01-01T09:00:00Z',
          isRead: true,
        ),
      ]);
    } catch (e) {
      // Handle error
      _notifications.clear();
    } finally {
      _isLoading.value = false;
    }
  }

  /// Refresh notifications
  Future<void> refreshNotifications() async {
    await loadNotifications();
  }
}

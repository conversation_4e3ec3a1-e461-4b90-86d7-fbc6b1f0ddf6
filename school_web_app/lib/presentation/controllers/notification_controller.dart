import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../../core/services/notification_service.dart';
import '../../core/usecases/usecase.dart';
import '../../domain/entities/notification.dart';
import '../../domain/usecases/get_notifications_usecase.dart';
import '../../domain/usecases/mark_notification_read_usecase.dart';
import '../../domain/usecases/get_unread_notifications_count_usecase.dart';
import '../../domain/usecases/register_fcm_token_usecase.dart';

/// Controller for managing notifications
class NotificationController extends GetxController {
  final GetNotificationsUseCase _getNotificationsUseCase;
  final MarkNotificationReadUseCase _markNotificationReadUseCase;
  final GetUnreadNotificationsCountUseCase _getUnreadNotificationsCountUseCase;
  final RegisterFCMTokenUseCase _registerFCMTokenUseCase;
  final NotificationService _notificationService =
      Get.find<NotificationService>();

  NotificationController({
    required GetNotificationsUseCase getNotificationsUseCase,
    required MarkNotificationReadUseCase markNotificationReadUseCase,
    required GetUnreadNotificationsCountUseCase
    getUnreadNotificationsCountUseCase,
    required RegisterFCMTokenUseCase registerFCMTokenUseCase,
  }) : _getNotificationsUseCase = getNotificationsUseCase,
       _markNotificationReadUseCase = markNotificationReadUseCase,
       _getUnreadNotificationsCountUseCase = getUnreadNotificationsCountUseCase,
       _registerFCMTokenUseCase = registerFCMTokenUseCase;

  // Observable variables
  final _isLoading = false.obs;
  final _currentPage = 1.obs;
  final _hasMoreData = true.obs;
  final _selectedFilter = 'all'.obs;
  final _errorMessage = ''.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  List<NotificationEntity> get notifications =>
      _notificationService.notifications;
  int get unreadCount => _notificationService.unreadCount;
  int get currentPage => _currentPage.value;
  bool get hasMoreData => _hasMoreData.value;
  String get selectedFilter => _selectedFilter.value;
  String get errorMessage => _errorMessage.value;

  @override
  void onInit() {
    super.onInit();
    loadNotifications();
    loadUnreadCount();
  }

  /// Load notifications
  Future<void> loadNotifications({bool refresh = false}) async {
    try {
      if (refresh) {
        _currentPage.value = 1;
        _hasMoreData.value = true;
      }

      if (!_hasMoreData.value && !refresh) return;

      _isLoading.value = true;
      _errorMessage.value = '';

      // Determine filter parameters
      bool? isRead;
      String? type;

      switch (_selectedFilter.value) {
        case 'unread':
          isRead = false;
          break;
        case 'read':
          isRead = true;
          break;
        case 'trip_started':
        case 'trip_ended':
        case 'student_picked_up':
        case 'student_dropped_off':
        case 'bus_breakdown':
        case 'route_changed':
          type = _selectedFilter.value;
          break;
      }

      final result = await _getNotificationsUseCase(
        GetNotificationsParams(
          page: _currentPage.value,
          limit: 20,
          isRead: isRead,
          type: type,
        ),
      );

      result.fold(
        (failure) {
          _errorMessage.value = failure.message;
        },
        (newNotifications) {
          if (refresh) {
            _notificationService.updateNotifications(newNotifications);
          } else {
            final currentNotifications = List<NotificationEntity>.from(
              notifications,
            );
            currentNotifications.addAll(newNotifications);
            _notificationService.updateNotifications(currentNotifications);
          }

          _currentPage.value++;
          _hasMoreData.value = newNotifications.length >= 20;
        },
      );
    } catch (e) {
      _errorMessage.value = 'حدث خطأ غير متوقع';
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load unread count
  Future<void> loadUnreadCount() async {
    try {
      final result = await _getUnreadNotificationsCountUseCase(NoParams());

      result.fold(
        (failure) {
          // Handle error silently for unread count
        },
        (count) {
          // Update service with the count
          // Note: This is a simplified approach
          // In a real app, you might want to sync this with the service
        },
      );
    } catch (e) {
      // Handle error silently
    }
  }

  /// Mark notification as read
  Future<void> markAsRead(int notificationId) async {
    try {
      final result = await _markNotificationReadUseCase(
        MarkNotificationReadParams(notificationId: notificationId),
      );

      result.fold(
        (failure) {
          Get.snackbar(
            'خطأ',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        },
        (success) {
          if (success) {
            _notificationService.markAsRead(notificationId);
          }
        },
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      // This would call the repository method
      // For now, we'll just update the service
      _notificationService.markAllAsRead();

      Get.snackbar(
        'نجح',
        'تم تحديد جميع الإشعارات كمقروءة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Delete notification
  Future<void> deleteNotification(int notificationId) async {
    try {
      // This would call the repository method
      // For now, we'll just update the service
      _notificationService.removeNotification(notificationId);

      Get.snackbar(
        'نجح',
        'تم حذف الإشعار',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    try {
      _notificationService.clearAllNotifications();

      Get.snackbar(
        'نجح',
        'تم حذف جميع الإشعارات',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ غير متوقع',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Filter notifications
  void filterNotifications(String filter) {
    _selectedFilter.value = filter;
    loadNotifications(refresh: true);
  }

  /// Load more notifications (pagination)
  Future<void> loadMoreNotifications() async {
    if (!_hasMoreData.value || _isLoading.value) return;
    await loadNotifications();
  }

  /// Register FCM token
  Future<void> registerFCMToken(String token) async {
    try {
      final result = await _registerFCMTokenUseCase(
        RegisterFCMTokenParams(
          token: token,
          deviceType: GetPlatform.isWeb ? 'web' : 'mobile',
          deviceId: 'device_id', // This should be actual device ID
        ),
      );

      result.fold(
        (failure) {
          if (kDebugMode) {
            print('Failed to register FCM token: ${failure.message}');
          }
        },
        (success) {
          if (kDebugMode) {
            print('FCM token registered successfully');
          }
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error registering FCM token: $e');
      }
    }
  }

  /// Get notification icon
  IconData getNotificationIcon(String? type) {
    switch (type) {
      case 'trip_started':
        return Icons.play_arrow;
      case 'trip_ended':
        return Icons.stop;
      case 'student_picked_up':
        return Icons.person_add;
      case 'student_dropped_off':
        return Icons.person_remove;
      case 'bus_breakdown':
        return Icons.warning;
      case 'route_changed':
        return Icons.route;
      default:
        return Icons.notifications;
    }
  }

  /// Get notification color
  Color getNotificationColor(String? type) {
    switch (type) {
      case 'trip_started':
        return Colors.green;
      case 'trip_ended':
        return Colors.blue;
      case 'student_picked_up':
        return Colors.teal;
      case 'student_dropped_off':
        return Colors.orange;
      case 'bus_breakdown':
        return Colors.red;
      case 'route_changed':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  /// Format notification date
  String formatNotificationDate(String? dateString) {
    return _notificationService.formatNotificationDate(dateString);
  }

  /// Check if notification is recent
  bool isRecentNotification(String? dateString) {
    return _notificationService.isRecentNotification(dateString);
  }
}

import 'package:get/get.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/parent.dart';
import '../../domain/usecases/create_parent_usecase.dart';
import '../../domain/usecases/delete_parent_usecase.dart';
import '../../domain/usecases/get_parent_by_id_usecase.dart';
import '../../domain/usecases/get_parents_usecase.dart';
import '../../domain/usecases/get_students_by_parent_id_usecase.dart';
import '../../domain/usecases/update_parent_usecase.dart';

/// Parents controller for managing parent-related operations
/// Following Clean Architecture and GetX patterns
class ParentsController extends GetxController {
  final GetParentsUseCase _getParentsUseCase;
  final GetParentByIdUseCase _getParentByIdUseCase;
  final CreateParentUseCase _createParentUseCase;
  final UpdateParentUseCase _updateParentUseCase;
  final DeleteParentUseCase _deleteParentUseCase;
  final GetStudentsByParentIdUseCase _getStudentsByParentIdUseCase;

  ParentsController(
    this._getParentsUseCase,
    this._getParentByIdUseCase,
    this._createParentUseCase,
    this._updateParentUseCase,
    this._deleteParentUseCase,
    this._getStudentsByParentIdUseCase,
  );

  // Observable state
  final RxList<Parent> _parents = <Parent>[].obs;
  final Rx<Parent?> _selectedParent = Rx<Parent?>(null);
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingMore = false.obs;
  final RxString _error = ''.obs;
  final RxInt _currentPage = 1.obs;
  final RxBool _hasMoreData = true.obs;
  final RxString _searchQuery = ''.obs;

  // Getters
  List<Parent> get parents => _parents;
  Parent? get selectedParent => _selectedParent.value;
  bool get isLoading => _isLoading.value;
  bool get isLoadingMore => _isLoadingMore.value;
  String get error => _error.value;
  int get currentPage => _currentPage.value;
  bool get hasMoreData => _hasMoreData.value;
  String get searchQuery => _searchQuery.value;

  @override
  void onInit() {
    super.onInit();
    loadParents();
  }

  /// Load parents with pagination
  Future<void> loadParents({bool refresh = false}) async {
    try {
      if (refresh) {
        _currentPage.value = 1;
        _hasMoreData.value = true;
        _parents.clear();
      }

      if (_isLoading.value || (!_hasMoreData.value && !refresh)) return;

      _isLoading.value = true;
      _error.value = '';

      LoggerService.info('Loading parents - Page: ${_currentPage.value}');

      final result = await _getParentsUseCase(GetParentsParams(
        page: _currentPage.value,
        search: _searchQuery.value.isNotEmpty ? _searchQuery.value : null,
      ));

      result.fold(
        (failure) {
          _error.value = failure.message;
          LoggerService.error('Failed to load parents: ${failure.message}');
        },
        (newParents) {
          if (newParents.isEmpty) {
            _hasMoreData.value = false;
          } else {
            _parents.addAll(newParents);
            _currentPage.value++;
          }
          LoggerService.success('Loaded ${newParents.length} parents');
        },
      );
    } catch (e) {
      _error.value = 'An unexpected error occurred';
      LoggerService.error('Unexpected error loading parents: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load more parents for pagination
  Future<void> loadMoreParents() async {
    if (_isLoadingMore.value || !_hasMoreData.value) return;

    try {
      _isLoadingMore.value = true;
      await loadParents();
    } finally {
      _isLoadingMore.value = false;
    }
  }

  /// Search parents
  Future<void> searchParents(String query) async {
    _searchQuery.value = query;
    await loadParents(refresh: true);
  }

  /// Get parent by ID
  Future<void> getParentById(int id) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      LoggerService.info('Getting parent by ID: $id');

      final result = await _getParentByIdUseCase(id);

      result.fold(
        (failure) {
          _error.value = failure.message;
          LoggerService.error('Failed to get parent: ${failure.message}');
        },
        (parent) {
          _selectedParent.value = parent;
          LoggerService.success('Parent loaded successfully');
        },
      );
    } catch (e) {
      _error.value = 'An unexpected error occurred';
      LoggerService.error('Unexpected error getting parent: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Create new parent
  Future<bool> createParent(Parent parent) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      LoggerService.info('Creating new parent: ${parent.name}');

      final result = await _createParentUseCase(parent);

      return result.fold(
        (failure) {
          _error.value = failure.message;
          LoggerService.error('Failed to create parent: ${failure.message}');
          return false;
        },
        (createdParent) {
          _parents.insert(0, createdParent);
          LoggerService.success('Parent created successfully');
          return true;
        },
      );
    } catch (e) {
      _error.value = 'An unexpected error occurred';
      LoggerService.error('Unexpected error creating parent: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update existing parent
  Future<bool> updateParent(Parent parent) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      LoggerService.info('Updating parent: ${parent.name}');

      final result = await _updateParentUseCase(parent);

      return result.fold(
        (failure) {
          _error.value = failure.message;
          LoggerService.error('Failed to update parent: ${failure.message}');
          return false;
        },
        (updatedParent) {
          final index = _parents.indexWhere((p) => p.id == updatedParent.id);
          if (index != -1) {
            _parents[index] = updatedParent;
          }
          if (_selectedParent.value?.id == updatedParent.id) {
            _selectedParent.value = updatedParent;
          }
          LoggerService.success('Parent updated successfully');
          return true;
        },
      );
    } catch (e) {
      _error.value = 'An unexpected error occurred';
      LoggerService.error('Unexpected error updating parent: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Delete parent
  Future<bool> deleteParent(int id) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      LoggerService.info('Deleting parent with ID: $id');

      final result = await _deleteParentUseCase(id);

      return result.fold(
        (failure) {
          _error.value = failure.message;
          LoggerService.error('Failed to delete parent: ${failure.message}');
          return false;
        },
        (success) {
          if (success) {
            _parents.removeWhere((parent) => parent.id == id);
            if (_selectedParent.value?.id == id) {
              _selectedParent.value = null;
            }
            LoggerService.success('Parent deleted successfully');
          }
          return success;
        },
      );
    } catch (e) {
      _error.value = 'An unexpected error occurred';
      LoggerService.error('Unexpected error deleting parent: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Get students by parent ID
  Future<List<int>> getStudentsByParentId(int parentId) async {
    try {
      LoggerService.info('Getting students for parent ID: $parentId');

      final result = await _getStudentsByParentIdUseCase(parentId);

      return result.fold(
        (failure) {
          LoggerService.error('Failed to get students: ${failure.message}');
          return [];
        },
        (studentIds) {
          LoggerService.success('Found ${studentIds.length} students for parent');
          return studentIds;
        },
      );
    } catch (e) {
      LoggerService.error('Unexpected error getting students: $e');
      return [];
    }
  }

  /// Clear error
  void clearError() {
    _error.value = '';
  }

  /// Clear selected parent
  void clearSelectedParent() {
    _selectedParent.value = null;
  }
}

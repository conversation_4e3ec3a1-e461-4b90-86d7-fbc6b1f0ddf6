import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../data/models/student_attendance_model.dart';
import '../../data/models/route_model.dart';
import '../../data/datasources/trip_remote_data_source.dart';
import '../../core/utils/logger.dart';

/// Previous Trip Details Controller
/// Manages the state of previous trip details including attendance and route data
/// Based on the original SchoolX project structure
class PreviousTripDetailsController extends GetxController
    with GetSingleTickerProviderStateMixin {
  final TripRemoteDataSource _tripRemoteDataSource;

  PreviousTripDetailsController({
    required TripRemoteDataSource tripRemoteDataSource,
  }) : _tripRemoteDataSource = tripRemoteDataSource;

  // Tab controller for managing tabs
  late TabController tabController;

  // Observable state
  final _isLoading = false.obs;
  final _isAttendanceLoading = false.obs;
  final _isRouteLoading = false.obs;
  final _errorMessage = ''.obs;
  final _presentStudents = <StudentAttendanceModel>[].obs;
  final _absentStudents = <StudentAttendanceModel>[].obs;
  final _routeData = Rxn<TripRouteModel>();

  // Trip details
  String tripId = '';
  String tripDate = '';
  String busName = '';
  String tripType = '';
  String supervisorName = '';
  String driverName = '';

  // Getters
  bool get isLoading => _isLoading.value;
  bool get isAttendanceLoading => _isAttendanceLoading.value;
  bool get isRouteLoading => _isRouteLoading.value;
  String get errorMessage => _errorMessage.value;
  List<StudentAttendanceModel> get presentStudents => _presentStudents;
  List<StudentAttendanceModel> get absentStudents => _absentStudents;
  TripRouteModel? get routeData => _routeData.value;

  // Computed properties
  int get totalStudents => presentStudents.length + absentStudents.length;
  int get presentCount => presentStudents.length;
  int get absentCount => absentStudents.length;

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 3, vsync: this);

    // Get trip details from arguments
    final arguments = Get.arguments as Map<String, dynamic>?;
    if (arguments != null) {
      tripId = arguments['tripId']?.toString() ?? '';
      tripDate = arguments['tripDate']?.toString() ?? '';
      busName = arguments['busName']?.toString() ?? '';
      tripType = arguments['tripType']?.toString() ?? '';
      supervisorName = arguments['supervisorName']?.toString() ?? '';
      driverName = arguments['driverName']?.toString() ?? '';
    }

    // Load data
    if (tripId.isNotEmpty) {
      loadTripDetails();
    }
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }

  /// Load all trip details (attendance and route)
  Future<void> loadTripDetails() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      LoggerService.info('Loading trip details', data: {'tripId': tripId});

      // Load both attendance and route data in parallel
      await Future.wait([loadAttendanceData(), loadRouteData()]);

      LoggerService.success('Trip details loaded successfully');
    } catch (e) {
      LoggerService.error('Failed to load trip details', error: e);
      _errorMessage.value = e.toString();
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load attendance data (present and absent students)
  Future<void> loadAttendanceData() async {
    try {
      _isAttendanceLoading.value = true;
      LoggerService.info('Loading attendance data', data: {'tripId': tripId});

      // Load both present and absent students in parallel
      final results = await Future.wait([
        _tripRemoteDataSource.getTripAttendance(tripId),
        _tripRemoteDataSource.getTripAbsentStudents(tripId),
      ]);

      final presentData = results[0];
      final absentData = results[1];

      // Convert to StudentAttendanceModel
      _presentStudents.value =
          presentData
              .map(
                (data) => StudentAttendanceModel.fromJson({
                  ...data,
                  'is_present': true,
                  'attendance_time':
                      data['pivot']?['arrived_at'] ??
                      data['pivot']?['onboard_at'],
                  'grade': 'الصف ${data['grade_id'] ?? 'غير محدد'}',
                  'classroom': data['classroom_id']?.toString() ?? '',
                  'profile_image': data['logo_path'],
                }),
              )
              .toList();

      _absentStudents.value =
          absentData
              .map(
                (data) => StudentAttendanceModel.fromJson({
                  ...data,
                  'is_present': false,
                  'attendance_time': null,
                  'grade': 'الصف ${data['grade_id'] ?? 'غير محدد'}',
                  'classroom': data['classroom_id']?.toString() ?? '',
                  'profile_image': data['logo_path'],
                }),
              )
              .toList();

      LoggerService.success(
        'Attendance data loaded successfully',
        data: {
          'presentCount': _presentStudents.length,
          'absentCount': _absentStudents.length,
        },
      );
    } catch (e) {
      LoggerService.error('Failed to load attendance data', error: e);
      _presentStudents.value = [];
      _absentStudents.value = [];
      rethrow;
    } finally {
      _isAttendanceLoading.value = false;
    }
  }

  /// Load route data
  Future<void> loadRouteData() async {
    try {
      _isRouteLoading.value = true;
      LoggerService.info('Loading route data', data: {'tripId': tripId});

      final routeResponse = await _tripRemoteDataSource.getTripRouteDetails(
        tripId,
      );

      if (routeResponse['status'] == true && routeResponse['data'] != null) {
        final tripData = routeResponse['data'];

        // Extract route points
        List<RoutePointModel> routePoints = [];
        if (tripData['routes'] != null && tripData['routes'] is List) {
          routePoints =
              (tripData['routes'] as List)
                  .map((point) => RoutePointModel.fromJson(point))
                  .toList();
        }

        // Create TripRouteModel
        _routeData.value = TripRouteModel(
          tripId: tripData['id'],
          startTime: tripData['created_at'],
          endTime: tripData['end_at'],
          totalDistance: _calculateTotalDistance(routePoints),
          estimatedTime: _calculateEstimatedTime(routePoints),
          routePoints: routePoints,
        );

        LoggerService.success(
          'Route data loaded successfully',
          data: {
            'routePointsCount': routePoints.length,
            'totalDistance': _routeData.value?.totalDistance,
          },
        );
      } else {
        LoggerService.warning('No route data available for trip');
        _routeData.value = null;
      }
    } catch (e) {
      LoggerService.error('Failed to load route data', error: e);
      _routeData.value = null;
      rethrow;
    } finally {
      _isRouteLoading.value = false;
    }
  }

  /// Refresh all data
  Future<void> refreshData() async {
    LoggerService.info('Refreshing trip details data');
    await loadTripDetails();
  }

  /// Calculate total distance from route points
  double _calculateTotalDistance(List<RoutePointModel> routePoints) {
    if (routePoints.length < 2) return 0.0;

    double totalDistance = 0.0;
    for (int i = 1; i < routePoints.length; i++) {
      final prev = routePoints[i - 1];
      final current = routePoints[i];

      if (prev.latitude != null &&
          prev.longitude != null &&
          current.latitude != null &&
          current.longitude != null) {
        final prevLat = double.tryParse(prev.latitude!) ?? 0.0;
        final prevLng = double.tryParse(prev.longitude!) ?? 0.0;
        final currentLat = double.tryParse(current.latitude!) ?? 0.0;
        final currentLng = double.tryParse(current.longitude!) ?? 0.0;

        // Simple distance calculation (Haversine formula would be more accurate)
        final distance = _calculateDistance(
          prevLat,
          prevLng,
          currentLat,
          currentLng,
        );
        totalDistance += distance;
      }
    }

    return totalDistance;
  }

  /// Calculate estimated time based on route points
  int _calculateEstimatedTime(List<RoutePointModel> routePoints) {
    if (routePoints.length < 2) return 0;

    // Simple estimation: assume 30 km/h average speed
    final distance = _calculateTotalDistance(routePoints);
    final timeInHours = distance / 30.0; // 30 km/h
    return (timeInHours * 60).round(); // Convert to minutes
  }

  /// Calculate distance between two points (simplified)
  double _calculateDistance(
    double lat1,
    double lng1,
    double lat2,
    double lng2,
  ) {
    // Simple Euclidean distance (for demonstration)
    // In production, use Haversine formula for accurate results
    final deltaLat = lat2 - lat1;
    final deltaLng = lng2 - lng1;
    return (deltaLat * deltaLat + deltaLng * deltaLng) *
        111.0; // Rough conversion to km
  }

  /// Get trip type display text
  String get tripTypeDisplayText {
    switch (tripType.toLowerCase()) {
      case 'start_day':
        return 'رحلة الصباح';
      case 'end_day':
        return 'رحلة المساء';
      default:
        return tripType.isNotEmpty ? tripType : 'رحلة سابقة';
    }
  }

  /// Get status color based on trip completion
  Color get statusColor {
    if (presentStudents.isNotEmpty || absentStudents.isNotEmpty) {
      return Colors.green; // Completed
    }
    return Colors.orange; // Unknown status
  }

  /// Get status text
  String get statusText {
    if (presentStudents.isNotEmpty || absentStudents.isNotEmpty) {
      return 'مكتملة';
    }
    return 'غير معروف';
  }
}

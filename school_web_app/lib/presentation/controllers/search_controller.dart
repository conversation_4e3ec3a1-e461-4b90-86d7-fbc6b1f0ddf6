import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/services/search_service.dart';
import '../../presentation/routes/app_routes.dart';
import '../../core/constants/size_constants.dart';

/// Controller for managing search functionality
class SearchController extends GetxController {
  final SearchService _searchService = Get.find<SearchService>();

  // Text editing controllers
  final searchTextController = TextEditingController();

  // Observable variables
  final _showSuggestions = false.obs;
  final _selectedCategories = <SearchCategory>[].obs;
  final _activeFilters = <String, dynamic>{}.obs;

  // Getters
  bool get isSearching => _searchService.isSearching;
  String get searchQuery => _searchService.searchQuery;
  List<SearchResult> get searchResults => _searchService.searchResults;
  List<String> get searchHistory => _searchService.searchHistory;
  bool get showSuggestions => _showSuggestions.value;
  List<SearchCategory> get selectedCategories => _selectedCategories;
  Map<String, dynamic> get activeFilters => _activeFilters;

  @override
  void onInit() {
    super.onInit();
    // Initialize with all categories selected
    _selectedCategories.assignAll(SearchCategory.values);
  }

  @override
  void onClose() {
    searchTextController.dispose();
    super.onClose();
  }

  /// Perform search
  Future<void> performSearch(String query) async {
    _showSuggestions.value = false;

    await _searchService.performGlobalSearch(
      query,
      categories: _selectedCategories,
      filters: _activeFilters,
    );
  }

  /// Handle search input change
  void onSearchChanged(String query) {
    if (query.trim().isEmpty) {
      _showSuggestions.value = false;
      _searchService.clearSearch();
    } else {
      _showSuggestions.value = true;
    }
  }

  /// Handle search submission
  void onSearchSubmitted(String query) {
    if (query.trim().isNotEmpty) {
      performSearch(query);
    }
  }

  /// Select search suggestion
  void selectSuggestion(String suggestion) {
    searchTextController.text = suggestion;
    performSearch(suggestion);
  }

  /// Toggle search category
  void toggleCategory(SearchCategory category) {
    if (_selectedCategories.contains(category)) {
      _selectedCategories.remove(category);
    } else {
      _selectedCategories.add(category);
    }

    // Re-search if there's an active query
    if (searchQuery.isNotEmpty) {
      performSearch(searchQuery);
    }
  }

  /// Add filter
  void addFilter(String key, dynamic value) {
    _activeFilters[key] = value;
    _searchService.applyFilters(_activeFilters);
  }

  /// Remove filter
  void removeFilter(String key) {
    _activeFilters.remove(key);
    _searchService.removeFilter(key);
  }

  /// Clear all filters
  void clearFilters() {
    _activeFilters.clear();
    _searchService.applyFilters({});
  }

  /// Clear search
  void clearSearch() {
    searchTextController.clear();
    _showSuggestions.value = false;
    _searchService.clearSearch();
  }

  /// Clear search history
  void clearSearchHistory() {
    _searchService.clearSearchHistory();
  }

  /// Get search suggestions
  List<String> getSearchSuggestions(String query) {
    return _searchService.getSearchSuggestions(query);
  }

  /// Navigate to search result
  void navigateToResult(SearchResult result) {
    switch (result.category) {
      case SearchCategory.students:
        Get.toNamed(
          AppRoutes.studentDetails,
          arguments: {'studentId': result.id},
        );
        break;
      case SearchCategory.parents:
        Get.toNamed(
          AppRoutes.parentDetails,
          arguments: {'parentId': result.id},
        );
        break;
      case SearchCategory.drivers:
        Get.toNamed(
          AppRoutes.driverDetails,
          arguments: {'driverId': result.id},
        );
        break;
      case SearchCategory.supervisors:
        Get.toNamed(
          AppRoutes.supervisorDetails,
          arguments: {'supervisorId': result.id},
        );
        break;
      case SearchCategory.buses:
        Get.toNamed(AppRoutes.busDetails, arguments: {'busId': result.id});
        break;
      case SearchCategory.trips:
        Get.toNamed(AppRoutes.tripDetails, arguments: {'tripId': result.id});
        break;
    }
  }

  /// Get category icon
  IconData getCategoryIcon(SearchCategory category) {
    switch (category) {
      case SearchCategory.students:
        return Icons.person;
      case SearchCategory.parents:
        return Icons.family_restroom;
      case SearchCategory.drivers:
        return Icons.drive_eta;
      case SearchCategory.supervisors:
        return Icons.supervisor_account;
      case SearchCategory.buses:
        return Icons.directions_bus;
      case SearchCategory.trips:
        return Icons.route;
    }
  }

  /// Get category color
  Color getCategoryColor(SearchCategory category) {
    switch (category) {
      case SearchCategory.students:
        return Colors.blue;
      case SearchCategory.parents:
        return Colors.green;
      case SearchCategory.drivers:
        return Colors.orange;
      case SearchCategory.supervisors:
        return Colors.purple;
      case SearchCategory.buses:
        return Colors.red;
      case SearchCategory.trips:
        return Colors.teal;
    }
  }

  /// Get filter display name
  String getFilterDisplayName(String key) {
    switch (key) {
      case 'school_id':
        return 'المدرسة';
      case 'grade_id':
        return 'الصف';
      case 'bus_id':
        return 'الحافلة';
      case 'status':
        return 'الحالة';
      case 'date_from':
        return 'من تاريخ';
      case 'date_to':
        return 'إلى تاريخ';
      default:
        return key;
    }
  }

  /// Format filter value for display
  String formatFilterValue(dynamic value) {
    if (value is DateTime) {
      return '${value.day}/${value.month}/${value.year}';
    }
    return value.toString();
  }

  /// Show search filters dialog
  void showFiltersDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('فلاتر البحث'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Categories section
              const Text(
                'الفئات',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: SizeConstants.spaceXS),
              Wrap(
                spacing: 8,
                children:
                    SearchCategory.values.map((category) {
                      return Obx(
                        () => FilterChip(
                          label: Text(category.displayName),
                          selected: _selectedCategories.contains(category),
                          onSelected: (selected) => toggleCategory(category),
                          avatar: Icon(
                            getCategoryIcon(category),
                            size: SizeConstants.iconXS,
                          ),
                        ),
                      );
                    }).toList(),
              ),
              SizedBox(height: SizeConstants.spaceBase),
              // Active filters section
              if (_activeFilters.isNotEmpty) ...[
                const Text(
                  'الفلاتر النشطة',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: SizeConstants.spaceXS),
                Wrap(
                  spacing: 8,
                  children:
                      _activeFilters.entries.map((entry) {
                        return Chip(
                          label: Text(
                            '${getFilterDisplayName(entry.key)}: ${formatFilterValue(entry.value)}',
                          ),
                          deleteIcon: const Icon(
                            Icons.close,
                            size: SizeConstants.iconXS,
                          ),
                          onDeleted: () => removeFilter(entry.key),
                        );
                      }).toList(),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(onPressed: clearFilters, child: const Text('مسح الكل')),
          TextButton(onPressed: () => Get.back(), child: const Text('إغلاق')),
        ],
      ),
    );
  }
}

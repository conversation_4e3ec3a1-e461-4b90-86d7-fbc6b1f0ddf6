import 'package:get/get.dart';
import '../routes/app_routes.dart';

/// SplashController for managing splash screen logic
/// Following Single Responsibility Principle by focusing only on splash screen functionality
class SplashController extends GetxController {
  // Initialize any required data or services when needed

  /// Navigate to the appropriate screen based on authentication status
  void navigateToNextScreen() {
    // In a real app, you would check authentication status here
    // For now, just navigate to home after a delay
    Future.delayed(const Duration(seconds: 3), () {
      Get.offAllNamed(AppRoutes.home);
    });
  }
}

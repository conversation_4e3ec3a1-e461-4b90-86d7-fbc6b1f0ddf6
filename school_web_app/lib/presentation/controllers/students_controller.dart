import 'package:flutter/material.dart';
import 'package:get/get.dart';

// import '../../core/utils/logger_service.dart';
import '../../domain/entities/student.dart';
import '../../domain/usecases/get_students_usecase.dart';
import '../../domain/usecases/get_students_by_bus_id_usecase.dart';
import '../../domain/usecases/get_students_by_parent_id_usecase.dart';
import '../../core/constants/size_constants.dart';

/// Students controller
class StudentsController extends GetxController {
  final GetStudentsUseCase getStudentsUseCase;
  final GetStudentsByBusIdUseCase getStudentsByBusIdUseCase;
  final GetStudentsByParentIdUseCase getStudentsByParentIdUseCase;

  StudentsController({
    required this.getStudentsUseCase,
    required this.getStudentsByBusIdUseCase,
    required this.getStudentsByParentIdUseCase,
  });

  // Observable state
  final RxList<Student> _students = <Student>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxInt _currentPage = 1.obs;
  final RxInt _lastPage = 1.obs;
  final RxInt _total = 0.obs;
  final RxString _searchQuery = ''.obs;

  // Form controllers for add/edit student
  final GlobalKey<FormState> addStudentFormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> editStudentFormKey = GlobalKey<FormState>();

  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController cityNameController = TextEditingController();
  final TextEditingController dateBirthController = TextEditingController();
  final TextEditingController genderController = TextEditingController();
  final TextEditingController religionController = TextEditingController();
  final TextEditingController gradeController = TextEditingController();
  final TextEditingController classroomController = TextEditingController();
  final TextEditingController busController = TextEditingController();
  final TextEditingController typeBloodController = TextEditingController();
  final TextEditingController tripTypeController = TextEditingController();

  // Import state
  final RxBool _isImporting = false.obs;
  final RxString _importProgress = ''.obs;
  final RxInt _importedCount = 0.obs;
  final RxInt _importErrorCount = 0.obs;

  // Selection state for forms
  final RxInt selectedGenderId = 0.obs;
  final RxInt selectedReligionId = 0.obs;
  final RxInt selectedGradeId = 0.obs;
  final RxInt selectedTypeBloodId = 0.obs;

  // Getters
  List<Student> get students => _students;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  int get currentPage => _currentPage.value;
  int get lastPage => _lastPage.value;
  int get total => _total.value;
  String get searchQuery => _searchQuery.value;
  bool get isImporting => _isImporting.value;
  String get importProgress => _importProgress.value;
  int get importedCount => _importedCount.value;
  int get importErrorCount => _importErrorCount.value;

  @override
  void onInit() {
    super.onInit();
    getStudents();
  }

  /// Get all students
  Future<void> getStudents({int page = 1}) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';
      _currentPage.value = page;

      final result = await getStudentsUseCase.call(
        GetStudentsParams(page: page),
      );

      result.fold(
        (failure) {
          _errorMessage.value = failure.message;
          debugPrint('Failed to get students: ${failure.message}');
        },
        (studentsResponse) {
          if (page == 1) {
            _students.clear();
          }
          _students.addAll(studentsResponse);
          // TODO: Add pagination support
          _lastPage.value = 1;
          _total.value = studentsResponse.length;
        },
      );
    } catch (e) {
      _errorMessage.value = 'An unexpected error occurred';
      debugPrint('Error getting students: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Refresh students list
  Future<void> refreshStudents() async {
    await getStudents(page: 1);
  }

  /// Load more students (pagination)
  Future<void> loadMoreStudents() async {
    if (_currentPage.value < _lastPage.value && !_isLoading.value) {
      await getStudents(page: _currentPage.value + 1);
    }
  }

  /// Search students
  void searchStudents(String query) {
    _searchQuery.value = query;
    // TODO: Implement search functionality
    getStudents(page: 1);
  }

  /// Clear form controllers
  void clearForm() {
    nameController.clear();
    phoneController.clear();
    addressController.clear();
    cityNameController.clear();
    dateBirthController.clear();
    genderController.clear();
    religionController.clear();
    gradeController.clear();
    classroomController.clear();
    busController.clear();
    typeBloodController.clear();
    tripTypeController.clear();
  }

  /// Initialize form for editing
  void initializeEditForm(Student student) {
    nameController.text = student.name ?? '';
    phoneController.text = student.phone ?? '';
    addressController.text = student.address ?? '';
    cityNameController.text = student.cityName ?? '';
    dateBirthController.text = student.dateBirth ?? '';
    genderController.text = student.gender ?? '';
    religionController.text = student.religion ?? '';
    gradeController.text = student.grade?.toString() ?? '';
    classroomController.text = student.classroom ?? '';
    busController.text = student.busId?.toString() ?? '';
    typeBloodController.text = student.typeBlood?.toString() ?? '';
    tripTypeController.text = student.tripType ?? '';
  }

  /// Generate and download Excel template
  Future<void> downloadTemplate() async {
    Get.snackbar(
      'Info',
      'Excel template feature will be available soon',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
      margin: EdgeInsets.all(SizeConstants.spaceBase),
      borderRadius: 12,
    );
  }

  /// Pick and import Excel file
  Future<void> importFromExcel() async {
    Get.snackbar(
      'Info',
      'Excel import feature will be available soon',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
      margin: EdgeInsets.all(SizeConstants.spaceBase),
      borderRadius: 12,
    );
  }

  /// Load next page (for pagination)
  Future<void> loadNextPage() async {
    await loadMoreStudents();
  }

  /// Add new student
  Future<void> addStudent() async {
    Get.snackbar(
      'Info',
      'Add student feature will be available soon',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
      margin: EdgeInsets.all(SizeConstants.spaceBase),
      borderRadius: 12,
    );
  }

  /// Update existing student
  Future<void> updateStudent(String? studentId) async {
    Get.snackbar(
      'Info',
      'Update student feature will be available soon',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
      margin: EdgeInsets.all(SizeConstants.spaceBase),
      borderRadius: 12,
    );
  }

  @override
  void onClose() {
    nameController.dispose();
    phoneController.dispose();
    addressController.dispose();
    cityNameController.dispose();
    dateBirthController.dispose();
    genderController.dispose();
    religionController.dispose();
    gradeController.dispose();
    classroomController.dispose();
    busController.dispose();
    typeBloodController.dispose();
    tripTypeController.dispose();
    super.onClose();
  }
}

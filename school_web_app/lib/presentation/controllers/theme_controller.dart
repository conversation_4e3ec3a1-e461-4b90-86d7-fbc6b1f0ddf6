import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// ThemeController for managing theme state
/// Following Single Responsibility Principle by focusing only on theme management
class ThemeController extends GetxController {
  static const String themeKey = 'theme_mode';
  
  final SharedPreferences _prefs;
  
  // Observable state
  final Rx<ThemeMode> _themeMode = ThemeMode.light.obs;
  
  // Getters
  ThemeMode get themeMode => _themeMode.value;
  bool get isDarkMode => _themeMode.value == ThemeMode.dark;
  
  // Constructor with dependency injection
  ThemeController({required SharedPreferences prefs}) : _prefs = prefs;
  
  @override
  void onInit() {
    super.onInit();
    _loadThemeFromPrefs();
  }
  
  /// Load theme preference from SharedPreferences
  void _loadThemeFromPrefs() {
    final String? themeModeString = _prefs.getString(themeKey);
    if (themeModeString != null) {
      if (themeModeString == 'dark') {
        _themeMode.value = ThemeMode.dark;
      } else {
        _themeMode.value = ThemeMode.light;
      }
    } else {
      // Default to light theme if no preference is saved
      _themeMode.value = ThemeMode.light;
    }
    
    // Apply the theme
    Get.changeThemeMode(_themeMode.value);
  }
  
  /// Toggle between light and dark theme
  Future<void> toggleTheme() async {
    // Toggle theme mode
    _themeMode.value = _themeMode.value == ThemeMode.light 
        ? ThemeMode.dark 
        : ThemeMode.light;
    
    // Save preference
    await _prefs.setString(
      themeKey, 
      _themeMode.value == ThemeMode.dark ? 'dark' : 'light'
    );
    
    // Apply the theme
    Get.changeThemeMode(_themeMode.value);
  }
  
  /// Set specific theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    // Set theme mode
    _themeMode.value = mode;
    
    // Save preference
    await _prefs.setString(
      themeKey, 
      mode == ThemeMode.dark ? 'dark' : 'light'
    );
    
    // Apply the theme
    Get.changeThemeMode(mode);
  }
  
  /// Get tooltip text based on current theme
  String get themeTooltip => 
      _themeMode.value == ThemeMode.light 
          ? 'Switch to Dark Mode' 
          : 'Switch to Light Mode';
}

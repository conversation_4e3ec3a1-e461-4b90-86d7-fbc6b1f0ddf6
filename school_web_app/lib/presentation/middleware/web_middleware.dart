import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

/// WebMiddleware class for handling web-specific functionality
/// Following Single Responsibility Principle by focusing only on web middleware
class WebMiddleware extends GetMiddleware {
  @override
  int? get priority => 2;

  @override
  RouteSettings? redirect(String? route) {
    // Only apply web-specific logic on web platform
    if (kIsWeb) {
      _updateBrowserTitle(route);
      _updateMetaTags(route);
      _handleWebNavigation(route);
    }
    
    return null;
  }

  @override
  GetPage? onPageCalled(GetPage? page) {
    if (kIsWeb) {
      // Update browser URL and title
      _updateBrowserState(page?.name);
      
      // Handle web-specific page setup
      _setupWebPage(page);
    }
    
    return super.onPageCalled(page);
  }

  @override
  Widget onPageBuilt(Widget page) {
    if (kIsWeb) {
      // Wrap page with web-specific functionality
      return _wrapWithWebFeatures(page);
    }
    
    return super.onPageBuilt(page);
  }

  /// Update browser title based on route
  void _updateBrowserTitle(String? route) {
    // Simplified title update for web
    debugPrint('🌐 Updating title for route: $route');
  }

  /// Update meta tags for SEO
  void _updateMetaTags(String? route) {
    // Simplified meta tags update for web
    debugPrint('🌐 Updating meta tags for route: $route');
  }

  /// Handle web-specific navigation
  void _handleWebNavigation(String? route) {
    // Add web-specific navigation handling here
    debugPrint('🌐 Web navigation to: $route');
  }

  /// Update browser state
  void _updateBrowserState(String? route) {
    if (route != null) {
      // Update browser URL without triggering navigation
      debugPrint('🌐 Updating browser state for: $route');
    }
  }

  /// Setup web-specific page configuration
  void _setupWebPage(GetPage? page) {
    // Add any web-specific page setup here
    debugPrint('🔧 Setting up web page: ${page?.name}');
  }

  /// Wrap page with web-specific features
  Widget _wrapWithWebFeatures(Widget page) {
    return Focus(
      autofocus: true,
      child: Shortcuts(
        shortcuts: <LogicalKeySet, Intent>{
          LogicalKeySet(LogicalKeyboardKey.escape): const _EscapeIntent(),
          LogicalKeySet(LogicalKeyboardKey.f5): const _RefreshIntent(),
          LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyR): const _RefreshIntent(),
        },
        child: Actions(
          actions: <Type, Action<Intent>>{
            _EscapeIntent: _EscapeAction(),
            _RefreshIntent: _RefreshAction(),
          },
          child: page,
        ),
      ),
    );
  }
}

/// Custom intents for keyboard shortcuts
class _EscapeIntent extends Intent {
  const _EscapeIntent();
}

class _RefreshIntent extends Intent {
  const _RefreshIntent();
}

/// Custom actions for keyboard shortcuts
class _EscapeAction extends Action<_EscapeIntent> {
  @override
  Object? invoke(_EscapeIntent intent) {
    // Handle escape key - go back or close dialogs
    if (Get.isDialogOpen ?? false) {
      Get.back();
    } else if (Get.isBottomSheetOpen ?? false) {
      Get.back();
    } else if (Get.isSnackbarOpen) {
      Get.closeAllSnackbars();
    } else {
      // Go back in navigation
      if (Navigator.canPop(Get.context!)) {
        Get.back();
      }
    }
    return null;
  }
}

class _RefreshAction extends Action<_RefreshIntent> {
  @override
  Object? invoke(_RefreshIntent intent) {
    // Handle refresh - reload current page data
    debugPrint('🔄 Refresh action triggered');
    return null;
  }
}

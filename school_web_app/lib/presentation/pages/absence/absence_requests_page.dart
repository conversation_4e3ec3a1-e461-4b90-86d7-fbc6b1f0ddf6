import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../domain/entities/absence_request.dart';
import '../../controllers/absence_controller.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';
import '../../widgets/common/empty_state_widget.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/absence/absence_request_card.dart';
import '../../../core/constants/size_constants.dart';

/// Page for managing absence requests
class AbsenceRequestsPage extends GetView<AbsenceController> {
  const AbsenceRequestsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.dark
              ? ColorConstants.backgroundDark
              : ColorConstants.background,
      body: ResponsiveSidebar(
        child: Column(
          children: [
            // Header with title and filters
            _buildHeader(context),
            // Content
            Expanded(child: _buildContent(context)),
          ],
        ),
      ),
    );
  }

  /// Build header with title and filters
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceBase),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.cardDark
                : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'إدارة طلبات الغياب',
            style: TextStyle(
              fontSize: SizeConstants.fontMD,
              fontWeight: FontWeight.bold,
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? ColorConstants.textPrimaryDark
                      : ColorConstants.textPrimary,
            ),
          ),
          SizedBox(height: SizeConstants.spaceBase),
          // Filters row 1
          Row(
            children: [
              // Search field
              Expanded(
                flex: 2,
                child: TextField(
                  onChanged: controller.searchRequests,
                  decoration: InputDecoration(
                    hintText: 'البحث باسم الطالب...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                    ),
                    filled: true,
                    fillColor:
                        Theme.of(context).brightness == Brightness.dark
                            ? ColorConstants.backgroundDark
                            : Colors.grey[100],
                  ),
                ),
              ),
              SizedBox(width: SizeConstants.spaceBase),
              // Attendance type filter
              Expanded(
                child: Obx(
                  () => DropdownButtonFormField<String>(
                    value:
                        controller.selectedAttendanceType.isEmpty
                            ? null
                            : controller.selectedAttendanceType,
                    onChanged:
                        (value) =>
                            controller.filterByAttendanceType(value ?? ''),
                    decoration: InputDecoration(
                      labelText: 'نوع الحضور',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                      ),
                      filled: true,
                      fillColor:
                          Theme.of(context).brightness == Brightness.dark
                              ? ColorConstants.backgroundDark
                              : Colors.grey[100],
                    ),
                    items: [
                      const DropdownMenuItem(
                        value: '',
                        child: Text('جميع الأنواع'),
                      ),
                      ...controller.attendanceTypes.map(
                        (type) =>
                            DropdownMenuItem(value: type, child: Text(type)),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: SizeConstants.spaceBase),
          // Filters row 2
          Row(
            children: [
              // Date filter
              Expanded(
                child: TextField(
                  onTap: () => _selectDate(context),
                  readOnly: true,
                  decoration: InputDecoration(
                    labelText: 'التاريخ',
                    hintText: 'اختر التاريخ',
                    prefixIcon: const Icon(Icons.calendar_today),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                    ),
                    filled: true,
                    fillColor:
                        Theme.of(context).brightness == Brightness.dark
                            ? ColorConstants.backgroundDark
                            : Colors.grey[100],
                  ),
                  controller: TextEditingController(
                    text:
                        controller.selectedDate.isEmpty
                            ? ''
                            : controller.formatDate(controller.selectedDate),
                  ),
                ),
              ),
              SizedBox(width: SizeConstants.spaceBase),
              // Bus filter (placeholder - would need bus list)
              Expanded(
                child: TextField(
                  onChanged: controller.filterByBus,
                  decoration: InputDecoration(
                    labelText: 'رقم الحافلة',
                    hintText: 'أدخل رقم الحافلة',
                    prefixIcon: const Icon(Icons.directions_bus),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                    ),
                    filled: true,
                    fillColor:
                        Theme.of(context).brightness == Brightness.dark
                            ? ColorConstants.backgroundDark
                            : Colors.grey[100],
                  ),
                ),
              ),
              SizedBox(width: SizeConstants.spaceBase),
              // Clear filters button
              ElevatedButton.icon(
                onPressed: controller.clearFilters,
                icon: const Icon(Icons.clear),
                label: const Text('مسح الفلاتر'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[600],
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build content
  Widget _buildContent(BuildContext context) {
    return Obx(() {
      if (controller.isLoading && controller.absenceRequests.isEmpty) {
        return const LoadingWidget();
      }

      if (controller.errorMessage.isNotEmpty) {
        return CustomErrorWidget(
          message: controller.errorMessage,
          onRetry: () => controller.loadAbsenceRequests(refresh: true),
        );
      }

      if (controller.absenceRequests.isEmpty) {
        return const EmptyStateWidget(
          icon: Icons.event_busy,
          title: 'لا توجد طلبات غياب',
          description: 'لم يتم العثور على أي طلبات غياب',
        );
      }

      return RefreshIndicator(
        onRefresh: () => controller.loadAbsenceRequests(refresh: true),
        child: ListView.builder(
          padding: EdgeInsets.all(SizeConstants.spaceBase),
          itemCount:
              controller.absenceRequests.length +
              (controller.hasMoreData ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == controller.absenceRequests.length) {
              // Loading indicator for pagination
              return const Padding(
                padding: EdgeInsets.all(SizeConstants.spaceBase),
                child: Center(child: CircularProgressIndicator()),
              );
            }

            final request = controller.absenceRequests[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: AbsenceRequestCard(
                request: request,
                onViewDetails: () => _showDetailsDialog(context, request),
              ),
            );
          },
        ),
      );
    });
  }

  /// Select date
  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      controller.filterByDate(picked.toIso8601String().split('T')[0]);
    }
  }

  /// Show request details dialog
  void _showDetailsDialog(BuildContext context, AbsenceRequest request) {
    Get.dialog(
      AlertDialog(
        title: const Text('تفاصيل طلب الغياب'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow(
                'اسم الطالب:',
                request.student?.name ?? 'غير محدد',
              ),
              _buildDetailRow(
                'اسم ولي الأمر:',
                request.parent?.name ?? 'غير محدد',
              ),
              _buildDetailRow('المدرسة:', request.school?.name ?? 'غير محدد'),
              _buildDetailRow('الصف:', request.grade?.name ?? 'غير محدد'),
              _buildDetailRow('الحافلة:', request.bus?.name ?? 'غير محدد'),
              _buildDetailRow(
                'التاريخ:',
                controller.formatDate(request.attendanceDate),
              ),
              _buildDetailRow(
                'نوع الحضور:',
                controller.getAttendanceTypeDisplayName(request.attendanceType),
              ),
              _buildDetailRow(
                'تاريخ الإنشاء:',
                controller.formatDate(request.createdAt),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إغلاق')),
        ],
      ),
    );
  }

  /// Build detail row for dialog
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}

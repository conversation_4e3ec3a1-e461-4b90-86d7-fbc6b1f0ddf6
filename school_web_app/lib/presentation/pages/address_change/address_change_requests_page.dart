import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../controllers/address_change_controller.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';
import '../../widgets/common/empty_state_widget.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/address_change/address_change_request_card.dart';
import '../../../core/constants/size_constants.dart';

/// Page for managing address change requests
class AddressChangeRequestsPage extends GetView<AddressChangeController> {
  const AddressChangeRequestsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor:
            Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.backgroundDark
                : ColorConstants.background,
        body: ResponsiveSidebar(
          child: Column(
            children: [
              // Header with title and filters
              _buildHeader(context),
              // Tab bar for permanent and temporary addresses
              _buildTabBar(context),
              // Content
              Expanded(
                child: TabBarView(
                  children: [
                    // Permanent address change requests
                    _buildRequestsList(context, isPermanent: true),
                    // Temporary addresses
                    _buildRequestsList(context, isPermanent: false),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build header with title and filters
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceBase),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.cardDark
                : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'إدارة طلبات تغيير العنوان',
            style: TextStyle(
              fontSize: SizeConstants.fontMD,
              fontWeight: FontWeight.bold,
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? ColorConstants.textPrimaryDark
                      : ColorConstants.textPrimary,
            ),
          ),
          SizedBox(height: SizeConstants.spaceBase),
          // Search and filters
          Row(
            children: [
              // Search field
              Expanded(
                flex: 2,
                child: TextField(
                  onChanged: controller.searchRequests,
                  decoration: InputDecoration(
                    hintText: 'البحث في الطلبات...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                    ),
                    filled: true,
                    fillColor:
                        Theme.of(context).brightness == Brightness.dark
                            ? ColorConstants.backgroundDark
                            : Colors.grey[100],
                  ),
                ),
              ),
              SizedBox(width: SizeConstants.spaceBase),
              // Status filter
              Expanded(
                child: Obx(
                  () => DropdownButtonFormField<String>(
                    value:
                        controller.selectedStatus.isEmpty
                            ? null
                            : controller.selectedStatus,
                    onChanged:
                        (value) => controller.filterByStatus(value ?? ''),
                    decoration: InputDecoration(
                      labelText: 'حالة الطلب',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                      ),
                      filled: true,
                      fillColor:
                          Theme.of(context).brightness == Brightness.dark
                              ? ColorConstants.backgroundDark
                              : Colors.grey[100],
                    ),
                    items: const [
                      DropdownMenuItem(value: '', child: Text('جميع الحالات')),
                      DropdownMenuItem(value: '1', child: Text('مقبول')),
                      DropdownMenuItem(value: '2', child: Text('مرفوض')),
                      DropdownMenuItem(value: '3', child: Text('في الانتظار')),
                    ],
                  ),
                ),
              ),
              SizedBox(width: SizeConstants.spaceBase),
              // Clear filters button
              ElevatedButton.icon(
                onPressed: controller.clearFilters,
                icon: const Icon(Icons.clear),
                label: const Text('مسح الفلاتر'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[600],
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build tab bar
  Widget _buildTabBar(BuildContext context) {
    return Container(
      color:
          Theme.of(context).brightness == Brightness.dark
              ? ColorConstants.cardDark
              : Colors.white,
      child: TabBar(
        labelColor: ColorConstants.primary,
        unselectedLabelColor: Colors.grey,
        indicatorColor: ColorConstants.primary,
        tabs: const [
          Tab(icon: Icon(Icons.location_on), text: 'الطلبات الدائمة'),
          Tab(icon: Icon(Icons.location_city), text: 'العناوين المؤقتة'),
        ],
      ),
    );
  }

  /// Build requests list
  Widget _buildRequestsList(BuildContext context, {required bool isPermanent}) {
    return Obx(() {
      if (controller.isLoading &&
          (isPermanent
              ? controller.addressChangeRequests.isEmpty
              : controller.temporaryAddresses.isEmpty)) {
        return const LoadingWidget();
      }

      if (controller.errorMessage.isNotEmpty) {
        return CustomErrorWidget(
          message: controller.errorMessage,
          onRetry:
              () =>
                  isPermanent
                      ? controller.loadAddressChangeRequests(refresh: true)
                      : controller.loadTemporaryAddresses(refresh: true),
        );
      }

      final requests =
          isPermanent
              ? controller.addressChangeRequests
              : controller.temporaryAddresses;

      if (requests.isEmpty) {
        return EmptyStateWidget(
          icon: Icons.location_off,
          title:
              isPermanent
                  ? 'لا توجد طلبات تغيير عنوان'
                  : 'لا توجد عناوين مؤقتة',
          description:
              isPermanent
                  ? 'لم يتم العثور على أي طلبات تغيير عنوان'
                  : 'لم يتم العثور على أي عناوين مؤقتة',
        );
      }

      return RefreshIndicator(
        onRefresh:
            () =>
                isPermanent
                    ? controller.loadAddressChangeRequests(refresh: true)
                    : controller.loadTemporaryAddresses(refresh: true),
        child: ListView.builder(
          padding: EdgeInsets.all(SizeConstants.spaceBase),
          itemCount: requests.length + (controller.hasMoreData ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == requests.length) {
              // Loading indicator for pagination
              return const Padding(
                padding: EdgeInsets.all(SizeConstants.spaceBase),
                child: Center(child: CircularProgressIndicator()),
              );
            }

            final request = requests[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: AddressChangeRequestCard(
                request: request,
                onAccept: () => _showAcceptDialog(context, request.id),
                onRefuse: () => _showRefuseDialog(context, request.id),
                onViewDetails: () => _showDetailsDialog(context, request),
              ),
            );
          },
        ),
      );
    });
  }

  /// Show accept confirmation dialog
  void _showAcceptDialog(BuildContext context, int requestId) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد القبول'),
        content: const Text('هل أنت متأكد من قبول طلب تغيير العنوان؟'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.acceptRequest(requestId);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('قبول'),
          ),
        ],
      ),
    );
  }

  /// Show refuse dialog with reason input
  void _showRefuseDialog(BuildContext context, int requestId) {
    final reasonController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('رفض الطلب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('يرجى إدخال سبب الرفض:'),
            SizedBox(height: SizeConstants.spaceBase),
            TextField(
              controller: reasonController,
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: 'سبب الرفض...',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              if (reasonController.text.trim().isNotEmpty) {
                Get.back();
                controller.refuseRequest(
                  requestId,
                  reasonController.text.trim(),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('رفض'),
          ),
        ],
      ),
    );
  }

  /// Show request details dialog
  void _showDetailsDialog(BuildContext context, request) {
    Get.dialog(
      AlertDialog(
        title: const Text('تفاصيل الطلب'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('اسم الطالب:', request.studentName),
              _buildDetailRow(
                'اسم ولي الأمر:',
                request.parentName ?? 'غير محدد',
              ),
              _buildDetailRow('المدرسة:', request.schoolName),
              _buildDetailRow('الصف:', request.gradeName),
              _buildDetailRow('الحافلة:', request.busName),
              _buildDetailRow(
                'العنوان القديم:',
                request.oldAddress ?? 'غير محدد',
              ),
              _buildDetailRow('العنوان الجديد:', request.address ?? 'غير محدد'),
              _buildDetailRow(
                'الحالة:',
                controller.getStatusText(request.status),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إغلاق')),
        ],
      ),
    );
  }

  /// Build detail row for dialog
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/constants/size_constants.dart';
import '../../../core/performance/performance_monitor.dart';
import '../../../core/utils/logger.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../core/widgets/responsive_builder.dart';
import '../../controllers/auth_controller.dart';
import '../../routes/app_routes.dart';
import '../../widgets/accessibility/accessible_button.dart';
import '../../widgets/accessibility/accessible_text_field.dart';
import '../../widgets/auth/auth_header.dart';

/// LoginPage for user authentication
/// Following Single Responsibility Principle by focusing only on login UI
class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _authController = Get.find<AuthController>();
  bool _obscurePassword = true;
  bool _rememberMe = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// Validate form and login
  Future<void> _login() async {
    LoggerService.info('Login button pressed');

    if (_formKey.currentState!.validate()) {
      LoggerService.debug('Form validation passed, attempting login');

      try {
        final email = _emailController.text.trim();
        final password = _passwordController.text;

        LoggerService.debug(
          'Calling auth controller login method',
          data: {'email': email, 'rememberMe': _rememberMe},
        );

        final success = await _authController.login(email, password);

        if (success) {
          LoggerService.success('Login successful, navigating to home');
          // Announce success to screen readers (check mounted)
          if (mounted) {
            AccessibilityHelper.announceFormSuccess('تم تسجيل الدخول بنجاح');
          }
          Get.offAllNamed(AppRoutes.home);
        } else {
          LoggerService.error(
            'Login failed',
            error: _authController.errorMessage,
          );
          // Announce error to screen readers (check mounted)
          if (mounted) {
            AccessibilityHelper.announceFormError(_authController.errorMessage);
          }
          Get.snackbar(
            'خطأ في تسجيل الدخول',
            _authController.errorMessage,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: ColorConstants.error.withAlpha(150),
            colorText: ColorConstants.white,
            margin: EdgeInsets.all(SizeConstants.spaceMD),
          );
        }
      } catch (e) {
        LoggerService.error('Unexpected error during login', error: e);
        Get.snackbar(
          'خطأ غير متوقع',
          'حدث خطأ غير متوقع أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: ColorConstants.error.withAlpha(150),
          colorText: ColorConstants.white,
          margin: EdgeInsets.all(SizeConstants.spaceMD),
        );
      }
    } else {
      LoggerService.warning('Form validation failed');
      // Announce validation errors to screen readers
      AccessibilityHelper.announceFormError('يرجى تصحيح الأخطاء في النموذج');
    }
  }

  @override
  Widget build(BuildContext context) {
    return PerformanceMonitor.monitorWidgetBuild('LoginPage', () {
      return Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                ColorConstants.primary,
                ColorConstants.primary.withValues(alpha: 0.8),
                ColorConstants.primary.withValues(alpha: 0.9),
              ],
            ),
          ),
          child: ResponsiveBuilder(
            builder: (context, screenType) {
              return SafeArea(
                child: Center(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.all(SizeConstants.spaceLG),
                    child: Container(
                      width: ResponsiveUtils.getResponsiveValue<double>(
                        context: context,
                        mobile: MediaQuery.of(context).size.width * 0.9,
                        tablet: 400,
                        desktop: 480,
                      ),
                      padding: EdgeInsets.all(SizeConstants.spaceLG),
                      decoration: BoxDecoration(
                        color: ColorConstants.white,
                        borderRadius: BorderRadius.circular(
                          SizeConstants.radiusXL,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: ColorConstants.black.withValues(alpha: 0.3),
                            blurRadius: 30,
                            offset: const Offset(0, 15),
                          ),
                          BoxShadow(
                            color: ColorConstants.black.withValues(alpha: 0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Header with logo and title
                          const AuthHeader(
                            title: 'تسجيل الدخول',
                            subtitle: 'أدخل بيانات الدخول للوصول إلى حسابك',
                          ),
                          SizedBox(height: SizeConstants.spaceLG),

                          // Login form
                          Form(
                            key: _formKey,
                            child: Column(
                              children: [
                                // Email field
                                AccessibleTextField(
                                  controller: _emailController,
                                  label: 'البريد الإلكتروني',
                                  hint: 'أدخل البريد الإلكتروني',
                                  prefixIcon: const Icon(Icons.email_outlined),
                                  keyboardType: TextInputType.emailAddress,
                                  required: true,
                                  semanticLabel: 'حقل البريد الإلكتروني',
                                  semanticHint: 'مطلوب لتسجيل الدخول',
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'الرجاء إدخال البريد الإلكتروني';
                                    }
                                    if (!GetUtils.isEmail(value)) {
                                      return 'الرجاء إدخال بريد إلكتروني صحيح';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: SizeConstants.spaceSM),

                                // Password field
                                AccessibleTextField(
                                  controller: _passwordController,
                                  label: 'كلمة المرور',
                                  hint: 'أدخل كلمة المرور',
                                  prefixIcon: const Icon(Icons.lock_outline),
                                  obscureText: _obscurePassword,
                                  required: true,
                                  semanticLabel: 'حقل كلمة المرور',
                                  semanticHint: 'مطلوب لتسجيل الدخول',
                                  suffixIcon: IconButton(
                                    icon: Icon(
                                      _obscurePassword
                                          ? Icons.visibility_outlined
                                          : Icons.visibility_off_outlined,
                                      color: ColorConstants.iconInputColor,
                                    ),
                                    onPressed: () {
                                      setState(() {
                                        _obscurePassword = !_obscurePassword;
                                      });
                                    },
                                    tooltip:
                                        _obscurePassword
                                            ? 'إظهار كلمة المرور'
                                            : 'إخفاء كلمة المرور',
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'الرجاء إدخال كلمة المرور';
                                    }
                                    if (value.length < 6) {
                                      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: SizeConstants.spaceSM),

                                // Remember me and forgot password
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    // Remember me with enhanced styling
                                    Row(
                                      children: [
                                        Transform.scale(
                                          scale: 1.2,
                                          child: Checkbox(
                                            value: _rememberMe,
                                            activeColor: ColorConstants.primary,
                                            checkColor: Colors.white,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(SizeConstants.radiusXS),
                                            ),
                                            onChanged: (value) {
                                              setState(() {
                                                _rememberMe = value ?? false;
                                              });
                                            },
                                          ),
                                        ),
                                        SizedBox(width: SizeConstants.spaceXS),
                                        Text(
                                          'تذكرني',
                                          style: TextStyle(
                                            color: ColorConstants.text,
                                            fontSize: SizeConstants.fontSM,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),

                                    // Forgot password with enhanced styling
                                    TextButton(
                                      onPressed: () {
                                        Get.toNamed(AppRoutes.forgotPassword);
                                      },
                                      style: TextButton.styleFrom(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: SizeConstants.spaceSM,
                                          vertical: SizeConstants.spaceXS,
                                        ),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                      ),
                                      child: Text(
                                        'نسيت كلمة المرور؟',
                                        style: TextStyle(
                                          color: ColorConstants.primary,
                                          fontSize: SizeConstants.fontSM,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: SizeConstants.spaceLG),

                                // Login button with enhanced styling
                                Obx(() {
                                  return AccessibleButton(
                                    text: 'تسجيل الدخول',
                                    isLoading: _authController.isLoading,
                                    onPressed:
                                        _authController.isLoading
                                            ? null
                                            : _login,
                                    height: SizeConstants.buttonHeightLG,
                                    icon: Icons.login_rounded,
                                    semanticLabel: 'زر تسجيل الدخول',
                                    semanticHint:
                                        'اضغط لتسجيل الدخول إلى حسابك',
                                    width: double.infinity,
                                  );
                                }),
                                SizedBox(height: SizeConstants.spaceMD),

                                // Register link with enhanced styling
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    vertical: SizeConstants.spaceMD,
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        'ليس لديك حساب؟',
                                        style: TextStyle(
                                          color: ColorConstants.textSecondary,
                                          fontSize: SizeConstants.fontBase,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      SizedBox(width: SizeConstants.spaceXS),
                                      AccessibleButton(
                                        text: 'إنشاء حساب جديد',
                                        onPressed: () {
                                          Get.toNamed(AppRoutes.register);
                                        },
                                        isOutlined: true,
                                        backgroundColor: Colors.transparent,
                                        textColor: ColorConstants.primary,
                                        semanticLabel: 'إنشاء حساب جديد',
                                        semanticHint:
                                            'الانتقال إلى صفحة التسجيل',
                                        width: null, // Let it size itself
                                        height: SizeConstants.buttonHeightMD,
                                      ),
                                    ],
                                  ),
                                ),

                                // Test login button (only in debug mode)
                                SizedBox(height: SizeConstants.spaceMD),
                                OutlinedButton(
                                  onPressed: () async {
                                    LoggerService.info(
                                      'Test login button pressed',
                                    );

                                    // Set test credentials
                                    _emailController.text = '<EMAIL>';
                                    _passwordController.text = 'password';

                                    // Attempt login with test credentials
                                    await _login();
                                  },
                                  style: OutlinedButton.styleFrom(
                                    side: BorderSide(
                                      color: ColorConstants.primary,
                                    ),
                                    padding: EdgeInsets.symmetric(
                                      horizontal: SizeConstants.spaceMD,
                                      vertical: SizeConstants.spaceXS,
                                    ),
                                  ),
                                  child: Text(
                                    'تسجيل دخول تجريبي',
                                    style: TextStyle(
                                      color: ColorConstants.primary,
                                      fontSize: SizeConstants.fontSM,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      );
    });
  }
}

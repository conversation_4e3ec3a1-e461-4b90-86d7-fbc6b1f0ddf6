import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/constants/size_constants.dart';
import '../../../core/performance/lazy_loader.dart';
import '../../../core/performance/performance_monitor.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/buses_controller.dart';
import '../../routes/app_routes.dart';
import '../../widgets/accessibility/accessible_button.dart';
import '../../widgets/buses/buses_header.dart';
import '../../widgets/buses/buses_table.dart';
import '../../widgets/common/custom_loading_widget.dart';
import '../../widgets/common/custom_error_widget.dart';
import '../../widgets/layout/responsive_sidebar.dart';

/// Buses page
/// Following Single Responsibility Principle by focusing only on buses UI
class BusesPage extends StatelessWidget {
  const BusesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PerformanceMonitor.monitorWidgetBuild('BusesPage', () {
      // Get the buses controller
      final controller = Get.find<BusesController>();

      return Scaffold(
        backgroundColor: ColorConstants.background,
        body: ResponsiveSidebar(child: _buildBusesContent(context, controller)),
      );
    });
  }

  /// Build buses content
  Widget _buildBusesContent(BuildContext context, BusesController controller) {
    return RefreshIndicator(
      onRefresh: () async {
        await controller.refreshBuses();
      },
      child: SingleChildScrollView(
        padding: EdgeInsets.all(SizeConstants.spaceMD),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Buses header with title, search, and add button
            BusesHeader(controller: controller),
            SizedBox(height: SizeConstants.spaceLG),

            // Buses content
            Obx(() => _buildBusesBody(context, controller)),
          ],
        ),
      ),
    );
  }

  /// Build buses body based on state
  Widget _buildBusesBody(BuildContext context, BusesController controller) {
    if (controller.isLoading && controller.buses.isEmpty) {
      return const CustomLoadingWidget(message: 'Loading buses...');
    }

    if (controller.errorMessage.isNotEmpty && controller.buses.isEmpty) {
      return CustomErrorWidget(
        message: controller.errorMessage,
        onRetry: () => controller.refreshBuses(),
      );
    }

    if (controller.buses.isEmpty) {
      return _buildEmptyState(context, controller);
    }

    return _buildBusesTable(context, controller);
  }

  /// Build empty state
  Widget _buildEmptyState(BuildContext context, BusesController controller) {
    final isDesktop = ResponsiveUtils.isDesktop(context);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        vertical: isDesktop ? 80 : 60,
        horizontal: 24,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Empty state icon
          Container(
            width: isDesktop ? 120 : 100,
            height: isDesktop ? 120 : 100,
            decoration: BoxDecoration(
              color: ColorConstants.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(60),
            ),
            child: Icon(
              Icons.directions_bus_rounded,
              size: isDesktop ? 60 : 50,
              color: ColorConstants.primary,
            ),
          ),
          SizedBox(height: SizeConstants.spaceLG),

          // Empty state title
          Text(
            controller.searchQuery.isNotEmpty
                ? 'No buses found'
                : 'No buses available',
            style: TextStyle(
              fontSize: isDesktop ? 24 : 20,
              fontWeight: FontWeight.bold,
              color: ColorConstants.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: SizeConstants.spaceSM),

          // Empty state description
          Text(
            controller.searchQuery.isNotEmpty
                ? 'Try adjusting your search criteria to find buses.'
                : 'Get started by adding your first bus to the system.',
            style: TextStyle(
              fontSize: isDesktop ? 16 : 14,
              color: ColorConstants.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: SizeConstants.spaceXL),

          // Add bus button (only show if no search query)
          if (controller.searchQuery.isEmpty)
            AccessibleButton(
              text: 'Add First Bus',
              icon: Icons.add_rounded,
              onPressed: () => Get.toNamed(AppRoutes.addBus),
              backgroundColor: ColorConstants.primary,
              textColor: Colors.white,
              semanticLabel: 'Add first bus to the system',
              semanticHint: 'Navigate to add bus form',
            ),
        ],
      ),
    );
  }

  /// Build buses table with lazy loading
  Widget _buildBusesTable(BuildContext context, BusesController controller) {
    return LazyLoader(
      builder:
          () => Column(
            children: [
              // Buses table
              BusesTable(controller: controller),

              // Load more button for pagination
              if (controller.currentPage < controller.lastPage)
                Padding(
                  padding: const EdgeInsets.only(top: 24),
                  child: Obx(
                    () => ElevatedButton.icon(
                      onPressed:
                          controller.isLoading
                              ? null
                              : () => controller.loadMoreBuses(),
                      icon:
                          controller.isLoading
                              ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                              : const Icon(Icons.expand_more_rounded),
                      label: Text(
                        controller.isLoading ? 'Loading...' : 'Load More Buses',
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorConstants.primary,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(
                          horizontal: SizeConstants.spaceLG,
                          vertical: SizeConstants.spaceSM,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            SizeConstants.radiusSM,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

              // Pagination info
              if (controller.buses.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 16),
                  child: Text(
                    'Showing ${controller.buses.length} of ${controller.total} buses',
                    style: TextStyle(
                      fontSize: SizeConstants.fontSM,
                      color: ColorConstants.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
            ],
          ),
      placeholder: const Center(child: CircularProgressIndicator()),
      delay: const Duration(milliseconds: 100),
    );
  }
}

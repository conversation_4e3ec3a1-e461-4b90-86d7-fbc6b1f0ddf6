import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/bus.dart';
import '../../controllers/buses_controller.dart';
import '../../widgets/buses/bus_form.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../../core/constants/size_constants.dart';

/// Edit bus page
/// Following Single Responsibility Principle by focusing only on edit bus UI
class EditBusPage extends StatelessWidget {
  const EditBusPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Get the buses controller
    final controller = Get.find<BusesController>();

    // Get bus from arguments
    final Bus? bus = Get.arguments as Bus?;

    if (bus == null) {
      return Scaffold(
        backgroundColor: ColorConstants.background,
        body: const Center(
          child: Text('Bus not found'),
        ),
      );
    }

    return Scaffold(
      backgroundColor: ColorConstants.background,
      body: ResponsiveSidebar(
        child: _buildEditBusContent(context, controller, bus),
      ),
    );
  }

  /// Build edit bus content
  Widget _buildEditBusContent(BuildContext context, BusesController controller, Bus bus) {
    final isDesktop = ResponsiveUtils.isDesktop(context);

    return SingleChildScrollView(
      padding: EdgeInsets.all(SizeConstants.spaceBase),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(context, isDesktop, bus),
          SizedBox(height: SizeConstants.spaceLG),

          // Form
          BusForm(
            controller: controller,
            formKey: controller.editBusFormKey,
            isEdit: true,
            bus: bus,
            onSubmit: () => controller.updateBus(bus.id),
          ),
        ],
      ),
    );
  }

  /// Build header
  Widget _buildHeader(BuildContext context, bool isDesktop, Bus bus) {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceLG),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Get.back(),
            icon: Icon(
              Icons.arrow_back_rounded,
              color: ColorConstants.textSecondary,
            ),
            style: IconButton.styleFrom(
              backgroundColor: Colors.grey.shade100,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              ),
            ),
          ),
          SizedBox(width: SizeConstants.spaceBase),

          // Icon and title
          Container(
            padding: EdgeInsets.all(SizeConstants.spaceSM),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            ),
            child: Icon(
              Icons.edit_rounded,
              color: Colors.orange,
              size: isDesktop ? 28 : 24,
            ),
          ),
          SizedBox(width: SizeConstants.spaceBase),

          // Title and subtitle
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Edit Bus',
                  style: TextStyle(
                    fontSize: isDesktop ? 28 : 24,
                    fontWeight: FontWeight.bold,
                    color: ColorConstants.textPrimary,
                  ),
                ),
                SizedBox(height: SizeConstants.spaceXS),
                Text(
                  'Update information for "${bus.name ?? 'Bus'}"',
                  style: TextStyle(
                    fontSize: isDesktop ? 16 : 14,
                    color: ColorConstants.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/driver.dart';
import '../../../data/models/driver_model.dart';
import '../../controllers/drivers_controller.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../../core/constants/size_constants.dart';

/// Edit driver page
class EditDriverPage extends StatefulWidget {
  const EditDriverPage({super.key});

  @override
  State<EditDriverPage> createState() => _EditDriverPageState();
}

class _EditDriverPageState extends State<EditDriverPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _passwordController = TextEditingController();

  DateTime? _selectedBirthDate;
  DateTime? _selectedJoiningDate;
  int? _selectedGenderId;
  int? _selectedReligionId;

  int? _selectedBusId;
  int? _selectedStatus;

  Driver? _driver;
  DriversController? _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<DriversController>();
    _driver = Get.arguments as Driver?;
    
    if (_driver != null) {
      _initializeForm();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _usernameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _initializeForm() {
    if (_driver == null) return;

    _nameController.text = _driver!.name ?? '';
    _usernameController.text = _driver!.username ?? '';
    _emailController.text = _driver!.email ?? '';
    _phoneController.text = _driver!.phone ?? '';
    _addressController.text = _driver!.address ?? '';
    _cityController.text = _driver!.cityName ?? '';

    _selectedGenderId = _driver!.genderId;
    _selectedReligionId = _driver!.religionId;
    _selectedBusId = _driver!.busId;
    _selectedStatus = _driver!.status;

    // Parse dates with better error handling
    if (_driver!.birthDate != null && _driver!.birthDate!.isNotEmpty) {
      try {
        // Try different date formats
        _selectedBirthDate = DateTime.parse(_driver!.birthDate!);
      } catch (e) {
        try {
          // Try alternative format (dd/MM/yyyy)
          final parts = _driver!.birthDate!.split('/');
          if (parts.length == 3) {
            _selectedBirthDate = DateTime(
              int.parse(parts[2]), // year
              int.parse(parts[1]), // month
              int.parse(parts[0]), // day
            );
          }
        } catch (e2) {
          // If all parsing fails, leave as null
          _selectedBirthDate = null;
        }
      }
    }

    if (_driver!.joiningDate != null && _driver!.joiningDate!.isNotEmpty) {
      try {
        // Try different date formats
        _selectedJoiningDate = DateTime.parse(_driver!.joiningDate!);
      } catch (e) {
        try {
          // Try alternative format (dd/MM/yyyy)
          final parts = _driver!.joiningDate!.split('/');
          if (parts.length == 3) {
            _selectedJoiningDate = DateTime(
              int.parse(parts[2]), // year
              int.parse(parts[1]), // month
              int.parse(parts[0]), // day
            );
          }
        } catch (e2) {
          // If all parsing fails, leave as null
          _selectedJoiningDate = null;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_driver == null) {
      return Scaffold(
        backgroundColor: ColorConstants.background,
        body: ResponsiveSidebar(
          child: _buildErrorState(),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? ColorConstants.backgroundDark
          : ColorConstants.background,
      body: ResponsiveSidebar(
        child: _buildContent(context),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);

    return SingleChildScrollView(
      padding: EdgeInsets.all(SizeConstants.spaceBase),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(context),
          SizedBox(height: SizeConstants.spaceLG),

          // Form
          Container(
            padding: EdgeInsets.all(SizeConstants.spaceLG),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Form(
              key: _formKey,
              child: isDesktop
                  ? _buildDesktopForm(context)
                  : _buildMobileForm(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceLG),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            ColorConstants.primary.withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(SizeConstants.radiusXL),
        boxShadow: [
          BoxShadow(
            color: ColorConstants.primary.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
        border: Border.all(
          color: ColorConstants.primary.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Get.back(),
            icon: Icon(
              Icons.arrow_back_rounded,
              color: ColorConstants.primary,
            ),
            style: IconButton.styleFrom(
              backgroundColor: ColorConstants.primary.withValues(alpha: 0.1),
              padding: EdgeInsets.all(SizeConstants.spaceSM),
            ),
          ),
          SizedBox(width: SizeConstants.spaceBase),

          // Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تعديل السائق',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                      context,
                      mobile: 24,
                      tablet: 28,
                      desktop: 32,
                    ),
                    fontWeight: FontWeight.w800,
                    color: ColorConstants.textPrimary,
                    letterSpacing: -0.5,
                  ),
                ),
                SizedBox(height: SizeConstants.spaceXS),
                Text(
                  'تعديل معلومات السائق: ${_driver?.name ?? ""}',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                      context,
                      mobile: 14,
                      tablet: 16,
                      desktop: 18,
                    ),
                    color: ColorConstants.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopForm(BuildContext context) {
    return Column(
      children: [
        // Personal Information Section
        _buildSectionTitle('المعلومات الشخصية', Icons.person_rounded),
        SizedBox(height: SizeConstants.spaceBase),
        Row(
          children: [
            Expanded(child: _buildNameField()),
            SizedBox(width: SizeConstants.spaceBase),
            Expanded(child: _buildUsernameField()),
          ],
        ),
        SizedBox(height: SizeConstants.spaceBase),
        Row(
          children: [
            Expanded(child: _buildEmailField()),
            SizedBox(width: SizeConstants.spaceBase),
            Expanded(child: _buildPhoneField()),
          ],
        ),
        SizedBox(height: SizeConstants.spaceBase),
        Row(
          children: [
            Expanded(child: _buildPasswordField()),
            SizedBox(width: SizeConstants.spaceBase),
            Expanded(child: Container()), // Empty space
          ],
        ),
        SizedBox(height: SizeConstants.spaceXL),

        // Demographics Section
        _buildSectionTitle('المعلومات الديموغرافية', Icons.info_rounded),
        SizedBox(height: SizeConstants.spaceBase),
        Row(
          children: [
            Expanded(child: _buildGenderDropdown()),
            SizedBox(width: SizeConstants.spaceBase),
            Expanded(child: _buildReligionDropdown()),
          ],
        ),
        SizedBox(height: SizeConstants.spaceBase),
        Row(
          children: [
            Expanded(child: _buildBirthDateField()),
            SizedBox(width: SizeConstants.spaceBase),
            Expanded(child: Container()), // Empty space
          ],
        ),
        SizedBox(height: SizeConstants.spaceXL),

        // Employment Section
        _buildSectionTitle('معلومات التوظيف', Icons.work_rounded),
        SizedBox(height: SizeConstants.spaceBase),
        Row(
          children: [
            Expanded(child: _buildJoiningDateField()),
            SizedBox(width: SizeConstants.spaceBase),
            Expanded(child: _buildBusDropdown()),
          ],
        ),
        SizedBox(height: SizeConstants.spaceBase),
        Row(
          children: [
            Expanded(child: _buildAddressField()),
            SizedBox(width: SizeConstants.spaceBase),
            Expanded(child: _buildCityField()),
          ],
        ),
        SizedBox(height: SizeConstants.spaceBase),
        Row(
          children: [
            Expanded(child: _buildStatusDropdown()),
            SizedBox(width: SizeConstants.spaceBase),
            Expanded(child: Container()), // Empty space
          ],
        ),
        SizedBox(height: SizeConstants.spaceXL),

        // Action buttons
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildMobileForm(BuildContext context) {
    return Column(
      children: [
        // Personal Information Section
        _buildSectionTitle('المعلومات الشخصية', Icons.person_rounded),
        SizedBox(height: SizeConstants.spaceBase),
        _buildNameField(),
        SizedBox(height: SizeConstants.spaceBase),
        _buildUsernameField(),
        SizedBox(height: SizeConstants.spaceBase),
        _buildEmailField(),
        SizedBox(height: SizeConstants.spaceBase),
        _buildPhoneField(),
        SizedBox(height: SizeConstants.spaceBase),
        _buildPasswordField(),
        SizedBox(height: SizeConstants.spaceXL),

        // Demographics Section
        _buildSectionTitle('المعلومات الديموغرافية', Icons.info_rounded),
        SizedBox(height: SizeConstants.spaceBase),
        _buildGenderDropdown(),
        SizedBox(height: SizeConstants.spaceBase),
        _buildReligionDropdown(),
        SizedBox(height: SizeConstants.spaceBase),

        _buildBirthDateField(),
        SizedBox(height: SizeConstants.spaceXL),

        // Employment Section
        _buildSectionTitle('معلومات التوظيف', Icons.work_rounded),
        SizedBox(height: SizeConstants.spaceBase),
        _buildJoiningDateField(),
        SizedBox(height: SizeConstants.spaceBase),
        _buildBusDropdown(),
        SizedBox(height: SizeConstants.spaceBase),
        _buildAddressField(),
        SizedBox(height: SizeConstants.spaceBase),
        _buildCityField(),
        SizedBox(height: SizeConstants.spaceBase),
        _buildStatusDropdown(),
        SizedBox(height: SizeConstants.spaceXL),

        // Action buttons
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(SizeConstants.spaceXS),
          decoration: BoxDecoration(
            color: ColorConstants.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
          ),
          child: Icon(
            icon,
            color: ColorConstants.primary,
            size: SizeConstants.iconSM,
          ),
        ),
        SizedBox(width: SizeConstants.spaceSM),
        Text(
          title,
          style: TextStyle(
            fontSize: SizeConstants.fontSM,
            fontWeight: FontWeight.w700,
            color: ColorConstants.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      decoration: InputDecoration(
        labelText: 'الاسم الكامل *',
        hintText: 'أدخل الاسم الكامل',
        prefixIcon: const Icon(Icons.person_outline),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        ),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'الاسم مطلوب';
        }
        return null;
      },
    );
  }

  Widget _buildUsernameField() {
    return TextFormField(
      controller: _usernameController,
      decoration: InputDecoration(
        labelText: 'اسم المستخدم *',
        hintText: 'أدخل اسم المستخدم',
        prefixIcon: const Icon(Icons.account_circle_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        ),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'اسم المستخدم مطلوب';
        }
        return null;
      },
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      decoration: InputDecoration(
        labelText: 'البريد الإلكتروني',
        hintText: 'أدخل البريد الإلكتروني',
        prefixIcon: const Icon(Icons.email_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        ),
      ),
      validator: (value) {
        if (value != null && value.trim().isNotEmpty) {
          // Basic email validation
          final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
          if (!emailRegex.hasMatch(value.trim())) {
            return 'البريد الإلكتروني غير صحيح';
          }
        }
        return null;
      },
    );
  }

  Widget _buildPhoneField() {
    return TextFormField(
      controller: _phoneController,
      keyboardType: TextInputType.phone,
      decoration: InputDecoration(
        labelText: 'رقم الهاتف',
        hintText: 'أدخل رقم الهاتف',
        prefixIcon: const Icon(Icons.phone_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        ),
      ),
      validator: (value) {
        if (value != null && value.trim().isNotEmpty) {
          // Basic phone validation - should contain only numbers, spaces, +, -, ()
          final phoneRegex = RegExp(r'^[\d\s\+\-\(\)]+$');
          if (!phoneRegex.hasMatch(value.trim())) {
            return 'رقم الهاتف غير صحيح';
          }
        }
        return null;
      },
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: true,
      decoration: InputDecoration(
        labelText: 'كلمة المرور (اختياري)',
        hintText: 'أدخل كلمة مرور جديدة',
        prefixIcon: const Icon(Icons.lock_outline),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        ),
        helperText: 'اتركه فارغاً للاحتفاظ بكلمة المرور الحالية',
      ),
    );
  }

  Widget _buildGenderDropdown() {
    return Obx(() {
      final genders = _controller?.genderOptions ?? [];

      // Ensure the selected value exists in the list
      final validSelectedGenderId = genders.any((g) => g['id'] == _selectedGenderId)
          ? _selectedGenderId
          : null;

      return DropdownButtonFormField<int>(
        value: validSelectedGenderId,
        decoration: InputDecoration(
          labelText: 'الجنس',
          prefixIcon: const Icon(Icons.person_outline),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
          ),
        ),
        items: genders.map((gender) {
          return DropdownMenuItem<int>(
            value: gender['id'] as int,
            child: Text(gender['name'] as String),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedGenderId = value;
          });
        },
      );
    });
  }

  Widget _buildReligionDropdown() {
    return Obx(() {
      final religions = _controller?.religionOptions ?? [];

      // Ensure the selected value exists in the list
      final validSelectedReligionId = religions.any((r) => r['id'] == _selectedReligionId)
          ? _selectedReligionId
          : null;

      return DropdownButtonFormField<int>(
        value: validSelectedReligionId,
        decoration: InputDecoration(
          labelText: 'الديانة',
          prefixIcon: const Icon(Icons.account_balance_outlined),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
          ),
        ),
        items: religions.map((religion) {
          return DropdownMenuItem<int>(
            value: religion['id'] as int,
            child: Text(religion['name'] as String),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedReligionId = value;
          });
        },
      );
    });
  }



  Widget _buildBusDropdown() {
    return Obx(() {
      final buses = _controller?.availableBuses ?? [];
      // Add "no bus" option
      final busOptions = [
        {'id': 0, 'name': 'غير مخصص'},
        ...buses,
      ];

      // Ensure the selected value exists in the list
      final validSelectedBusId = busOptions.any((b) => b['id'] == _selectedBusId)
          ? _selectedBusId
          : 0; // Default to "غير مخصص"

      return DropdownButtonFormField<int>(
        value: validSelectedBusId,
        decoration: InputDecoration(
          labelText: 'الحافلة المخصصة',
          prefixIcon: const Icon(Icons.directions_bus_outlined),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
          ),
        ),
        items: busOptions.map((bus) {
          return DropdownMenuItem<int>(
            value: bus['id'] as int,
            child: Text(bus['name'] as String),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedBusId = value;
          });
        },
      );
    });
  }

  Widget _buildBirthDateField() {
    return InkWell(
      onTap: () => _selectBirthDate(),
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: 'تاريخ الميلاد',
          prefixIcon: const Icon(Icons.calendar_today_outlined),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
          ),
        ),
        child: Text(
          _selectedBirthDate != null
              ? '${_selectedBirthDate!.year}-${_selectedBirthDate!.month.toString().padLeft(2, '0')}-${_selectedBirthDate!.day.toString().padLeft(2, '0')}'
              : 'اختر تاريخ الميلاد',
          style: TextStyle(
            color: _selectedBirthDate != null
                ? ColorConstants.textPrimary
                : ColorConstants.textSecondary,
          ),
        ),
      ),
    );
  }

  Widget _buildJoiningDateField() {
    return InkWell(
      onTap: () => _selectJoiningDate(),
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: 'تاريخ الانضمام',
          prefixIcon: const Icon(Icons.work_outline),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
          ),
        ),
        child: Text(
          _selectedJoiningDate != null
              ? '${_selectedJoiningDate!.year}-${_selectedJoiningDate!.month.toString().padLeft(2, '0')}-${_selectedJoiningDate!.day.toString().padLeft(2, '0')}'
              : 'اختر تاريخ الانضمام',
          style: TextStyle(
            color: _selectedJoiningDate != null
                ? ColorConstants.textPrimary
                : ColorConstants.textSecondary,
          ),
        ),
      ),
    );
  }

  Widget _buildAddressField() {
    return TextFormField(
      controller: _addressController,
      maxLines: 2,
      decoration: InputDecoration(
        labelText: 'العنوان',
        hintText: 'أدخل العنوان',
        prefixIcon: const Icon(Icons.location_on_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        ),
      ),
    );
  }

  Widget _buildCityField() {
    return TextFormField(
      controller: _cityController,
      decoration: InputDecoration(
        labelText: 'المدينة',
        hintText: 'أدخل المدينة',
        prefixIcon: const Icon(Icons.location_city_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        ),
      ),
    );
  }

  Widget _buildStatusDropdown() {
    final statusOptions = [
      {'id': 1, 'name': 'نشط'},
      {'id': 0, 'name': 'غير نشط'},
    ];

    return DropdownButtonFormField<int>(
      value: _selectedStatus,
      decoration: InputDecoration(
        labelText: 'حالة السائق',
        prefixIcon: const Icon(Icons.toggle_on_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        ),
      ),
      items: statusOptions.map((status) {
        return DropdownMenuItem<int>(
          value: status['id'] as int,
          child: Row(
            children: [
              Icon(
                status['id'] == 1 ? Icons.check_circle : Icons.cancel,
                color: status['id'] == 1 ? Colors.green : Colors.red,
                size: SizeConstants.iconSM,
              ),
              SizedBox(width: SizeConstants.spaceXS),
              Text(status['name'] as String),
            ],
          ),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedStatus = value;
        });
      },
    );
  }

  Widget _buildActionButtons() {
    return Obx(() {
      final isLoading = _controller?.isLoading ?? false;
      
      return Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: isLoading ? null : _saveDriver,
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorConstants.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: SizeConstants.spaceBase),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
                ),
              ),
              child: isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'حفظ التغييرات',
                      style: TextStyle(
                        fontSize: SizeConstants.fontBase,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
          SizedBox(width: SizeConstants.spaceBase),
          Expanded(
            child: OutlinedButton(
              onPressed: isLoading ? null : () => Get.back(),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: SizeConstants.spaceBase),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
                ),
              ),
              child: const Text(
                'إلغاء',
                style: TextStyle(
                  fontSize: SizeConstants.fontBase,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      );
    });
  }

  Future<void> _selectBirthDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedBirthDate ?? DateTime.now().subtract(const Duration(days: 365 * 25)),
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() {
        _selectedBirthDate = date;
      });
    }
  }

  Future<void> _selectJoiningDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedJoiningDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (date != null) {
      setState(() {
        _selectedJoiningDate = date;
      });
    }
  }

  Future<void> _saveDriver() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_driver == null || _controller == null) {
      return;
    }

    // Create updated driver model
    final updatedDriver = DriverModel(
      id: _driver!.id,
      name: _nameController.text.trim(),
      username: _usernameController.text.trim(),
      email: _emailController.text.trim().isNotEmpty ? _emailController.text.trim() : null,
      phone: _phoneController.text.trim().isNotEmpty ? _phoneController.text.trim() : null,
      address: _addressController.text.trim().isNotEmpty ? _addressController.text.trim() : null,
      cityName: _cityController.text.trim().isNotEmpty ? _cityController.text.trim() : null,
      genderId: _selectedGenderId,
      religionId: _selectedReligionId,
      typeBloodId: null, // فصيلة الدم غير مستخدمة
      busId: _selectedBusId,
      birthDate: _selectedBirthDate?.toIso8601String().split('T')[0],
      joiningDate: _selectedJoiningDate?.toIso8601String().split('T')[0],
      // Keep existing values for fields not being edited
      schoolId: _driver!.schoolId,
      status: _selectedStatus ?? _driver!.status,
      logo: _driver!.logo,
      type: _driver!.type,
      emailVerifiedAt: _driver!.emailVerifiedAt,
      deletedAt: _driver!.deletedAt,
      createdAt: _driver!.createdAt,
      updatedAt: _driver!.updatedAt,
      typeAuth: _driver!.typeAuth,
      logoPath: _driver!.logoPath,
      schoolName: _driver!.schoolName,
      genderName: _driver!.genderName,
      religionName: _driver!.religionName,
      typeBloodName: _driver!.typeBloodName,
      busName: _driver!.busName,
      busCarNumber: _driver!.busCarNumber,
    );

    final success = await _controller!.updateDriver(
      updatedDriver,
      password: _passwordController.text.trim().isNotEmpty ? _passwordController.text.trim() : null,
    );

    if (success) {
      Get.back(); // Go back to previous page
    }
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline_rounded,
            size: SizeConstants.icon2XL,
            color: Colors.red.shade300,
          ),
          SizedBox(height: SizeConstants.spaceBase),
          Text(
            'لم يتم العثور على بيانات السائق',
            style: TextStyle(
              fontSize: SizeConstants.fontSM,
              fontWeight: FontWeight.w600,
              color: ColorConstants.textPrimary,
            ),
          ),
          SizedBox(height: SizeConstants.spaceLG),
          ElevatedButton.icon(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.arrow_back_rounded),
            label: const Text('العودة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorConstants.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}

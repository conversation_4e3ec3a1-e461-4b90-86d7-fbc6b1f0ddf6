import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/constants/size_constants.dart';
import '../../../core/localization/language_controller.dart';
import '../../../core/utils/asset_helper.dart';
import '../../../core/utils/controller_utils.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../core/performance/performance_monitor.dart';
import '../../routes/app_routes.dart';

/// Enhanced LanguageSelectionPage for web with beautiful design and performance optimization
class LanguageSelectionPage extends StatefulWidget {
  const LanguageSelectionPage({super.key});

  @override
  State<LanguageSelectionPage> createState() => _LanguageSelectionPageState();
}

class _LanguageSelectionPageState extends State<LanguageSelectionPage>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    // Start animations
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PerformanceMonitor.monitorWidgetBuild('LanguageSelectionPage', () {
      final languageController = ControllerUtils.getLanguageController();
      final isDesktop = ResponsiveUtils.isDesktop(context);
      final isTablet = ResponsiveUtils.isTablet(context);

      return Scaffold(
        body: Container(
          decoration: _buildBackgroundDecoration(),
          child: SafeArea(
            child: _buildResponsiveLayout(
              context,
              languageController,
              isDesktop,
              isTablet,
            ),
          ),
        ),
      );
    });
  }

  /// Build responsive layout based on screen size
  Widget _buildResponsiveLayout(
    BuildContext context,
    LanguageController languageController,
    bool isDesktop,
    bool isTablet,
  ) {
    if (isDesktop) {
      return _buildDesktopLayout(context, languageController);
    } else if (isTablet) {
      return _buildTabletLayout(context, languageController);
    } else {
      return _buildMobileLayout(context, languageController);
    }
  }

  /// Build desktop layout with side-by-side design
  Widget _buildDesktopLayout(
    BuildContext context,
    LanguageController languageController,
  ) {
    return Row(
      children: [
        // Left side - Welcome content
        Expanded(
          flex: 5,
          child: _buildWelcomeSection(context, isDesktop: true),
        ),
        // Right side - Language selection
        Expanded(
          flex: 4,
          child: _buildLanguageSection(
            context,
            languageController,
            isDesktop: true,
          ),
        ),
      ],
    );
  }

  /// Build tablet layout
  Widget _buildTabletLayout(
    BuildContext context,
    LanguageController languageController,
  ) {
    return Center(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 600),
        padding: EdgeInsets.all(SizeConstants.spaceXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildWelcomeSection(context, isDesktop: false),
            SizedBox(height: SizeConstants.space3XL),
            _buildLanguageSection(
              context,
              languageController,
              isDesktop: false,
            ),
          ],
        ),
      ),
    );
  }

  /// Build mobile layout
  Widget _buildMobileLayout(
    BuildContext context,
    LanguageController languageController,
  ) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(SizeConstants.spaceLG),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildWelcomeSection(context, isDesktop: false),
            SizedBox(height: SizeConstants.spaceXL),
            _buildLanguageSection(
              context,
              languageController,
              isDesktop: false,
            ),
          ],
        ),
      ),
    );
  }

  /// Build background decoration with gradient
  BoxDecoration _buildBackgroundDecoration() {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          ColorConstants.primary.withAlpha(25),
          Colors.white,
          ColorConstants.secondary.withAlpha(15),
        ],
        stops: const [0.0, 0.5, 1.0],
      ),
    );
  }

  /// Build welcome section with logo and text
  Widget _buildWelcomeSection(BuildContext context, {required bool isDesktop}) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo with enhanced styling
            Container(
              padding: EdgeInsets.all(
                isDesktop ? SizeConstants.spaceMD : SizeConstants.spaceSM,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
                boxShadow: [
                  BoxShadow(
                    color: ColorConstants.primary.withAlpha(50),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: SizedBox(
                width: isDesktop ? 48 : 40,
                height: isDesktop ? 48 : 40,
                child: AssetHelper.logoImage,
              ),
            ),
            SizedBox(
              height: isDesktop ? SizeConstants.spaceLG : SizeConstants.spaceMD,
            ),

            // Welcome text with enhanced typography
            Text(
              'مرحباً بك في باصاتي',
              style: TextStyle(
                fontSize:
                    isDesktop ? SizeConstants.font2XL : SizeConstants.fontXL,
                fontWeight: FontWeight.bold,
                color: ColorConstants.textPrimary,
                letterSpacing: 0.3,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: SizeConstants.spaceSM),

            Text(
              'Welcome to Busaty',
              style: TextStyle(
                fontSize:
                    isDesktop ? SizeConstants.fontLG : SizeConstants.fontMD,
                fontWeight: FontWeight.w600,
                color: ColorConstants.primary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: SizeConstants.spaceMD),

            Container(
              padding: EdgeInsets.symmetric(
                horizontal: SizeConstants.spaceMD,
                vertical: SizeConstants.spaceSM,
              ),
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(200),
                borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
                border: Border.all(color: ColorConstants.primary.withAlpha(50)),
              ),
              child: Text(
                'يرجى اختيار لغتك المفضلة\nPlease select your preferred language',
                style: TextStyle(
                  fontSize:
                      isDesktop ? SizeConstants.fontBase : SizeConstants.fontSM,
                  color: ColorConstants.textSecondary,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build language selection section
  Widget _buildLanguageSection(
    BuildContext context,
    LanguageController languageController, {
    required bool isDesktop,
  }) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: EdgeInsets.all(
          isDesktop ? SizeConstants.spaceLG : SizeConstants.spaceMD,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(SizeConstants.radiusXL),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(25),
              blurRadius: 12,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Section title
            Text(
              'اختر اللغة | Choose Language',
              style: TextStyle(
                fontSize:
                    isDesktop ? SizeConstants.fontLG : SizeConstants.fontMD,
                fontWeight: FontWeight.bold,
                color: ColorConstants.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(
              height: isDesktop ? SizeConstants.spaceLG : SizeConstants.spaceMD,
            ),

            // Language options
            isDesktop
                ? Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Expanded(
                      child: _buildEnhancedLanguageOption(
                        languageController,
                        'العربية',
                        AssetHelper.arabicFlag,
                        'ar',
                        isDesktop: true,
                      ),
                    ),
                    SizedBox(width: SizeConstants.spaceMD),
                    Expanded(
                      child: _buildEnhancedLanguageOption(
                        languageController,
                        'English',
                        AssetHelper.englishFlag,
                        'en',
                        isDesktop: true,
                      ),
                    ),
                  ],
                )
                : Column(
                  children: [
                    _buildEnhancedLanguageOption(
                      languageController,
                      'العربية',
                      AssetHelper.arabicFlag,
                      'ar',
                      isDesktop: false,
                    ),
                    SizedBox(height: SizeConstants.spaceSM),
                    _buildEnhancedLanguageOption(
                      languageController,
                      'English',
                      AssetHelper.englishFlag,
                      'en',
                      isDesktop: false,
                    ),
                  ],
                ),

            SizedBox(height: isDesktop ? 40 : 32),

            // Continue button with enhanced styling
            _buildContinueButton(isDesktop),
          ],
        ),
      ),
    );
  }

  /// Build enhanced language option with animations and hover effects
  Widget _buildEnhancedLanguageOption(
    LanguageController controller,
    String language,
    String flagPath,
    String languageCode, {
    required bool isDesktop,
  }) {
    return Obx(() {
      final isSelected = controller.currentLanguage == languageCode;

      return AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => controller.changeLanguage(languageCode),
            borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
            hoverColor: ColorConstants.primary.withAlpha(25),
            splashColor: ColorConstants.primary.withAlpha(50),
            child: Container(
              width: isDesktop ? double.infinity : 200,
              padding: EdgeInsets.all(
                isDesktop ? SizeConstants.spaceMD : SizeConstants.spaceSM,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
                border: Border.all(
                  color:
                      isSelected
                          ? ColorConstants.primary
                          : Colors.grey.shade300,
                  width: isSelected ? 2 : 1,
                ),
                color:
                    isSelected
                        ? ColorConstants.primary.withAlpha(25)
                        : Colors.grey.shade50,
                boxShadow:
                    isSelected
                        ? [
                          BoxShadow(
                            color: ColorConstants.primary.withAlpha(75),
                            blurRadius: 8,
                            offset: const Offset(0, 3),
                          ),
                        ]
                        : [
                          BoxShadow(
                            color: Colors.grey.withAlpha(50),
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                          ),
                        ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Flag with enhanced styling
                  Container(
                    padding: EdgeInsets.all(SizeConstants.spaceSM),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(
                        SizeConstants.radiusMD,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(25),
                          blurRadius: 4,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Image.asset(
                      flagPath,
                      width:
                          isDesktop
                              ? SizeConstants.icon2XL
                              : SizeConstants.iconXL,
                      height:
                          isDesktop
                              ? SizeConstants.icon2XL
                              : SizeConstants.iconXL,
                      fit: BoxFit.cover,
                    ),
                  ),
                  SizedBox(
                    height:
                        isDesktop
                            ? SizeConstants.spaceSM
                            : SizeConstants.spaceXS,
                  ),

                  // Language name
                  Text(
                    language,
                    style: TextStyle(
                      fontSize:
                          isDesktop
                              ? SizeConstants.fontMD
                              : SizeConstants.fontBase,
                      fontWeight: FontWeight.bold,
                      color:
                          isSelected
                              ? ColorConstants.primary
                              : ColorConstants.textPrimary,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  // Selection indicator
                  if (isSelected) ...[
                    SizedBox(height: SizeConstants.spaceXS),
                    Container(
                      width: 24,
                      height: 2,
                      decoration: BoxDecoration(
                        color: ColorConstants.primary,
                        borderRadius: BorderRadius.circular(1),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  /// Build continue button with enhanced styling
  Widget _buildContinueButton(bool isDesktop) {
    return Container(
      width: isDesktop ? 200 : double.infinity,
      height: SizeConstants.buttonHeightLG,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
        gradient: LinearGradient(
          colors: [
            ColorConstants.primary,
            ColorConstants.primary.withBlue(200),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: ColorConstants.primary.withAlpha(100),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => Get.offAllNamed(AppRoutes.login),
          borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
          child: Container(
            alignment: Alignment.center,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.arrow_forward_rounded,
                  color: Colors.white,
                  size: SizeConstants.iconMD,
                ),
                SizedBox(width: SizeConstants.spaceSM),
                Text(
                  'متابعة | Continue',
                  style: TextStyle(
                    fontSize:
                        isDesktop
                            ? SizeConstants.fontMD
                            : SizeConstants.fontBase,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    letterSpacing: 0.3,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

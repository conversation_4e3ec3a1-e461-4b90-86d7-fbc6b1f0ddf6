import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/constants/size_constants.dart';

class SelectLocationPage extends StatefulWidget {
  const SelectLocationPage({super.key});

  @override
  State<SelectLocationPage> createState() => _SelectLocationPageState();
}

class _SelectLocationPageState extends State<SelectLocationPage> {
  final Completer<GoogleMapController> _controller =
      Completer<GoogleMapController>();

  // Default location (Riyadh, Saudi Arabia)
  LatLng _selectedLocation = const LatLng(24.7136, 46.6753);
  final Set<Marker> _markers = {};

  @override
  void initState() {
    super.initState();
    _addMarker(_selectedLocation);
  }

  void _addMarker(LatLng position) {
    setState(() {
      _markers.clear();
      _markers.add(
        Marker(
          markerId: const MarkerId('selected-location'),
          position: position,
          infoWindow: const InfoWindow(
            title: 'الموقع المحدد',
            snippet: 'اضغط على تأكيد لحفظ هذا الموقع',
          ),
        ),
      );
      _selectedLocation = position;
    });
  }

  void _onMapTap(LatLng position) {
    _addMarker(position);
  }

  void _confirmLocation() {
    Get.back(
      result: {
        'latitude': _selectedLocation.latitude,
        'longitude': _selectedLocation.longitude,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'تحديد الموقع',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: ColorConstants.primary,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Get.back(),
        ),
      ),
      body: Stack(
        children: [
          // Google Map
          GoogleMap(
            onMapCreated: (GoogleMapController controller) {
              _controller.complete(controller);
            },
            initialCameraPosition: CameraPosition(
              target: _selectedLocation,
              zoom: 15.0,
            ),
            onTap: _onMapTap,
            markers: _markers,
            myLocationEnabled: true,
            myLocationButtonEnabled: true,
            mapType: MapType.normal,
            zoomControlsEnabled: true,
            compassEnabled: true,
            trafficEnabled: false,
            buildingsEnabled: true,
          ),

          // Instructions overlay
          Positioned(
            top: 16,
            left: 16,
            right: 16,
            child: Container(
              padding: EdgeInsets.all(SizeConstants.spaceBase),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Text(
                'اضغط على الخريطة لتحديد موقعك',
                style: TextStyle(
                  fontSize: SizeConstants.fontSM,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),

          // Confirm button
          Positioned(
            bottom: 32,
            left: 16,
            right: 16,
            child: ElevatedButton(
              onPressed: _confirmLocation,
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorConstants.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: SizeConstants.spaceBase),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                ),
                elevation: 4,
              ),
              child: const Text(
                'تأكيد الموقع',
                style: TextStyle(fontSize: SizeConstants.fontBase, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

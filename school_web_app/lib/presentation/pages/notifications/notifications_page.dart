import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../controllers/notification_controller.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/empty_state_widget.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../../core/constants/size_constants.dart';

/// Simple notifications page matching SchoolX functionality
class NotificationsPage extends GetView<NotificationController> {
  const NotificationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.dark
              ? ColorConstants.backgroundDark
              : ColorConstants.background,
      body: ResponsiveSidebar(
        child: Column(
          children: [
            // Simple header
            _buildSimpleHeader(context),
            // Simple content
            Expanded(child: _buildSimpleContent(context)),
          ],
        ),
      ),
    );
  }

  /// Build simple header matching SchoolX
  Widget _buildSimpleHeader(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 120,
      decoration: BoxDecoration(
        color: ColorConstants.primary,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          'الإشعارات',
          style: TextStyle(
            fontSize: SizeConstants.fontLG,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// Build simple content matching SchoolX
  Widget _buildSimpleContent(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.value) {
        return const LoadingWidget();
      }

      if (controller.notifications.isEmpty) {
        return const EmptyStateWidget(
          icon: Icons.notifications_none,
          title: 'لا توجد إشعارات',
          description: 'لم يتم العثور على أي إشعارات',
        );
      }

      return ListView.separated(
        padding: EdgeInsets.all(SizeConstants.spaceBase),
        itemCount: controller.notifications.length,
        separatorBuilder:
            (context, index) => Divider(
              color: ColorConstants.primary.withValues(alpha: 0.3),
              thickness: 0.5,
            ),
        itemBuilder: (context, index) {
          final notification = controller.notifications[index];
          return _buildSimpleNotificationItem(context, notification);
        },
      );
    });
  }

  /// Build simple notification item matching SchoolX
  Widget _buildSimpleNotificationItem(
    BuildContext context,
    dynamic notification,
  ) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: ColorConstants.primary,
        radius: 25,
        child: Icon(Icons.notifications, color: Colors.white, size: 20),
      ),
      title: Text(
        notification.title ?? 'إشعار',
        style: TextStyle(
          fontSize: SizeConstants.fontSM,
          fontWeight: FontWeight.w600,
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? ColorConstants.textPrimaryDark
                  : ColorConstants.textPrimary,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (notification.body != null) ...[
            const SizedBox(height: 4),
            Text(
              notification.body!,
              style: TextStyle(
                fontSize: SizeConstants.fontXS,
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? ColorConstants.textSecondaryDark
                        : ColorConstants.textSecondary,
              ),
            ),
          ],
          if (notification.createdAt != null) ...[
            const SizedBox(height: 4),
            Text(
              _formatDate(notification.createdAt!),
              style: TextStyle(
                fontSize: SizeConstants.fontXS,
                color: Colors.grey,
              ),
            ),
          ],
        ],
      ),
      onTap: () {
        // Simple notification tap - just show a snackbar
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(notification.title ?? 'إشعار'),
            backgroundColor: ColorConstants.primary,
          ),
        );
      },
    );
  }

  /// Format date to simple string
  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inDays > 0) {
        return 'منذ ${difference.inDays} يوم';
      } else if (difference.inHours > 0) {
        return 'منذ ${difference.inHours} ساعة';
      } else if (difference.inMinutes > 0) {
        return 'منذ ${difference.inMinutes} دقيقة';
      } else {
        return 'الآن';
      }
    } catch (e) {
      return dateString; // Return original string if parsing fails
    }
  }
}

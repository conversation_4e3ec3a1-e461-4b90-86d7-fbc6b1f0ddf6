import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../controllers/notification_controller.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';
import '../../widgets/common/empty_state_widget.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/notifications/notification_card.dart';
import '../../widgets/notifications/notification_filters.dart';
import '../../../core/constants/size_constants.dart';

/// Page for displaying notifications
class NotificationsPage extends GetView<NotificationController> {
  const NotificationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.dark
              ? ColorConstants.backgroundDark
              : ColorConstants.background,
      body: ResponsiveSidebar(
        child: Column(
          children: [
            // Header with title and actions
            _buildHeader(context),
            // Filters
            _buildFilters(context),
            // Content
            Expanded(child: _buildContent(context)),
          ],
        ),
      ),
    );
  }

  /// Build header with title and actions
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceBase),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.cardDark
                : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Title with unread count
          Expanded(
            child: Row(
              children: [
                Text(
                  'الإشعارات',
                  style: TextStyle(
                    fontSize: SizeConstants.fontMD,
                    fontWeight: FontWeight.bold,
                    color:
                        Theme.of(context).brightness == Brightness.dark
                            ? ColorConstants.textPrimaryDark
                            : ColorConstants.textPrimary,
                  ),
                ),
                SizedBox(width: SizeConstants.spaceSM),
                Obx(
                  () =>
                      controller.unreadCount > 0
                          ? Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: SizeConstants.spaceXS,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(
                                SizeConstants.radiusMD,
                              ),
                            ),
                            child: Text(
                              '${controller.unreadCount}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: SizeConstants.fontXS,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          )
                          : const SizedBox.shrink(),
                ),
              ],
            ),
          ),
          // Action buttons
          Row(
            children: [
              // Mark all as read button
              Obx(
                () =>
                    controller.unreadCount > 0
                        ? OutlinedButton.icon(
                          onPressed: controller.markAllAsRead,
                          icon: const Icon(Icons.done_all),
                          label: const Text('تحديد الكل كمقروء'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: ColorConstants.primary,
                          ),
                        )
                        : const SizedBox.shrink(),
              ),
              SizedBox(width: SizeConstants.spaceSM),
              // Clear all button
              OutlinedButton.icon(
                onPressed: () => _showClearAllDialog(context),
                icon: const Icon(Icons.clear_all),
                label: const Text('حذف الكل'),
                style: OutlinedButton.styleFrom(foregroundColor: Colors.red),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build filters
  Widget _buildFilters(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: SizeConstants.spaceBase,
        vertical: 12,
      ),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.cardDark
                : Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
        ),
      ),
      child: NotificationFilters(
        selectedFilter: controller.selectedFilter,
        onFilterChanged: controller.filterNotifications,
      ),
    );
  }

  /// Build content
  Widget _buildContent(BuildContext context) {
    return Obx(() {
      if (controller.isLoading && controller.notifications.isEmpty) {
        return const LoadingWidget();
      }

      if (controller.errorMessage.isNotEmpty) {
        return CustomErrorWidget(
          message: controller.errorMessage,
          onRetry: () => controller.loadNotifications(refresh: true),
        );
      }

      if (controller.notifications.isEmpty) {
        return const EmptyStateWidget(
          icon: Icons.notifications_off,
          title: 'لا توجد إشعارات',
          description: 'لم يتم العثور على أي إشعارات',
        );
      }

      return RefreshIndicator(
        onRefresh: () => controller.loadNotifications(refresh: true),
        child: ListView.builder(
          padding: EdgeInsets.all(SizeConstants.spaceBase),
          itemCount:
              controller.notifications.length +
              (controller.hasMoreData ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == controller.notifications.length) {
              // Loading indicator for pagination
              return const Padding(
                padding: EdgeInsets.all(SizeConstants.spaceBase),
                child: Center(child: CircularProgressIndicator()),
              );
            }

            final notification = controller.notifications[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: NotificationCard(
                notification: notification,
                onTap: () => _handleNotificationTap(notification),
                onMarkAsRead: () => controller.markAsRead(notification.id!),
                onDelete: () => _showDeleteDialog(context, notification),
              ),
            );
          },
        ),
      );
    });
  }

  /// Handle notification tap
  void _handleNotificationTap(notification) {
    // Mark as read if not already read
    if (notification.isRead != true) {
      controller.markAsRead(notification.id!);
    }

    // Navigate based on notification type and data
    if (notification.notificationData != null) {
      final data = notification.notificationData!;

      if (data.tripId != null) {
        // Navigate to trip details
        Get.toNamed('/trips/details', arguments: {'tripId': data.tripId});
      } else if (data.studentId != null) {
        // Navigate to student details
        Get.toNamed(
          '/students/details',
          arguments: {'studentId': data.studentId},
        );
      } else if (data.busId != null) {
        // Navigate to bus details
        Get.toNamed('/buses/details', arguments: {'busId': data.busId});
      }
    }
  }

  /// Show delete confirmation dialog
  void _showDeleteDialog(BuildContext context, notification) {
    Get.dialog(
      AlertDialog(
        title: const Text('حذف الإشعار'),
        content: const Text('هل أنت متأكد من حذف هذا الإشعار؟'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.deleteNotification(notification.id!);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// Show clear all confirmation dialog
  void _showClearAllDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        title: const Text('حذف جميع الإشعارات'),
        content: const Text(
          'هل أنت متأكد من حذف جميع الإشعارات؟ لا يمكن التراجع عن هذا الإجراء.',
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.clearAllNotifications();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف الكل'),
          ),
        ],
      ),
    );
  }
}

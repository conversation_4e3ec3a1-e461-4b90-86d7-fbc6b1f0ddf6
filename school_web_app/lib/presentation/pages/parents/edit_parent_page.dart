import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../domain/entities/parent.dart';
import '../../controllers/parents_controller.dart';
import '../../widgets/parents/parent_form.dart';
import '../../../core/constants/size_constants.dart';

/// Edit parent page
/// Following Clean Architecture and GetX patterns
class EditParentPage extends GetView<ParentsController> {
  const EditParentPage({super.key});

  @override
  Widget build(BuildContext context) {
    final Parent parent = Get.arguments as Parent;

    return Scaffold(
      appBar: AppBar(
        title: const Text('تعديل ولي أمر'),
        backgroundColor: ColorConstants.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(SizeConstants.spaceBase),
        child: ParentForm(
          parent: parent,
          onSubmit: (formData) => _handleSubmit(formData, parent),
          isLoading: controller.isLoading,
        ),
      ),
    );
  }

  Future<void> _handleSubmit(
    Map<String, dynamic> formData,
    Parent originalParent,
  ) async {
    final updatedParent = Parent(
      id: originalParent.id,
      name: formData['name'],
      email: formData['email'],
      phone: formData['phone'],
      address: formData['address'],
      logoPath: originalParent.logoPath,
      studentIds: originalParent.studentIds,
      createdAt: originalParent.createdAt,
      updatedAt: DateTime.now().toIso8601String(),
    );

    final success = await controller.updateParent(updatedParent);

    if (success) {
      Get.back();
      Get.snackbar(
        'نجح',
        'تم تحديث ولي الأمر بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } else {
      Get.snackbar(
        'خطأ',
        controller.error.isNotEmpty
            ? controller.error
            : 'فشل في تحديث ولي الأمر',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}

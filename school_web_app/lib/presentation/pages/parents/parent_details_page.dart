import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../domain/entities/parent.dart';
import '../../controllers/parents_controller.dart';
import '../../routes/app_routes.dart';
import '../../../core/constants/size_constants.dart';

/// Parent details page
/// Following Clean Architecture and GetX patterns
class ParentDetailsPage extends GetView<ParentsController> {
  const ParentDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final Parent parent = Get.arguments as Parent;

    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل ولي الأمر'),
        backgroundColor: ColorConstants.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed:
                () => Get.toNamed(AppRoutes.editParent, arguments: parent),
            icon: const Icon(Icons.edit),
            tooltip: 'تعديل',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(SizeConstants.spaceBase),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProfileSection(parent),
            SizedBox(height: SizeConstants.spaceLG),
            _buildInfoSection(parent),
            SizedBox(height: SizeConstants.spaceLG),
            _buildStudentsSection(parent),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileSection(Parent parent) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(SizeConstants.spaceBase),
        child: Row(
          children: [
            CircleAvatar(
              radius: 40,
              backgroundColor: ColorConstants.primary.withValues(alpha: 0.1),
              backgroundImage:
                  parent.logoPath != null
                      ? NetworkImage(parent.logoPath!)
                      : null,
              child:
                  parent.logoPath == null
                      ? Icon(
                        Icons.person,
                        size: 40,
                        color: ColorConstants.primary,
                      )
                      : null,
            ),
            SizedBox(width: SizeConstants.spaceBase),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    parent.name ?? 'غير محدد',
                    style: const TextStyle(
                      fontSize: SizeConstants.fontBase,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (parent.email != null) ...[
                    SizedBox(height: SizeConstants.spaceXS),
                    Text(
                      parent.email!,
                      style: TextStyle(color: Colors.grey[600], fontSize: SizeConstants.fontSM),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(Parent parent) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(SizeConstants.spaceBase),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الشخصية',
              style: TextStyle(fontSize: SizeConstants.fontSM, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: SizeConstants.spaceBase),
            _buildInfoRow('الاسم', parent.name ?? 'غير محدد'),
            if (parent.email != null)
              _buildInfoRow('البريد الإلكتروني', parent.email!),
            if (parent.phone != null)
              _buildInfoRow('رقم الهاتف', parent.phone!),
            if (parent.address != null)
              _buildInfoRow('العنوان', parent.address!),
            if (parent.createdAt != null)
              _buildInfoRow('تاريخ التسجيل', parent.createdAt!),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(child: Text(value, style: const TextStyle(fontSize: SizeConstants.fontBase))),
        ],
      ),
    );
  }

  Widget _buildStudentsSection(Parent parent) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(SizeConstants.spaceBase),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'الطلاب',
                  style: TextStyle(fontSize: SizeConstants.fontSM, fontWeight: FontWeight.bold),
                ),
                TextButton.icon(
                  onPressed:
                      parent.id != null
                          ? () => _loadStudents(parent.id!)
                          : null,
                  icon: const Icon(Icons.refresh),
                  label: const Text('تحديث'),
                ),
              ],
            ),
            SizedBox(height: SizeConstants.spaceBase),
            FutureBuilder<List<int>>(
              future:
                  parent.id != null
                      ? controller.getStudentsByParentId(parent.id!)
                      : Future.value(<int>[]),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (snapshot.hasError) {
                  return Text(
                    'خطأ في تحميل الطلاب: ${snapshot.error}',
                    style: const TextStyle(color: Colors.red),
                  );
                }

                final studentIds = snapshot.data ?? [];

                if (studentIds.isEmpty) {
                  return const Text(
                    'لا يوجد طلاب مرتبطين بهذا الولي',
                    style: TextStyle(
                      fontStyle: FontStyle.italic,
                      color: Colors.grey,
                    ),
                  );
                }

                return Column(
                  children:
                      studentIds.map((studentId) {
                        return ListTile(
                          leading: const Icon(Icons.person),
                          title: Text('طالب رقم $studentId'),
                          trailing: const Icon(Icons.arrow_forward_ios),
                          onTap: () {
                            // Navigate to student details
                            // Get.toNamed(AppRoutes.studentDetails, arguments: studentId);
                          },
                        );
                      }).toList(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _loadStudents(int parentId) {
    controller.getStudentsByParentId(parentId);
  }
}

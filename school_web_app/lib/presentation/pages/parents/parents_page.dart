import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../domain/entities/parent.dart';
import '../../controllers/parents_controller.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';
import '../../widgets/common/empty_state_widget.dart';
import '../../widgets/common/pagination_indicator.dart';
import '../../widgets/parents/parent_card.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../routes/app_routes.dart';
import '../../../core/constants/size_constants.dart';

/// Parents page for displaying list of parents
/// Following Clean Architecture and GetX patterns
class ParentsPage extends GetView<ParentsController> {
  const ParentsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.dark
              ? ColorConstants.backgroundDark
              : ColorConstants.background,
      body: ResponsiveSidebar(
        child: Column(
          children: [
            // Header with title and add button
            Container(
              padding: EdgeInsets.all(SizeConstants.spaceBase),
              decoration: BoxDecoration(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? ColorConstants.cardDark
                        : Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Text(
                    'أولياء الأمور',
                    style: TextStyle(
                      fontSize: SizeConstants.fontMD,
                      fontWeight: FontWeight.bold,
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? ColorConstants.textPrimaryDark
                              : ColorConstants.textPrimary,
                    ),
                  ),
                  const Spacer(),
                  ElevatedButton.icon(
                    onPressed: () => Get.toNamed(AppRoutes.addParent),
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة ولي أمر'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorConstants.primary,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            // Search bar
            _buildSearchBar(),
            // Content
            Expanded(child: Obx(() => _buildContent())),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceBase),
      child: TextField(
        onChanged: controller.searchParents,
        decoration: InputDecoration(
          hintText: 'البحث عن ولي أمر...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(SizeConstants.radiusMD)),
          filled: true,
          fillColor: Colors.grey[100],
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (controller.isLoading && controller.parents.isEmpty) {
      return const LoadingWidget();
    }

    if (controller.error.isNotEmpty && controller.parents.isEmpty) {
      return CustomErrorWidget(
        message: controller.error,
        onRetry: () => controller.loadParents(refresh: true),
      );
    }

    if (controller.parents.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.family_restroom_rounded,
        title: 'لا يوجد أولياء أمور',
        description: 'لم يتم العثور على أي أولياء أمور',
        actionText: 'إضافة ولي أمر',
        onAction: () => Get.toNamed(AppRoutes.addParent),
      );
    }

    return PaginationIndicatorExtension.buildPaginatedList<Parent>(
      items: controller.parents,
      hasMoreData: controller.hasMoreData,
      isLoading: controller.isLoadingMore,
      onLoadMore: controller.loadMoreParents,
      onRefresh: () => controller.loadParents(refresh: true),
      padding: EdgeInsets.all(SizeConstants.spaceBase),
      itemBuilder: (context, index, parent) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: ParentCard(
            parent: parent,
            onEdit: () => Get.toNamed(AppRoutes.editParent, arguments: parent),
            onDelete: () => _showDeleteDialog(parent),
            onViewDetails:
                () => Get.toNamed(AppRoutes.parentDetails, arguments: parent),
          ),
        );
      },
    );
  }

  void _showDeleteDialog(parent) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف ولي الأمر "${parent.name}"؟'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          TextButton(
            onPressed: () async {
              Get.back();
              final success = await controller.deleteParent(parent.id);
              if (success) {
                Get.snackbar(
                  'نجح',
                  'تم حذف ولي الأمر بنجاح',
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  'خطأ',
                  controller.error.isNotEmpty
                      ? controller.error
                      : 'فشل في حذف ولي الأمر',
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}

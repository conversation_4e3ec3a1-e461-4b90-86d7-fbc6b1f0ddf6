import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/constants/color_constants.dart';
import '../widgets/layout/responsive_sidebar.dart';
import '../../../core/constants/size_constants.dart';

/// PlaceholderPage for routes that don't have a dedicated page yet
/// This is a temporary solution until the actual pages are implemented
class PlaceholderPage extends StatelessWidget {
  final String title;
  final IconData icon;
  final String description;

  const PlaceholderPage({
    super.key,
    required this.title,
    required this.icon,
    this.description = 'This page is under construction',
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveSidebar(
      child: Scaffold(
        appBar: AppBar(
          title: Text(title),
          backgroundColor: ColorConstants.primary,
          elevation: 0,
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                Get.snackbar(
                  'تحديث',
                  'تم تحديث الصفحة',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: ColorConstants.success.withAlpha(150),
                  colorText: ColorConstants.white,
                  margin: EdgeInsets.all(SizeConstants.spaceBase),
                );
              },
            ),
          ],
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 100,
                color: ColorConstants.primary.withAlpha(150),
              ),
              SizedBox(height: SizeConstants.spaceLG),
              Text(
                title,
                style: const TextStyle(
                  fontSize: SizeConstants.fontMD,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: SizeConstants.spaceBase),
              Text(
                description,
                style: TextStyle(
                  fontSize: SizeConstants.fontBase,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: SizeConstants.spaceXL),
              ElevatedButton(
                onPressed: () {
                  Get.back();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorConstants.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                ),
                child: const Text('العودة'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

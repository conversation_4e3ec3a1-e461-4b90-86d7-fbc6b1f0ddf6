import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/controller_utils.dart';
import '../../controllers/auth_controller.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../../core/constants/size_constants.dart';

/// ChangePasswordPage for changing user password
class ChangePasswordPage extends StatefulWidget {
  const ChangePasswordPage({super.key});

  @override
  State<ChangePasswordPage> createState() => _ChangePasswordPageState();
}

class _ChangePasswordPageState extends State<ChangePasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  bool _obscureCurrentPassword = true;
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;
  
  late final AuthController _authController;
  
  @override
  void initState() {
    super.initState();
    _authController = ControllerUtils.getAuthController();
  }
  
  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveSidebar(
      child: Scaffold(
        appBar: AppBar(
          title: Text('changePassword'.tr),
          backgroundColor: ColorConstants.primary,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Get.back(),
          ),
        ),
        body: Obx(() {
          if (_authController.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          
          return SingleChildScrollView(
            padding: EdgeInsets.all(SizeConstants.spaceBase),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Lock icon
                  Center(
                    child: Container(
                      padding: EdgeInsets.all(SizeConstants.spaceBase),
                      decoration: BoxDecoration(
                        color: ColorConstants.primary.withAlpha(50),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.lock,
                        size: 60,
                        color: ColorConstants.primary,
                      ),
                    ),
                  ),
                  SizedBox(height: SizeConstants.spaceXL),
                  
                  // Form title
                  Center(
                    child: Text(
                      'changePassword'.tr,
                      style: const TextStyle(
                        fontSize: SizeConstants.fontMD,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  SizedBox(height: SizeConstants.spaceXL),
                  
                  // Current password
                  Text(
                    'oldPassword'.tr,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: SizeConstants.spaceXS),
                  CustomTextField(
                    controller: _currentPasswordController,
                    hintText: 'oldPassword'.tr,
                    prefixIcon: Icons.lock,
                    obscureText: _obscureCurrentPassword,
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscureCurrentPassword ? Icons.visibility : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscureCurrentPassword = !_obscureCurrentPassword;
                        });
                      },
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your current password'.tr;
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: SizeConstants.spaceBase),
                  
                  // New password
                  Text(
                    'newPassword'.tr,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: SizeConstants.spaceXS),
                  CustomTextField(
                    controller: _newPasswordController,
                    hintText: 'newPassword'.tr,
                    prefixIcon: Icons.lock_outline,
                    obscureText: _obscureNewPassword,
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscureNewPassword ? Icons.visibility : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscureNewPassword = !_obscureNewPassword;
                        });
                      },
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a new password'.tr;
                      }
                      if (value.length < 6) {
                        return 'Password must be at least 6 characters'.tr;
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: SizeConstants.spaceBase),
                  
                  // Confirm password
                  Text(
                    'confirmPassword'.tr,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: SizeConstants.spaceXS),
                  CustomTextField(
                    controller: _confirmPasswordController,
                    hintText: 'confirmPassword'.tr,
                    prefixIcon: Icons.lock_outline,
                    obscureText: _obscureConfirmPassword,
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscureConfirmPassword = !_obscureConfirmPassword;
                        });
                      },
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please confirm your new password'.tr;
                      }
                      if (value != _newPasswordController.text) {
                        return 'Passwords do not match'.tr;
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: SizeConstants.spaceXL),
                  
                  // Change password button
                  Center(
                    child: CustomButton(
                      text: 'changePassword'.tr,
                      onPressed: _changePassword,
                      isLoading: _authController.isLoading,
                      width: 200,
                    ),
                  ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }
  
  /// Change password
  Future<void> _changePassword() async {
    if (_formKey.currentState!.validate()) {
      final success = await _authController.changePassword(
        _currentPasswordController.text,
        _newPasswordController.text,
      );
      
      if (success) {
        Get.back();
        Get.snackbar(
          'changePassword'.tr,
          'passwordChangeSuccess'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: ColorConstants.success.withAlpha(150),
          colorText: ColorConstants.white,
          margin: EdgeInsets.all(SizeConstants.spaceBase),
        );
      } else {
        Get.snackbar(
          'Error'.tr,
          _authController.errorMessage,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: ColorConstants.error.withAlpha(150),
          colorText: ColorConstants.white,
          margin: EdgeInsets.all(SizeConstants.spaceBase),
        );
      }
    }
  }
}

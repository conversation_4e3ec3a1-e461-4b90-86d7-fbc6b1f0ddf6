import 'package:flutter/material.dart';
import '../../../core/constants/color_constants.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../../core/constants/size_constants.dart';

/// Simple search page matching SchoolX functionality
class SearchPage extends StatelessWidget {
  const SearchPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.dark
              ? ColorConstants.backgroundDark
              : ColorConstants.background,
      body: ResponsiveSidebar(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Search header
              _buildSearchHeader(context),
              const SizedBox(height: 20),
              // Simple search field
              _buildSearchField(context),
            ],
          ),
        ),
      ),
    );
  }

  /// Build search header
  Widget _buildSearchHeader(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 120,
      decoration: BoxDecoration(
        color: ColorConstants.primary,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          'البحث',
          style: TextStyle(
            fontSize: SizeConstants.fontLG,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// Build simple search field with instructions
  Widget _buildSearchField(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 30),
      child: Column(
        children: [
          // Search field with enhanced design
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'ابحث هنا... (للتوضيح فقط)',
                hintStyle: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: SizeConstants.fontSM,
                ),
                prefixIcon: Icon(Icons.search, color: Colors.grey.shade400),
                suffixIcon: InkWell(
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                          'هذا مجرد مثال - استخدم البحث في الصفحات المخصصة',
                        ),
                        backgroundColor: Colors.orange,
                      ),
                    );
                  },
                  child: Container(
                    margin: const EdgeInsets.all(8),
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: ColorConstants.primary,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.search,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
                fillColor: Colors.white,
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
                  borderSide: const BorderSide(
                    color: ColorConstants.primary,
                    width: 2,
                  ),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
              ),
            ),
          ),

          const SizedBox(height: 30),

          // Quick navigation cards
          _buildQuickNavigationCards(context),

          const SizedBox(height: 20),

          // Instructions card
          _buildInstructionsCard(context),
        ],
      ),
    );
  }

  /// Build quick navigation cards
  Widget _buildQuickNavigationCards(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التنقل السريع للبحث',
          style: TextStyle(
            fontSize: SizeConstants.fontMD,
            fontWeight: FontWeight.bold,
            color:
                Theme.of(context).brightness == Brightness.dark
                    ? ColorConstants.textPrimaryDark
                    : ColorConstants.textPrimary,
          ),
        ),

        const SizedBox(height: 15),

        // Grid of navigation cards
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 15,
          mainAxisSpacing: 15,
          childAspectRatio: 1.2,
          children: [
            _buildNavigationCard(
              context,
              Icons.people_alt_rounded,
              'الطلاب',
              ColorConstants.primary,
              () {
                // Navigate to students page
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('انتقل إلى صفحة الطلاب للبحث')),
                );
              },
            ),
            _buildNavigationCard(
              context,
              Icons.supervisor_account_rounded,
              'المشرفين',
              ColorConstants.secondary,
              () {
                // Navigate to supervisors page
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('انتقل إلى صفحة المشرفين للبحث'),
                  ),
                );
              },
            ),
            _buildNavigationCard(
              context,
              Icons.drive_eta_rounded,
              'السائقين',
              ColorConstants.accent2,
              () {
                // Navigate to drivers page
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('انتقل إلى صفحة السائقين للبحث'),
                  ),
                );
              },
            ),
            _buildNavigationCard(
              context,
              Icons.family_restroom_rounded,
              'أولياء الأمور',
              ColorConstants.accent1,
              () {
                // Navigate to parents page
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('انتقل إلى صفحة أولياء الأمور للبحث'),
                  ),
                );
              },
            ),
            _buildNavigationCard(
              context,
              Icons.directions_bus_rounded,
              'الحافلات',
              Colors.orange,
              () {
                // Navigate to buses page
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('انتقل إلى صفحة الحافلات للبحث'),
                  ),
                );
              },
            ),
            _buildNavigationCard(
              context,
              Icons.route_rounded,
              'الرحلات',
              Colors.green,
              () {
                // Navigate to trips page
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('انتقل إلى صفحة الرحلات للبحث')),
                );
              },
            ),
          ],
        ),
      ],
    );
  }

  /// Build navigation card
  Widget _buildNavigationCard(
    BuildContext context,
    IconData icon,
    String title,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
      child: Container(
        decoration: BoxDecoration(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? ColorConstants.cardDark
                  : Colors.white,
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
          border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 28),
            ),

            const SizedBox(height: 12),

            Text(
              title,
              style: TextStyle(
                fontSize: SizeConstants.fontSM,
                fontWeight: FontWeight.w600,
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? ColorConstants.textPrimaryDark
                        : ColorConstants.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build instructions card
  Widget _buildInstructionsCard(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        border: Border.all(color: Colors.blue.shade200, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue.shade600, size: 24),
              const SizedBox(width: 8),
              Text(
                'كيفية استخدام البحث',
                style: TextStyle(
                  fontSize: SizeConstants.fontMD,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade800,
                ),
              ),
            ],
          ),

          const SizedBox(height: 15),

          // Instructions list
          _buildInstructionItem(
            context,
            Icons.people_alt_rounded,
            'البحث في الطلاب',
            'انتقل إلى صفحة الطلاب واستخدم حقل البحث للبحث بالاسم',
            ColorConstants.primary,
          ),

          const SizedBox(height: 12),

          _buildInstructionItem(
            context,
            Icons.supervisor_account_rounded,
            'البحث في المشرفين',
            'انتقل إلى صفحة المشرفين واستخدم حقل البحث للبحث بالاسم',
            ColorConstants.secondary,
          ),

          const SizedBox(height: 12),

          _buildInstructionItem(
            context,
            Icons.drive_eta_rounded,
            'البحث في السائقين',
            'انتقل إلى صفحة السائقين واستخدم حقل البحث للبحث بالاسم',
            ColorConstants.accent2,
          ),

          const SizedBox(height: 12),

          _buildInstructionItem(
            context,
            Icons.family_restroom_rounded,
            'البحث في أولياء الأمور',
            'انتقل إلى صفحة أولياء الأمور واستخدم حقل البحث للبحث بالاسم',
            ColorConstants.accent1,
          ),

          const SizedBox(height: 12),

          _buildInstructionItem(
            context,
            Icons.directions_bus_rounded,
            'البحث في الحافلات',
            'انتقل إلى صفحة الحافلات واستخدم حقل البحث للبحث بالاسم أو الرقم',
            Colors.orange,
          ),
        ],
      ),
    );
  }

  /// Build instruction item
  Widget _buildInstructionItem(
    BuildContext context,
    IconData icon,
    String title,
    String description,
    Color color,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),

        const SizedBox(width: 12),

        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: SizeConstants.fontSM,
                  fontWeight: FontWeight.w600,
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? ColorConstants.textPrimaryDark
                          : ColorConstants.textPrimary,
                ),
              ),

              const SizedBox(height: 4),

              Text(
                description,
                style: TextStyle(
                  fontSize: SizeConstants.fontXS,
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? ColorConstants.textSecondaryDark
                          : ColorConstants.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

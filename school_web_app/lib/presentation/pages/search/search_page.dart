import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/services/search_service.dart';
import '../../controllers/search_controller.dart' as app_search;
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/empty_state_widget.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/search/search_result_card.dart';
import '../../widgets/search/search_suggestions.dart';
import '../../widgets/search/search_filters.dart';
import '../../../core/constants/size_constants.dart';

/// Page for advanced search functionality
class SearchPage extends GetView<app_search.SearchController> {
  const SearchPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.dark
              ? ColorConstants.backgroundDark
              : ColorConstants.background,
      body: ResponsiveSidebar(
        child: Column(
          children: [
            // Search header
            _buildSearchHeader(context),
            // Search content
            Expanded(child: _buildSearchContent(context)),
          ],
        ),
      ),
    );
  }

  /// Build search header
  Widget _buildSearchHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceBase),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.cardDark
                : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'البحث المتقدم',
            style: TextStyle(
              fontSize: SizeConstants.fontMD,
              fontWeight: FontWeight.bold,
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? ColorConstants.textPrimaryDark
                      : ColorConstants.textPrimary,
            ),
          ),
          SizedBox(height: SizeConstants.spaceBase),
          // Search bar
          Row(
            children: [
              // Search field
              Expanded(
                child: Stack(
                  children: [
                    TextField(
                      controller: controller.searchTextController,
                      onChanged: controller.onSearchChanged,
                      onSubmitted: controller.onSearchSubmitted,
                      decoration: InputDecoration(
                        hintText: 'ابحث في الطلاب، أولياء الأمور، السائقين...',
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: Obx(
                          () =>
                              controller.searchQuery.isNotEmpty
                                  ? IconButton(
                                    icon: const Icon(Icons.clear),
                                    onPressed: controller.clearSearch,
                                  )
                                  : const SizedBox.shrink(),
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
                        ),
                        filled: true,
                        fillColor:
                            Theme.of(context).brightness == Brightness.dark
                                ? ColorConstants.backgroundDark
                                : Colors.grey[100],
                      ),
                    ),
                    // Search suggestions overlay
                    Obx(
                      () =>
                          controller.showSuggestions
                              ? Positioned(
                                top: 60,
                                left: 0,
                                right: 0,
                                child: SearchSuggestions(
                                  suggestions: controller.getSearchSuggestions(
                                    controller.searchTextController.text,
                                  ),
                                  onSuggestionSelected:
                                      controller.selectSuggestion,
                                ),
                              )
                              : const SizedBox.shrink(),
                    ),
                  ],
                ),
              ),
              SizedBox(width: SizeConstants.spaceBase),
              // Filters button
              OutlinedButton.icon(
                onPressed: controller.showFiltersDialog,
                icon: const Icon(Icons.filter_list),
                label: const Text('الفلاتر'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: ColorConstants.primary,
                ),
              ),
            ],
          ),
          // Active filters
          Obx(
            () =>
                controller.activeFilters.isNotEmpty
                    ? Padding(
                      padding: const EdgeInsets.only(top: 12),
                      child: SearchFilters(
                        filters: controller.activeFilters,
                        onFilterRemoved: controller.removeFilter,
                        onClearAll: controller.clearFilters,
                      ),
                    )
                    : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  /// Build search content
  Widget _buildSearchContent(BuildContext context) {
    return Obx(() {
      // Show loading
      if (controller.isSearching) {
        return const LoadingWidget();
      }

      // Show search results
      if (controller.searchResults.isNotEmpty) {
        return _buildSearchResults(context);
      }

      // Show empty state or search history
      if (controller.searchQuery.isEmpty) {
        return _buildSearchHistory(context);
      } else {
        return const EmptyStateWidget(
          icon: Icons.search_off,
          title: 'لا توجد نتائج',
          description: 'لم يتم العثور على نتائج مطابقة لبحثك',
        );
      }
    });
  }

  /// Build search results
  Widget _buildSearchResults(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.all(SizeConstants.spaceBase),
      itemCount: controller.searchResults.length,
      itemBuilder: (context, index) {
        final result = controller.searchResults[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: SearchResultCard(
            result: result,
            onTap: () => controller.navigateToResult(result),
          ),
        );
      },
    );
  }

  /// Build search history
  Widget _buildSearchHistory(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(SizeConstants.spaceBase),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search history header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'البحث الأخير',
                style: TextStyle(
                  fontSize: SizeConstants.fontSM,
                  fontWeight: FontWeight.bold,
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? ColorConstants.textPrimaryDark
                          : ColorConstants.textPrimary,
                ),
              ),
              if (controller.searchHistory.isNotEmpty)
                TextButton(
                  onPressed: controller.clearSearchHistory,
                  child: const Text('مسح الكل'),
                ),
            ],
          ),
          SizedBox(height: SizeConstants.spaceBase),
          // Search history list
          if (controller.searchHistory.isEmpty)
            const EmptyStateWidget(
              icon: Icons.history,
              title: 'لا يوجد تاريخ بحث',
              description: 'ابدأ البحث لرؤية تاريخ البحث هنا',
            )
          else
            Expanded(
              child: ListView.builder(
                itemCount: controller.searchHistory.length,
                itemBuilder: (context, index) {
                  final historyItem = controller.searchHistory[index];
                  return ListTile(
                    leading: const Icon(Icons.history),
                    title: Text(historyItem),
                    onTap: () => controller.selectSuggestion(historyItem),
                    trailing: IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () {
                        // Remove from history
                        controller.searchHistory.removeAt(index);
                      },
                    ),
                  );
                },
              ),
            ),
          SizedBox(height: SizeConstants.spaceLG),
          // Quick search categories
          Text(
            'البحث السريع',
            style: TextStyle(
              fontSize: SizeConstants.fontSM,
              fontWeight: FontWeight.bold,
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? ColorConstants.textPrimaryDark
                      : ColorConstants.textPrimary,
            ),
          ),
          SizedBox(height: SizeConstants.spaceBase),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children:
                SearchCategory.values.map((category) {
                  return _buildQuickSearchCard(context, category);
                }).toList(),
          ),
        ],
      ),
    );
  }

  /// Build quick search card
  Widget _buildQuickSearchCard(BuildContext context, SearchCategory category) {
    return InkWell(
      onTap: () {
        controller.searchTextController.text = category.displayName;
        controller.performSearch(category.displayName);
      },
      borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
      child: Container(
        width: 120,
        height: 100,
        decoration: BoxDecoration(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? ColorConstants.cardDark
                  : Colors.white,
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
          border: Border.all(
            color: controller.getCategoryColor(category).withValues(alpha: 0.3),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              controller.getCategoryIcon(category),
              size: SizeConstants.iconLG,
              color: controller.getCategoryColor(category),
            ),
            SizedBox(height: SizeConstants.spaceXS),
            Text(
              category.displayName,
              style: TextStyle(
                fontSize: SizeConstants.fontSM,
                fontWeight: FontWeight.bold,
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? ColorConstants.textPrimaryDark
                        : ColorConstants.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

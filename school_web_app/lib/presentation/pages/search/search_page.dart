import 'package:flutter/material.dart';
import '../../../core/constants/color_constants.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../../core/constants/size_constants.dart';

/// Simple search page matching SchoolX functionality
class SearchPage extends StatelessWidget {
  const SearchPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.dark
              ? ColorConstants.backgroundDark
              : ColorConstants.background,
      body: ResponsiveSidebar(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Search header
              _buildSearchHeader(context),
              const SizedBox(height: 20),
              // Simple search field
              _buildSearchField(context),
            ],
          ),
        ),
      ),
    );
  }

  /// Build search header
  Widget _buildSearchHeader(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 120,
      decoration: BoxDecoration(
        color: ColorConstants.primary,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          'البحث',
          style: TextStyle(
            fontSize: SizeConstants.fontLG,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// Build simple search field
  Widget _buildSearchField(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 30),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'البحث',
          suffixIcon: InkWell(
            onTap: () {
              // Simple search functionality - can be implemented later
            },
            child: const Icon(Icons.search, color: ColorConstants.primary),
          ),
          fillColor: Colors.white,
          filled: true,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            borderSide: const BorderSide(color: ColorConstants.primary),
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/controller_utils.dart';
import '../../widgets/common/language_selector.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../../core/constants/size_constants.dart';

/// SettingsPage for app settings
class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final languageController = ControllerUtils.getLanguageController();
    final themeController = ControllerUtils.getThemeController();

    return ResponsiveSidebar(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('الإعدادات'),
          backgroundColor: ColorConstants.primary,
          elevation: 0,
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.all(SizeConstants.spaceBase),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Settings header
              const Text(
                'إعدادات التطبيق',
                style: TextStyle(
                  fontSize: SizeConstants.fontMD,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: SizeConstants.spaceLG),

              // Language settings
              _buildSettingsSection(
                title: 'اللغة',
                icon: Icons.language,
                child: Column(
                  children: [
                    // Language selector
                    Row(
                      children: [
                        const Text('اختر اللغة:'),
                        SizedBox(width: SizeConstants.spaceBase),
                        const LanguageSelector(),
                        const Spacer(),
                        Obx(
                          () => Text(
                            languageController.isArabic ? 'العربية' : 'English',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: SizeConstants.spaceBase),

                    // Language toggle button
                    ElevatedButton.icon(
                      onPressed: () {
                        languageController.toggleLanguage();
                      },
                      icon: const Icon(Icons.swap_horiz),
                      label: const Text('تبديل اللغة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorConstants.primary,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: SizeConstants.spaceBase),

              // Theme settings
              _buildSettingsSection(
                title: 'المظهر',
                icon: Icons.palette,
                child: Column(
                  children: [
                    // Theme mode selector
                    Row(
                      children: [
                        const Text('وضع المظهر:'),
                        SizedBox(width: SizeConstants.spaceBase),
                        Obx(
                          () => Switch(
                            value: themeController.isDarkMode,
                            onChanged: (value) {
                              themeController.toggleTheme();
                            },
                            activeColor: ColorConstants.primary,
                          ),
                        ),
                        SizedBox(width: SizeConstants.spaceXS),
                        Obx(
                          () => Text(
                            themeController.isDarkMode
                                ? 'الوضع الداكن'
                                : 'الوضع الفاتح',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              SizedBox(height: SizeConstants.spaceBase),

              // Notification settings
              _buildSettingsSection(
                title: 'الإشعارات',
                icon: Icons.notifications,
                child: Column(
                  children: [
                    // Push notifications
                    Row(
                      children: [
                        const Text('إشعارات الدفع:'),
                        SizedBox(width: SizeConstants.spaceBase),
                        Switch(
                          value: true,
                          onChanged: (value) {
                            // Toggle push notifications
                          },
                          activeColor: ColorConstants.primary,
                        ),
                      ],
                    ),
                    SizedBox(height: SizeConstants.spaceXS),

                    // Email notifications
                    Row(
                      children: [
                        const Text('إشعارات البريد الإلكتروني:'),
                        SizedBox(width: SizeConstants.spaceBase),
                        Switch(
                          value: false,
                          onChanged: (value) {
                            // Toggle email notifications
                          },
                          activeColor: ColorConstants.primary,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              SizedBox(height: SizeConstants.spaceBase),

              // About section
              _buildSettingsSection(
                title: 'حول التطبيق',
                icon: Icons.info,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('إصدار التطبيق: 1.0.0'),
                    SizedBox(height: SizeConstants.spaceXS),
                    const Text('تم التطوير بواسطة: فريق بساطي'),
                    SizedBox(height: SizeConstants.spaceBase),
                    TextButton.icon(
                      onPressed: () {
                        // Show about dialog
                        showAboutDialog(
                          context: context,
                          applicationName: 'بساطي',
                          applicationVersion: '1.0.0',
                          applicationIcon: Icon(
                            Icons.directions_bus_rounded,
                            color: ColorConstants.primary,
                            size: SizeConstants.iconLG,
                          ),
                          applicationLegalese:
                              'جميع الحقوق محفوظة © 2023 بساطي',
                        );
                      },
                      icon: const Icon(Icons.more_horiz),
                      label: const Text('عرض المزيد'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build a settings section with a title, icon, and child widget
  Widget _buildSettingsSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceBase),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(50),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            children: [
              Icon(
                icon,
                color: ColorConstants.primary,
              ),
              SizedBox(width: SizeConstants.spaceXS),
              Text(
                title,
                style: const TextStyle(
                  fontSize: SizeConstants.fontSM,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const Divider(),
          SizedBox(height: SizeConstants.spaceXS),

          // Section content
          child,
        ],
      ),
    );
  }
}

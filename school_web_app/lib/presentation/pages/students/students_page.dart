import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/constants/size_constants.dart';
import '../../../core/performance/performance_monitor.dart';
import '../../../core/utils/logger.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/student.dart';
import '../../controllers/students_controller.dart';
import '../../widgets/students/student_details_dialog.dart';
import '../../widgets/students/students_table.dart';
import '../../widgets/students/students_header.dart';
import '../../widgets/layout/responsive_sidebar.dart';

/// Students page
class StudentsPage extends StatelessWidget {
  const StudentsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PerformanceMonitor.monitorWidgetBuild('StudentsPage', () {
      final controller = Get.find<StudentsController>();

      return Scaffold(
        backgroundColor: ColorConstants.background,
        body: ResponsiveSidebar(child: _buildContent(context, controller)),
      );
    });
  }

  Widget _buildContent(BuildContext context, StudentsController controller) {
    // حساب ارتفاع الشاشة المتاح
    final screenHeight = MediaQuery.of(context).size.height;
    final isDesktop = ResponsiveUtils.isDesktop(context);

    // تحديد ارتفاع الجدول بناءً على حجم الشاشة
    // في وضع سطح المكتب، نجعل الجدول يأخذ مساحة أكبر
    final tableHeight =
        isDesktop
            ? screenHeight -
                180 // ارتفاع أكبر لسطح المكتب
            : screenHeight - 220; // ارتفاع أصغر للجوال

    return SingleChildScrollView(
      padding: EdgeInsets.all(
        isDesktop ? SizeConstants.spaceLG : SizeConstants.spaceMD,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Students header
          const StudentsHeader(),
          SizedBox(
            height: isDesktop ? SizeConstants.spaceLG : SizeConstants.spaceMD,
          ),

          // Search bar
          _buildSearchBar(context, controller),
          SizedBox(
            height: isDesktop ? SizeConstants.spaceMD : SizeConstants.spaceSM,
          ),

          // Students table - تكبير حجم الجدول
          SizedBox(
            height: tableHeight,
            width: double.infinity,
            child: _buildStudentsTable(context, controller),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context, StudentsController controller) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(SizeConstants.spaceMD),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(Icons.search, color: ColorConstants.primary),
          SizedBox(width: SizeConstants.spaceMD),
          Expanded(
            child: TextField(
              decoration: InputDecoration(
                hintText: 'search_students'.tr,
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
              onChanged: (value) {
                controller.searchStudents(value);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStudentsTable(
    BuildContext context,
    StudentsController controller,
  ) {
    return Obx(() {
      // تسجيل حالة وحدة التحكم للتشخيص
      LoggerService.debug(
        'Students controller state',
        data: {
          'isLoading': controller.isLoading,
          'students.length': controller.students.length,
          'errorMessage': controller.errorMessage,
        },
      );

      // حالة التحميل الأولي
      if (controller.isLoading && controller.students.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: SizeConstants.spaceBase),
              Text('جاري تحميل بيانات الطلاب...'),
            ],
          ),
        );
      }

      // حالة الخطأ
      if (controller.errorMessage.isNotEmpty && controller.students.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, color: Colors.red, size: SizeConstants.iconXL),
              SizedBox(height: SizeConstants.spaceBase),
              Text(
                controller.errorMessage,
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: SizeConstants.spaceBase),
              ElevatedButton.icon(
                onPressed: controller.refreshStudents,
                icon: const Icon(Icons.refresh),
                label: Text('إعادة المحاولة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorConstants.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceLG,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ),
        );
      }

      // حالة عدم وجود طلاب
      if (controller.students.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.person_off, color: Colors.grey, size: SizeConstants.iconXL),
              SizedBox(height: SizeConstants.spaceBase),
              Text(
                'لا يوجد طلاب',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              SizedBox(height: SizeConstants.spaceBase),
              ElevatedButton.icon(
                onPressed: controller.refreshStudents,
                icon: const Icon(Icons.refresh),
                label: Text('تحديث'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorConstants.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceLG,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ),
        );
      }

      // حالة وجود طلاب
      return RefreshIndicator(
        onRefresh: controller.refreshStudents,
        child: StudentsTable(
          students: controller.students,
          isLoading: controller.isLoading,
          onRowTap: (student) => _showStudentDetails(context, student),
          onLoadMore: controller.loadNextPage,
        ),
      );
    });
  }

  void _showStudentDetails(BuildContext context, Student student) {
    showDialog(
      context: context,
      builder: (context) => StudentDetailsDialog(student: student),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/supervisors_controller.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/supervisors/supervisors_table.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';
import '../../routes/app_routes.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../core/constants/size_constants.dart';

/// Supervisors page with responsive design
/// Following Single Responsibility Principle by focusing only on supervisors list display
class SupervisorsPage extends StatelessWidget {
  const SupervisorsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.dark
              ? ColorConstants.backgroundDark
              : ColorConstants.background,
      body: ResponsiveSidebar(child: _buildContent(context)),
    );
  }

  Widget _buildContent(BuildContext context) {
    return GetBuilder<SupervisorsController>(
      builder: (controller) {
        return Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              SizedBox(height: SizeConstants.spaceLG),
              _buildSearchAndActions(context, controller),
              SizedBox(height: SizeConstants.spaceLG),
              Expanded(child: _buildSupervisorsContent(context, controller)),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.supervisor_account_rounded,
          size: SizeConstants.iconLG,
          color: ColorConstants.primary,
        ),
        SizedBox(width: SizeConstants.spaceBase),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إدارة المشرفين',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? ColorConstants.textPrimaryDark
                        : ColorConstants.textPrimary,
              ),
            ),
            Text(
              'إدارة وتنظيم المشرفين في المدرسة',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? ColorConstants.textSecondaryDark
                        : ColorConstants.textSecondary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSearchAndActions(
    BuildContext context,
    SupervisorsController controller,
  ) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final isMobile = deviceType == DeviceScreenType.mobile;

    return Column(
      children: [
        if (isMobile) ...[
          // Mobile layout - stacked
          _buildSearchField(context, controller),
          SizedBox(height: SizeConstants.spaceBase),
          _buildActionButtons(context, controller),
        ] else ...[
          // Desktop/Tablet layout - row
          Row(
            children: [
              Expanded(flex: 2, child: _buildSearchField(context, controller)),
              SizedBox(width: SizeConstants.spaceBase),
              _buildActionButtons(context, controller),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildSearchField(
    BuildContext context,
    SupervisorsController controller,
  ) {
    return Container(
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.cardDark
                : ColorConstants.white,
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        onChanged: controller.searchSupervisors,
        decoration: InputDecoration(
          hintText: 'البحث عن المشرفين...',
          hintStyle: TextStyle(
            color:
                Theme.of(context).brightness == Brightness.dark
                    ? ColorConstants.textSecondaryDark
                    : ColorConstants.textSecondary,
          ),
          prefixIcon: Icon(
            Icons.search,
            color:
                Theme.of(context).brightness == Brightness.dark
                    ? ColorConstants.textSecondaryDark
                    : ColorConstants.textSecondary,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.transparent,
          contentPadding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceBase,
            vertical: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    SupervisorsController controller,
  ) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final isMobile = deviceType == DeviceScreenType.mobile;

    return Row(
      mainAxisSize: isMobile ? MainAxisSize.max : MainAxisSize.min,
      children: [
        if (isMobile) const Spacer(),
        // Refresh button
        Container(
          decoration: BoxDecoration(
            color:
                Theme.of(context).brightness == Brightness.dark
                    ? ColorConstants.cardDark
                    : ColorConstants.white,
            borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            onPressed: () => controller.refreshData(),
            icon: Icon(
              Icons.refresh,
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? ColorConstants.textPrimaryDark
                      : ColorConstants.textPrimary,
            ),
            tooltip: 'تحديث',
          ),
        ),
        SizedBox(width: SizeConstants.spaceSM),
        // Add supervisor button
        ElevatedButton.icon(
          onPressed: () => Get.toNamed(AppRoutes.addSupervisor),
          icon: const Icon(Icons.add, color: ColorConstants.white),
          label: Text(
            isMobile ? 'إضافة' : 'إضافة مشرف',
            style: const TextStyle(
              color: ColorConstants.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: ColorConstants.primary,
            padding: EdgeInsets.symmetric(
              horizontal: isMobile ? 16 : 24,
              vertical: 16,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            ),
            elevation: 2,
          ),
        ),
        if (isMobile) const Spacer(),
      ],
    );
  }

  Widget _buildSupervisorsContent(
    BuildContext context,
    SupervisorsController controller,
  ) {
    return Obx(() {
      if (controller.isLoading && controller.supervisors.isEmpty) {
        return const LoadingWidget();
      }

      if (controller.errorMessage.isNotEmpty &&
          controller.supervisors.isEmpty) {
        return CustomErrorWidget(
          message: controller.errorMessage,
          onRetry: () => controller.loadSupervisors(refresh: true),
        );
      }

      if (controller.supervisors.isEmpty) {
        return _buildEmptyState(context);
      }

      return SupervisorsTable(
        supervisors: controller.supervisors,
        isLoading: controller.isLoading,
        isLoadingMore: controller.isLoadingMore,
        hasMoreData: controller.hasMoreData,
        onLoadMore: controller.loadMoreSupervisors,
        onEdit:
            (supervisor) =>
                Get.toNamed(AppRoutes.editSupervisor, arguments: supervisor),
        onDelete:
            (supervisor) => _showDeleteDialog(context, controller, supervisor),
        onView:
            (supervisor) =>
                Get.toNamed(AppRoutes.supervisorDetails, arguments: supervisor),
      );
    });
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.supervisor_account_outlined,
            size: SizeConstants.icon2XL,
            color:
                Theme.of(context).brightness == Brightness.dark
                    ? ColorConstants.textSecondaryDark
                    : ColorConstants.textSecondary,
          ),
          SizedBox(height: SizeConstants.spaceBase),
          Text(
            'لا توجد مشرفين',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? ColorConstants.textSecondaryDark
                      : ColorConstants.textSecondary,
            ),
          ),
          SizedBox(height: SizeConstants.spaceXS),
          Text(
            'ابدأ بإضافة مشرف جديد',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? ColorConstants.textSecondaryDark
                      : ColorConstants.textSecondary,
            ),
          ),
          SizedBox(height: SizeConstants.spaceLG),
          ElevatedButton.icon(
            onPressed: () => Get.toNamed(AppRoutes.addSupervisor),
            icon: const Icon(Icons.add, color: ColorConstants.white),
            label: const Text(
              'إضافة مشرف',
              style: TextStyle(
                color: ColorConstants.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorConstants.primary,
              padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceLG, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(
    BuildContext context,
    SupervisorsController controller,
    supervisor,
  ) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المشرف "${supervisor.name}"؟'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              final success = await controller.deleteSupervisor(supervisor.id!);
              if (success) {
                Get.snackbar(
                  'نجح',
                  'تم حذف المشرف بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorConstants.error,
            ),
            child: const Text(
              'حذف',
              style: TextStyle(color: ColorConstants.white),
            ),
          ),
        ],
      ),
    );
  }
}

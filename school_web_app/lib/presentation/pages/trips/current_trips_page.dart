import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../controllers/trip_controller.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';
import '../../widgets/trips/current_trip_card.dart';
import '../../widgets/trips/trip_stats_widget.dart';
import '../../../core/constants/size_constants.dart';

/// CurrentTripsPage for displaying and managing current trips
/// Following Single Responsibility Principle by focusing only on current trips display
class CurrentTripsPage extends StatelessWidget {
  const CurrentTripsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.background,
      body: ResponsiveSidebar(
        child: GetBuilder<TripController>(
          builder: (controller) {
            return RefreshIndicator(
              onRefresh: controller.refreshCurrentTrip,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: EdgeInsets.all(SizeConstants.spaceBase),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Page header
                    _buildPageHeader(context),

                    SizedBox(height: SizeConstants.spaceLG),

                    // Trip stats
                    const TripStatsWidget(),

                    SizedBox(height: SizeConstants.spaceLG),

                    // Current trip section
                    _buildCurrentTripSection(controller),

                    SizedBox(height: SizeConstants.spaceLG),

                    // Quick actions
                    _buildQuickActions(context),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPageHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceMD),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withAlpha(204), // 0.8 * 255 = 204
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        boxShadow: [
          BoxShadow(
            color: Theme.of(
              context,
            ).primaryColor.withAlpha(77), // 0.3 * 255 = 77
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(SizeConstants.spaceSM),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
              borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
            ),
            child: const Icon(
              Icons.directions_bus_rounded,
              color: Colors.white,
              size: SizeConstants.iconLG,
            ),
          ),
          SizedBox(width: SizeConstants.spaceBase),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الرحلات الحالية',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: SizeConstants.spaceXS),
                Text(
                  'تتبع ومراقبة الرحلات الجارية في الوقت الفعلي',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withAlpha(230), // 0.9 * 255 = 230
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Get.find<TripController>().refreshCurrentTrip(),
            icon: const Icon(Icons.refresh_rounded, color: Colors.white),
            tooltip: 'تحديث',
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentTripSection(TripController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.location_on_rounded,
              color: Get.theme.primaryColor,
              size: SizeConstants.iconMD,
            ),
            SizedBox(width: SizeConstants.spaceXS),
            Text(
              'الرحلة الحالية',
              style: Get.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),

        SizedBox(height: SizeConstants.spaceBase),

        Obx(() {
          if (controller.isLoading) {
            return const LoadingWidget(message: 'جاري تحميل الرحلة الحالية...');
          }

          if (controller.errorMessage.isNotEmpty) {
            return CustomErrorWidget(
              message: controller.errorMessage,
              onRetry: controller.refreshCurrentTrip,
            );
          }

          if (controller.currentTrip == null) {
            return _buildNoCurrentTripWidget();
          }

          return CurrentTripCard(
            trip: controller.currentTrip!,
            onViewDetails: () => _viewTripDetails(controller.currentTrip!.id),
            onTrackLocation:
                () => _trackTripLocation(controller.currentTrip!.id),
          );
        }),
      ],
    );
  }

  Widget _buildNoCurrentTripWidget() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(SizeConstants.spaceXL),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          Icon(
            Icons.directions_bus_outlined,
            size: SizeConstants.icon2XL,
            color: Colors.grey[400],
          ),
          SizedBox(height: SizeConstants.spaceBase),
          Text(
            'لا توجد رحلات حالية',
            style: Get.textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: SizeConstants.spaceXS),
          Text(
            'لا توجد رحلات نشطة في الوقت الحالي',
            style: Get.textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.flash_on_rounded,
              color: Get.theme.primaryColor,
              size: SizeConstants.iconMD,
            ),
            SizedBox(width: SizeConstants.spaceXS),
            Text(
              'إجراءات سريعة',
              style: Get.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),

        SizedBox(height: SizeConstants.spaceBase),

        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildQuickActionCard(
              icon: Icons.history_rounded,
              title: 'الرحلات السابقة',
              subtitle: 'عرض سجل الرحلات',
              onTap: () => Get.toNamed('/previous-trips'),
            ),
            _buildQuickActionCard(
              icon: Icons.map_rounded,
              title: 'خريطة الباصات',
              subtitle: 'تتبع جميع الباصات',
              onTap: () => Get.toNamed('/buses-map'),
            ),
            _buildQuickActionCard(
              icon: Icons.analytics_rounded,
              title: 'التقارير',
              subtitle: 'إحصائيات الرحلات',
              onTap: () => Get.toNamed('/trip-reports'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
      child: Container(
        width: 200,
        padding: EdgeInsets.all(SizeConstants.spaceBase),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
          border: Border.all(color: Colors.grey[300]!),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13), // 0.05 * 255 = 13
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, color: Get.theme.primaryColor, size: SizeConstants.iconLG),
            SizedBox(height: SizeConstants.spaceSM),
            Text(
              title,
              style: Get.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: SizeConstants.spaceXS),
            Text(
              subtitle,
              style: Get.textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  void _viewTripDetails(String tripId) {
    Get.toNamed('/current-trips/details', arguments: {'tripId': tripId});
  }

  void _trackTripLocation(String tripId) {
    Get.toNamed('/trip-tracking', arguments: {'tripId': tripId});
  }
}

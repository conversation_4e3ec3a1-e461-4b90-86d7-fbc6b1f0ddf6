import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/previous_trip_details_controller.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/custom_error_widget.dart';
import '../../widgets/trips/trip_details_header.dart';
import '../../widgets/trips/attendance_tab.dart';
import '../../widgets/trips/route_map_tab.dart';

/// Previous Trip Details Page
/// Shows detailed information about a previous trip including attendance and route
/// Based on the original SchoolX project structure
class PreviousTripDetailsPage extends StatefulWidget {
  const PreviousTripDetailsPage({super.key});

  @override
  State<PreviousTripDetailsPage> createState() =>
      _PreviousTripDetailsPageState();
}

class _PreviousTripDetailsPageState extends State<PreviousTripDetailsPage> {
  late PreviousTripDetailsController controller;

  @override
  void initState() {
    super.initState();
    // Initialize controller with dependency injection
    controller = Get.put(
      PreviousTripDetailsController(tripRemoteDataSource: Get.find()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: Obx(() {
        if (controller.isLoading) {
          return const LoadingWidget(message: 'جاري تحميل تفاصيل الرحلة...');
        }

        if (controller.errorMessage.isNotEmpty) {
          return CustomErrorWidget(
            message: controller.errorMessage,
            onRetry: controller.refreshData,
          );
        }

        return _buildContent();
      }),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'تفاصيل الرحلة',
        style: TextStyle(
          fontSize: ResponsiveUtils.getFontSize(18),
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: ColorConstants.primary,
      elevation: 0,
      centerTitle: true,
      actions: [
        IconButton(
          onPressed: controller.refreshData,
          icon: const Icon(Icons.refresh_rounded, color: Colors.white),
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  Widget _buildContent() {
    return Column(
      children: [
        // Trip details header
        TripDetailsHeader(
          tripId: controller.tripId,
          tripType: controller.tripTypeDisplayText,
          busName: controller.busName,
          supervisorName: controller.supervisorName,
          driverName: controller.driverName,
          date: controller.tripDate,
          totalStudents: controller.totalStudents,
          presentCount: controller.presentCount,
          absentCount: controller.absentCount,
          statusColor: controller.statusColor,
          statusText: controller.statusText,
        ),

        // Tab bar
        Container(
          color: Colors.white,
          child: TabBar(
            controller: controller.tabController,
            labelColor: ColorConstants.primary,
            unselectedLabelColor: Colors.grey[600],
            indicatorColor: ColorConstants.primary,
            indicatorWeight: 3,
            tabs: const [
              Tab(icon: Icon(Icons.map_rounded), text: 'المسار'),
              Tab(icon: Icon(Icons.check_circle_rounded), text: 'الحاضرين'),
              Tab(icon: Icon(Icons.cancel_rounded), text: 'الغائبين'),
            ],
          ),
        ),

        // Tab content
        Expanded(
          child: TabBarView(
            controller: controller.tabController,
            children: [
              // Route Map Tab
              RouteMapTab(
                routeData: controller.routeData,
                presentStudents: controller.presentStudents,
                absentStudents: controller.absentStudents,
                isLoading: controller.isRouteLoading,
                onRefresh: controller.loadRouteData,
              ),

              // Present Students Tab
              AttendanceTab(
                students: controller.presentStudents,
                isLoading: controller.isAttendanceLoading,
                isPresent: true,
                onRefresh: controller.loadAttendanceData,
              ),

              // Absent Students Tab
              AttendanceTab(
                students: controller.absentStudents,
                isLoading: controller.isAttendanceLoading,
                isPresent: false,
                onRefresh: controller.loadAttendanceData,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

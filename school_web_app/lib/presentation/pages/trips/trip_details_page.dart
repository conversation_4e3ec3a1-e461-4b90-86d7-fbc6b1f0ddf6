import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/trip_controller.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';
import '../../widgets/trips/trip_info_card.dart';
import '../../widgets/trips/trip_students_list.dart';
import '../../widgets/trips/trip_route_map.dart';
import '../../../core/constants/size_constants.dart';

/// TripDetailsPage for displaying detailed trip information
/// Following Single Responsibility Principle by focusing only on trip details display
class TripDetailsPage extends StatelessWidget {
  const TripDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final String? tripId = Get.arguments?['tripId'];

    if (tripId == null) {
      return Scaffold(body: const Center(child: Text('معرف الرحلة غير صحيح')));
    }

    return Scaffold(
      backgroundColor: ColorConstants.background,
      body: ResponsiveSidebar(
        child: GetBuilder<TripController>(
          initState: (_) {
            // Load trip details when page initializes
            WidgetsBinding.instance.addPostFrameCallback((_) {
              Get.find<TripController>().loadTripById(tripId);
            });
          },
          builder: (controller) {
            return RefreshIndicator(
              onRefresh: () => controller.loadTripById(tripId),
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: EdgeInsets.all(SizeConstants.spaceBase),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Page header
                    _buildPageHeader(context, tripId),

                    SizedBox(height: SizeConstants.spaceLG),

                    // Trip content
                    _buildTripContent(controller),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPageHeader(BuildContext context, String tripId) {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceMD),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            ColorConstants.accent1,
            ColorConstants.accent1.withAlpha(204), // 0.8 * 255 = 204
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        boxShadow: [
          BoxShadow(
            color: ColorConstants.accent1.withAlpha(77), // 0.3 * 255 = 77
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(SizeConstants.spaceSM),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
              borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
            ),
            child: const Icon(
              Icons.info_outline_rounded,
              color: Colors.white,
              size: SizeConstants.iconLG,
            ),
          ),
          SizedBox(width: SizeConstants.spaceBase),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تفاصيل الرحلة',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: SizeConstants.spaceXS),
                Text(
                  'معرف الرحلة: $tripId',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withAlpha(230), // 0.9 * 255 = 230
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              IconButton(
                onPressed:
                    () => Get.find<TripController>().loadTripById(tripId),
                icon: const Icon(Icons.refresh_rounded, color: Colors.white),
                tooltip: 'تحديث',
              ),
              IconButton(
                onPressed: () => Get.back(),
                icon: const Icon(Icons.arrow_back_rounded, color: Colors.white),
                tooltip: 'العودة',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTripContent(TripController controller) {
    return Obx(() {
      if (controller.isLoading) {
        return const LoadingWidget(message: 'جاري تحميل تفاصيل الرحلة...');
      }

      if (controller.errorMessage.isNotEmpty) {
        return CustomErrorWidget(
          message: controller.errorMessage,
          onRetry: () => controller.loadTripById(Get.arguments?['tripId']),
        );
      }

      if (controller.selectedTrip == null) {
        return _buildTripNotFound();
      }

      final trip = controller.selectedTrip!;
      final isDesktop = ResponsiveUtils.isDesktop(Get.context!);

      if (isDesktop) {
        return _buildDesktopLayout(trip);
      } else {
        return _buildMobileLayout(trip);
      }
    });
  }

  Widget _buildTripNotFound() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search_off_rounded, size: SizeConstants.icon2XL, color: Colors.grey[400]),
          SizedBox(height: SizeConstants.spaceBase),
          Text(
            'الرحلة غير موجودة',
            style: Get.textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: SizeConstants.spaceXS),
          Text(
            'لم يتم العثور على الرحلة المطلوبة',
            style: Get.textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: SizeConstants.spaceBase),
          ElevatedButton.icon(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.arrow_back_rounded),
            label: const Text('العودة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Get.theme.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout(trip) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left column - Trip info and students
        Expanded(
          flex: 2,
          child: Column(
            children: [
              TripInfoCard(trip: trip),
              SizedBox(height: SizeConstants.spaceLG),
              TripStudentsList(trip: trip),
            ],
          ),
        ),

        SizedBox(width: SizeConstants.spaceLG),

        // Right column - Map and route
        Expanded(
          flex: 3,
          child: TripRouteMap(
            routeData: null, // TODO: Add route data from trip
            presentStudents: null, // TODO: Add present students from trip
            absentStudents: null, // TODO: Add absent students from trip
          ),
        ),
      ],
    );
  }

  Widget _buildMobileLayout(trip) {
    return Column(
      children: [
        TripInfoCard(trip: trip),
        SizedBox(height: SizeConstants.spaceLG),
        TripRouteMap(
          routeData: null, // TODO: Add route data from trip
          presentStudents: null, // TODO: Add present students from trip
          absentStudents: null, // TODO: Add absent students from trip
        ),
        SizedBox(height: SizeConstants.spaceLG),
        TripStudentsList(trip: trip),
      ],
    );
  }
}

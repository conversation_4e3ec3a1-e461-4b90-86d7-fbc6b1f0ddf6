import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/trip_controller.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';
import '../../widgets/common/search_field.dart';
import '../../widgets/trips/trip_card.dart';
import '../../widgets/trips/trip_filters_widget.dart';
import '../../routes/app_routes.dart';
import '../../../core/constants/size_constants.dart';

/// Enhanced trips management page with CRUD operations
/// Following Single Responsibility Principle by focusing only on trip management
class TripsManagementPage extends StatefulWidget {
  const TripsManagementPage({super.key});

  @override
  State<TripsManagementPage> createState() => _TripsManagementPageState();
}

class _TripsManagementPageState extends State<TripsManagementPage> {
  late TripController _controller;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _controller = Get.find<TripController>();
    _loadTrips();
    _setupScrollListener();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _loadTrips() {
    _controller.loadTrips();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        _controller.loadMoreTrips();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.background,
      body: ResponsiveSidebar(
        child: RefreshIndicator(
          onRefresh: () async => _loadTrips(),
          child: SingleChildScrollView(
            controller: _scrollController,
            physics: const AlwaysScrollableScrollPhysics(),
            padding: EdgeInsets.all(SizeConstants.spaceBase),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildPageHeader(context),
                SizedBox(height: SizeConstants.spaceLG),
                _buildFiltersSection(),
                SizedBox(height: SizeConstants.spaceLG),
                _buildTripsSection(),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildPageHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceMD),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withAlpha(204),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withAlpha(77),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(SizeConstants.spaceSM),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51),
              borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
            ),
            child: const Icon(
              Icons.directions_bus_rounded,
              color: Colors.white,
              size: SizeConstants.iconLG,
            ),
          ),
          SizedBox(width: SizeConstants.spaceBase),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إدارة الرحلات',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: SizeConstants.spaceXS),
                Text(
                  'إنشاء وتحرير ومراقبة جميع الرحلات المدرسية',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withAlpha(230),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: _loadTrips,
            icon: const Icon(Icons.refresh_rounded, color: Colors.white),
            tooltip: 'تحديث',
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.filter_list_rounded,
              color: Get.theme.primaryColor,
              size: SizeConstants.iconMD,
            ),
            SizedBox(width: SizeConstants.spaceXS),
            Text(
              'البحث والتصفية',
              style: Get.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        SizedBox(height: SizeConstants.spaceBase),
        if (ResponsiveUtils.isMobile(context))
          Column(
            children: [
              _buildSearchField(),
              SizedBox(height: SizeConstants.spaceBase),
              const TripFiltersWidget(),
            ],
          )
        else
          Row(
            children: [
              Expanded(flex: 2, child: _buildSearchField()),
              SizedBox(width: SizeConstants.spaceBase),
              const Expanded(flex: 3, child: TripFiltersWidget()),
            ],
          ),
      ],
    );
  }

  Widget _buildSearchField() {
    return SearchField(
      hint: 'البحث عن رحلة...',
      onChanged: (query) => _controller.searchTrips(query),
    );
  }

  Widget _buildTripsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(
                  Icons.list_rounded,
                  color: Get.theme.primaryColor,
                  size: SizeConstants.iconMD,
                ),
                SizedBox(width: SizeConstants.spaceXS),
                Text(
                  'قائمة الرحلات',
                  style: Get.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            Obx(() => Text(
              'المجموع: ${_controller.totalTrips}',
              style: Get.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            )),
          ],
        ),
        SizedBox(height: SizeConstants.spaceBase),
        Obx(() => _buildTripsContent()),
      ],
    );
  }

  Widget _buildTripsContent() {
    if (_controller.isLoading && _controller.trips.isEmpty) {
      return const LoadingWidget(message: 'جاري تحميل الرحلات...');
    }

    if (_controller.errorMessage.isNotEmpty && _controller.trips.isEmpty) {
      return CustomErrorWidget(
        message: _controller.errorMessage,
        onRetry: _loadTrips,
      );
    }

    if (_controller.trips.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        ...(_controller.trips.map((trip) => Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: TripCard(
            trip: trip,
            onTap: () => _viewTripDetails(trip.id),
            onEdit: () => _editTrip(trip.id),
            onDelete: () => _deleteTrip(trip.id),
            onStart: trip.status == 'scheduled' ? () => _startTrip(trip.id) : null,
            onEnd: trip.status == 'in_progress' ? () => _endTrip(trip.id) : null,
            onTrack: trip.status == 'in_progress' ? () => _trackTrip(trip.id) : null,
          ),
        ))),
        if (_controller.isLoadingMore)
          const Padding(
            padding: EdgeInsets.all(SizeConstants.spaceBase),
            child: LoadingWidget(size: SizeConstants.iconMD),
          ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(SizeConstants.space3XL),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          Icon(
            Icons.directions_bus_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          SizedBox(height: SizeConstants.spaceBase),
          Text(
            'لا توجد رحلات',
            style: Get.textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: SizeConstants.spaceXS),
          Text(
            'لم يتم العثور على رحلات مطابقة للبحث',
            style: Get.textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: SizeConstants.spaceLG),
          ElevatedButton.icon(
            onPressed: _createTrip,
            icon: const Icon(Icons.add, color: Colors.white),
            label: const Text('إنشاء رحلة جديدة', style: TextStyle(color: Colors.white)),
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorConstants.primary,
              padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceLG, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _createTrip,
      backgroundColor: ColorConstants.primary,
      icon: const Icon(Icons.add, color: Colors.white),
      label: const Text('رحلة جديدة', style: TextStyle(color: Colors.white)),
    );
  }

  void _createTrip() {
    Get.toNamed(AppRoutes.createTrip);
  }

  void _viewTripDetails(String tripId) {
    Get.toNamed(AppRoutes.tripDetails, arguments: {'tripId': tripId});
  }

  void _editTrip(String tripId) {
    Get.toNamed(AppRoutes.editTrip, arguments: {'tripId': tripId});
  }

  void _deleteTrip(String tripId) {
    _controller.deleteTrip(tripId);
  }

  void _startTrip(String tripId) {
    _controller.startTrip(tripId);
  }

  void _endTrip(String tripId) {
    _controller.endTrip(tripId);
  }

  void _trackTrip(String tripId) {
    Get.toNamed(AppRoutes.tripTracking, arguments: {'tripId': tripId});
  }
}

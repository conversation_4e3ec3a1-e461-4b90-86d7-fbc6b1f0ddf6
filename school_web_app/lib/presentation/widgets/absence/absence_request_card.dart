import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../domain/entities/absence_request.dart';
import '../../controllers/absence_controller.dart';
import '../../../core/constants/size_constants.dart';

/// Card widget for displaying absence request
class AbsenceRequestCard extends StatelessWidget {
  final AbsenceRequest request;
  final VoidCallback onViewDetails;

  const AbsenceRequestCard({
    super.key,
    required this.request,
    required this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<AbsenceController>();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(SizeConstants.radiusMD)),
      child: Padding(
        padding: EdgeInsets.all(SizeConstants.spaceBase),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with student name and attendance type
            Row(
              children: [
                // Student icon
                CircleAvatar(
                  backgroundColor: ColorConstants.primary.withValues(
                    alpha: 0.1,
                  ),
                  child: Icon(Icons.person, color: ColorConstants.primary),
                ),
                SizedBox(width: SizeConstants.spaceSM),
                // Student name
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        request.student?.name ?? 'غير محدد',
                        style: const TextStyle(
                          fontSize: SizeConstants.fontBase,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (request.parent?.name != null)
                        Text(
                          'ولي الأمر: ${request.parent!.name}',
                          style: TextStyle(
                            fontSize: SizeConstants.fontSM,
                            color: Colors.grey[600],
                          ),
                        ),
                    ],
                  ),
                ),
                // Attendance type badge
                _buildAttendanceTypeBadge(controller),
              ],
            ),
            SizedBox(height: SizeConstants.spaceBase),
            // Date and time information
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: ColorConstants.primary,
                  size: SizeConstants.iconSM,
                ),
                SizedBox(width: SizeConstants.spaceXS),
                Text(
                  'التاريخ: ${controller.formatDate(request.attendanceDate)}',
                  style: const TextStyle(
                    fontSize: SizeConstants.fontSM,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: SizeConstants.spaceSM),
            // School and grade information
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    context,
                    Icons.school,
                    'المدرسة',
                    request.school?.name ?? 'غير محدد',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    Icons.class_,
                    'الصف',
                    request.grade?.name ?? 'غير محدد',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    Icons.directions_bus,
                    'الحافلة',
                    request.bus?.name ?? 'غير محدد',
                  ),
                ),
              ],
            ),
            SizedBox(height: SizeConstants.spaceBase),
            // Action buttons
            // Action buttons
            Row(
              children: [
                // Approve button
                if (request.status == 'pending') ...[
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed:
                          () => _showApprovalDialog(context, controller, true),
                      icon: const Icon(Icons.check_circle, size: 18),
                      label: const Text('موافقة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: SizeConstants.spaceSM),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: SizeConstants.spaceSM),
                  // Reject button
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed:
                          () => _showApprovalDialog(context, controller, false),
                      icon: const Icon(Icons.cancel, size: 18),
                      label: const Text('رفض'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: SizeConstants.spaceSM),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: SizeConstants.spaceSM),
                ],
                // View details button
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: onViewDetails,
                    icon: const Icon(Icons.visibility, size: 18),
                    label: const Text('التفاصيل'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: ColorConstants.primary,
                      side: BorderSide(color: ColorConstants.primary),
                      padding: const EdgeInsets.symmetric(vertical: SizeConstants.spaceSM),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Show approval/rejection dialog
  void _showApprovalDialog(
    BuildContext context,
    AbsenceController controller,
    bool isApproval,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isApproval ? 'موافقة على الطلب' : 'رفض الطلب'),
            content: Text(
              isApproval
                  ? 'هل أنت متأكد من الموافقة على طلب الغياب لـ ${request.student?.name ?? "الطالب"}؟'
                  : 'هل أنت متأكد من رفض طلب الغياب لـ ${request.student?.name ?? "الطالب"}؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  if (isApproval) {
                    controller.approveAbsenceRequest(request.id!);
                  } else {
                    controller.rejectAbsenceRequest(request.id!);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: isApproval ? Colors.green : Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: Text(isApproval ? 'موافقة' : 'رفض'),
              ),
            ],
          ),
    );
  }

  /// Build attendance type badge
  Widget _buildAttendanceTypeBadge(AbsenceController controller) {
    final attendanceType = request.attendanceType;
    final displayName = controller.getAttendanceTypeDisplayName(attendanceType);
    final color = controller.getAttendanceTypeColor(attendanceType);

    IconData icon;
    switch (attendanceType?.toLowerCase()) {
      case 'present':
      case 'حاضر':
        icon = Icons.check_circle;
        break;
      case 'absent':
      case 'غائب':
        icon = Icons.cancel;
        break;
      case 'late':
      case 'متأخر':
        icon = Icons.access_time;
        break;
      case 'sick':
      case 'مريض':
        icon = Icons.local_hospital;
        break;
      case 'leave':
      case 'إجازة':
        icon = Icons.event_available;
        break;
      default:
        icon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceSM, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(SizeConstants.radiusXL),
        border: Border.all(color: color),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: SizeConstants.iconXS, color: color),
          SizedBox(width: SizeConstants.spaceXS),
          Text(
            displayName,
            style: TextStyle(color: color, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  /// Build info item
  Widget _buildInfoItem(
    BuildContext context,
    IconData icon,
    String label,
    String value,
  ) {
    return Row(
      children: [
        Icon(icon, color: ColorConstants.primary, size: SizeConstants.iconSM),
        SizedBox(width: SizeConstants.spaceXS),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(fontSize: SizeConstants.fontXS, color: Colors.grey[600]),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: SizeConstants.fontSM,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

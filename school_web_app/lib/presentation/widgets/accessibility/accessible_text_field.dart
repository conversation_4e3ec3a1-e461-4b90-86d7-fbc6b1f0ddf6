import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/constants/size_constants.dart';

/// AccessibleTextField - Enhanced text field with accessibility features
/// Following Single Responsibility Principle by focusing only on accessible text field functionality
class AccessibleTextField extends StatefulWidget {
  final String label;
  final String? hint;
  final String? value;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final TextInputType keyboardType;
  final bool obscureText;
  final bool enabled;
  final bool required;
  final int? maxLines;
  final int? maxLength;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? semanticLabel;
  final String? semanticHint;
  final bool autofocus;
  final TextInputAction? textInputAction;
  final VoidCallback? onEditingComplete;
  final ValueChanged<String>? onSubmitted;

  const AccessibleTextField({
    super.key,
    required this.label,
    this.hint,
    this.value,
    this.onChanged,
    this.onTap,
    this.keyboardType = TextInputType.text,
    this.obscureText = false,
    this.enabled = true,
    this.required = false,
    this.maxLines = 1,
    this.maxLength,
    this.controller,
    this.focusNode,
    this.validator,
    this.inputFormatters,
    this.prefixIcon,
    this.suffixIcon,
    this.semanticLabel,
    this.semanticHint,
    this.autofocus = false,
    this.textInputAction,
    this.onEditingComplete,
    this.onSubmitted,
  });

  @override
  State<AccessibleTextField> createState() => _AccessibleTextFieldState();
}

class _AccessibleTextFieldState extends State<AccessibleTextField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _isFocused = false;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _controller =
        widget.controller ?? TextEditingController(text: widget.value);
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    if (widget.controller == null) {
      _controller.dispose();
    }
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });

    // Announce field focus to screen readers
    if (_isFocused) {
      final semanticLabel =
          widget.semanticLabel ??
          AccessibilityHelper.createTextFieldLabel(
            widget.label,
            isRequired: widget.required,
            hint: widget.hint,
          );
      AccessibilityHelper.announceArabic(semanticLabel);
    }
  }

  void _validateField() {
    if (widget.validator != null) {
      setState(() {
        _errorText = widget.validator!(_controller.text);
      });

      // Announce validation error to screen readers
      if (_errorText != null) {
        final errorLabel = AccessibilityHelper.createValidationLabel(
          widget.label,
          isRequired: widget.required,
        );
        AccessibilityHelper.announceArabic('$errorLabel: $_errorText');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final semanticLabel =
        widget.semanticLabel ??
        AccessibilityHelper.createTextFieldLabel(
          widget.label,
          isRequired: widget.required,
          hint: widget.semanticHint ?? widget.hint,
        );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (widget.label.isNotEmpty) ...[
          Semantics(
            label: widget.label + (widget.required ? '. مطلوب' : ''),
            child: RichText(
              text: TextSpan(
                text: widget.label,
                style: TextStyle(
                  color: ColorConstants.text,
                  fontSize: SizeConstants.fontBase,
                  fontWeight: FontWeight.w500,
                ),
                children: [
                  if (widget.required)
                    TextSpan(
                      text: ' *',
                      style: TextStyle(
                        color: ColorConstants.error,
                        fontSize: SizeConstants.fontBase,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                ],
              ),
            ),
          ),
          SizedBox(height: SizeConstants.spaceXS),
        ],

        // Text Field
        Semantics(
          label: semanticLabel,
          textField: true,
          enabled: widget.enabled,
          focusable: true,
          child: TextFormField(
            controller: _controller,
            focusNode: _focusNode,
            onChanged: (value) {
              widget.onChanged?.call(value);
              if (widget.validator != null) {
                _validateField();
              }
            },
            onTap: widget.onTap,
            onEditingComplete: widget.onEditingComplete,
            onFieldSubmitted: widget.onSubmitted,
            keyboardType: widget.keyboardType,
            obscureText: widget.obscureText,
            enabled: widget.enabled,
            maxLines: widget.maxLines,
            maxLength: widget.maxLength,
            inputFormatters: widget.inputFormatters,
            autofocus: widget.autofocus,
            textInputAction: widget.textInputAction,
            decoration: InputDecoration(
              hintText: widget.hint,
              prefixIcon: widget.prefixIcon,
              suffixIcon: widget.suffixIcon,
              errorText: _errorText,
              filled: true,
              fillColor:
                  widget.enabled
                      ? ColorConstants.fillFormField
                      : ColorConstants.disabled.withValues(alpha: 0.1),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                borderSide: BorderSide(color: ColorConstants.border, width: 1),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                borderSide: BorderSide(color: ColorConstants.border, width: 1),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                borderSide: BorderSide(color: ColorConstants.primary, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                borderSide: BorderSide(color: ColorConstants.error, width: 1),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                borderSide: BorderSide(color: ColorConstants.error, width: 2),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                borderSide: BorderSide(
                  color: ColorConstants.disabled,
                  width: 1,
                ),
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: SizeConstants.spaceMD,
                vertical: 12,
              ),
              hintStyle: TextStyle(
                color: ColorConstants.textSecondary,
                fontSize: SizeConstants.fontBase,
              ),
              errorStyle: TextStyle(
                color: ColorConstants.error,
                fontSize: SizeConstants.fontSM,
              ),
            ),
            style: TextStyle(
              color:
                  widget.enabled
                      ? ColorConstants.text
                      : ColorConstants.textDisabled,
              fontSize: SizeConstants.fontBase,
            ),
          ),
        ),

        // Helper text or error message
        if (widget.hint != null && _errorText == null) ...[
          SizedBox(height: SizeConstants.spaceXS),
          Semantics(
            label: 'نصيحة: ${widget.hint}',
            child: Text(
              widget.hint!,
              style: TextStyle(
                color: ColorConstants.textSecondary,
                fontSize: SizeConstants.fontSM,
              ),
            ),
          ),
        ],
      ],
    );
  }
}

/// AccessibleSearchField - Enhanced search field with accessibility features
class AccessibleSearchField extends StatefulWidget {
  final String hint;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onClear;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final bool autofocus;

  const AccessibleSearchField({
    super.key,
    this.hint = 'البحث...',
    this.onChanged,
    this.onClear,
    this.controller,
    this.focusNode,
    this.autofocus = false,
  });

  @override
  State<AccessibleSearchField> createState() => _AccessibleSearchFieldState();
}

class _AccessibleSearchFieldState extends State<AccessibleSearchField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _focusNode = widget.focusNode ?? FocusNode();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: 'حقل البحث. ${widget.hint}',
      textField: true,
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        onChanged: widget.onChanged,
        autofocus: widget.autofocus,
        decoration: InputDecoration(
          hintText: widget.hint,
          prefixIcon: const Icon(Icons.search_rounded),
          suffixIcon:
              _controller.text.isNotEmpty
                  ? IconButton(
                    icon: const Icon(Icons.clear_rounded),
                    onPressed: () {
                      _controller.clear();
                      widget.onChanged?.call('');
                      widget.onClear?.call();
                    },
                    tooltip: 'مسح البحث',
                  )
                  : null,
          filled: true,
          fillColor: ColorConstants.fillFormField,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceBase,
            vertical: 12,
          ),
        ),
      ),
    );
  }
}

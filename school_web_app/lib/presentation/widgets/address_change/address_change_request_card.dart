import 'package:flutter/material.dart';
import '../../../core/constants/color_constants.dart';
import '../../../domain/entities/address_change_request.dart';
import '../../../core/constants/size_constants.dart';

/// Card widget for displaying address change request
class AddressChangeRequestCard extends StatelessWidget {
  final AddressChangeRequest request;
  final VoidCallback onAccept;
  final VoidCallback onRefuse;
  final VoidCallback onViewDetails;

  const AddressChangeRequestCard({
    super.key,
    required this.request,
    required this.onAccept,
    required this.onRefuse,
    required this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(SizeConstants.radiusMD)),
      child: Padding(
        padding: EdgeInsets.all(SizeConstants.spaceBase),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with student name and status
            Row(
              children: [
                // Student icon
                CircleAvatar(
                  backgroundColor: ColorConstants.primary.withValues(
                    alpha: 0.1,
                  ),
                  child: Icon(Icons.person, color: ColorConstants.primary),
                ),
                SizedBox(width: SizeConstants.spaceSM),
                // Student name
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        request.studentName,
                        style: const TextStyle(
                          fontSize: SizeConstants.fontBase,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (request.parentName != null)
                        Text(
                          'ولي الأمر: ${request.parentName}',
                          style: TextStyle(
                            fontSize: SizeConstants.fontSM,
                            color: Colors.grey[600],
                          ),
                        ),
                    ],
                  ),
                ),
                // Status badge
                _buildStatusBadge(context),
              ],
            ),
            SizedBox(height: SizeConstants.spaceBase),
            // Address information
            _buildAddressInfo(context),
            SizedBox(height: SizeConstants.spaceBase),
            // School and grade information
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    context,
                    Icons.school,
                    'المدرسة',
                    request.schoolName,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    Icons.class_,
                    'الصف',
                    request.gradeName,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    Icons.directions_bus,
                    'الحافلة',
                    request.busName,
                  ),
                ),
              ],
            ),
            SizedBox(height: SizeConstants.spaceBase),
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // View details button
                OutlinedButton.icon(
                  onPressed: onViewDetails,
                  icon: const Icon(Icons.visibility),
                  label: const Text('التفاصيل'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: ColorConstants.primary,
                  ),
                ),
                SizedBox(width: SizeConstants.spaceXS),
                // Accept button - only show for pending requests
                if (request.status == 3)
                  ElevatedButton.icon(
                    onPressed: onAccept,
                    icon: const Icon(Icons.check),
                    label: const Text('قبول'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                // Refuse button - only show for pending requests
                if (request.status == 3) SizedBox(width: SizeConstants.spaceXS),
                if (request.status == 3)
                  ElevatedButton.icon(
                    onPressed: onRefuse,
                    icon: const Icon(Icons.close),
                    label: const Text('رفض'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build status badge
  Widget _buildStatusBadge(BuildContext context) {
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (request.status) {
      case 1:
        statusColor = Colors.green;
        statusText = 'مقبول';
        statusIcon = Icons.check_circle;
        break;
      case 2:
        statusColor = Colors.red;
        statusText = 'مرفوض';
        statusIcon = Icons.cancel;
        break;
      case 3:
        statusColor = Colors.orange;
        statusText = 'في الانتظار';
        statusIcon = Icons.pending;
        break;
      default:
        statusColor = Colors.grey;
        statusText = 'غير محدد';
        statusIcon = Icons.help;
    }

    // Use status text from API if available
    if (request.statusText != null && request.statusText!.text != null) {
      statusText = request.statusText!.text!;

      // Use status color from API if available
      if (request.statusText!.color != null) {
        try {
          statusColor = Color(
            int.parse(request.statusText!.color!.replaceAll('#', '0xFF')),
          );
        } catch (e) {
          // Use default color if parsing fails
        }
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceSM, vertical: 6),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(SizeConstants.radiusXL),
        border: Border.all(color: statusColor),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusIcon, size: SizeConstants.iconXS, color: statusColor),
          SizedBox(width: SizeConstants.spaceXS),
          Text(
            statusText,
            style: TextStyle(color: statusColor, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  /// Build address information
  Widget _buildAddressInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (request.oldAddress != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(Icons.location_on, color: Colors.grey[600], size: SizeConstants.iconSM),
                SizedBox(width: SizeConstants.spaceXS),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'العنوان القديم:',
                        style: TextStyle(
                          fontSize: SizeConstants.fontSM,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        request.oldAddress!,
                        style: const TextStyle(fontSize: SizeConstants.fontSM),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        if (request.address != null)
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(Icons.location_on, color: ColorConstants.primary, size: SizeConstants.iconSM),
              SizedBox(width: SizeConstants.spaceXS),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'العنوان الجديد:',
                      style: TextStyle(
                        fontSize: SizeConstants.fontSM,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      request.address!,
                      style: const TextStyle(fontSize: SizeConstants.fontSM),
                    ),
                  ],
                ),
              ),
            ],
          ),
      ],
    );
  }

  /// Build info item
  Widget _buildInfoItem(
    BuildContext context,
    IconData icon,
    String label,
    String value,
  ) {
    return Row(
      children: [
        Icon(icon, color: ColorConstants.primary, size: SizeConstants.iconSM),
        SizedBox(width: SizeConstants.spaceXS),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(fontSize: SizeConstants.fontXS, color: Colors.grey[600]),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: SizeConstants.fontSM,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

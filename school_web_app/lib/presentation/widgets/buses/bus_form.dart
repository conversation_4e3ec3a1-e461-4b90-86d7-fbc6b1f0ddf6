import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/bus.dart';
import '../../controllers/buses_controller.dart';
import '../../../core/constants/size_constants.dart';

/// Bus form widget
/// Following Single Responsibility Principle by focusing only on form UI
class BusForm extends StatefulWidget {
  final BusesController controller;
  final GlobalKey<FormState> formKey;
  final bool isEdit;
  final Bus? bus;
  final VoidCallback onSubmit;

  const BusForm({
    super.key,
    required this.controller,
    required this.formKey,
    required this.isEdit,
    required this.onSubmit,
    this.bus,
  });

  @override
  State<BusForm> createState() => _BusFormState();
}

class _BusFormState extends State<BusForm> {
  @override
  void initState() {
    super.initState();

    // Initialize form with bus data if editing
    if (widget.isEdit && widget.bus != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.controller.initializeEditForm(widget.bus!);
      });
    } else {
      // Clear form for new bus
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.controller.clearFormControllers();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceLG),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Form(
        key: widget.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Form title
            _buildFormTitle(isDesktop),
            SizedBox(height: SizeConstants.spaceLG),

            // Form fields
            if (isDesktop || isTablet)
              _buildDesktopLayout()
            else
              _buildMobileLayout(),

            SizedBox(height: SizeConstants.spaceXL),

            // Form actions
            _buildFormActions(isDesktop),
          ],
        ),
      ),
    );
  }

  /// Build form title
  Widget _buildFormTitle(bool isDesktop) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Bus Information',
          style: TextStyle(
            fontSize: isDesktop ? 20 : 18,
            fontWeight: FontWeight.bold,
            color: ColorConstants.textPrimary,
          ),
        ),
        SizedBox(height: SizeConstants.spaceXS),
        Text(
          'Please fill in the bus details below',
          style: TextStyle(
            fontSize: isDesktop ? 16 : 14,
            color: ColorConstants.textSecondary,
          ),
        ),
      ],
    );
  }

  /// Build desktop layout (2 columns)
  Widget _buildDesktopLayout() {
    return Column(
      children: [
        // First row: Bus Name and Car Number
        Row(
          children: [
            Expanded(child: _buildBusNameField()),
            SizedBox(width: SizeConstants.spaceBase),
            Expanded(child: _buildCarNumberField()),
          ],
        ),
        SizedBox(height: SizeConstants.spaceMD),

        // Second row: Notes (full width)
        _buildNotesField(),
      ],
    );
  }

  /// Build mobile layout (single column)
  Widget _buildMobileLayout() {
    return Column(
      children: [
        _buildBusNameField(),
        SizedBox(height: SizeConstants.spaceMD),
        _buildCarNumberField(),
        SizedBox(height: SizeConstants.spaceMD),
        _buildNotesField(),
      ],
    );
  }

  /// Build bus name field
  Widget _buildBusNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Bus Name *',
          style: TextStyle(
            fontSize: SizeConstants.fontSM,
            fontWeight: FontWeight.w500,
            color: ColorConstants.textPrimary,
          ),
        ),
        SizedBox(height: SizeConstants.spaceXS),
        TextFormField(
          controller: widget.controller.nameController,
          decoration: InputDecoration(
            hintText: 'Enter bus name (e.g., Bus A1)',
            prefixIcon: Icon(
              Icons.directions_bus_rounded,
              color: ColorConstants.textSecondary,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              borderSide: BorderSide(color: ColorConstants.primary),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              borderSide: const BorderSide(color: Colors.red),
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
            contentPadding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceBase,
              vertical: 12,
            ),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Bus name is required';
            }
            if (value.trim().length < 2) {
              return 'Bus name must be at least 2 characters';
            }
            return null;
          },
        ),
      ],
    );
  }

  /// Build car number field
  Widget _buildCarNumberField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Car Number *',
          style: TextStyle(
            fontSize: SizeConstants.fontSM,
            fontWeight: FontWeight.w500,
            color: ColorConstants.textPrimary,
          ),
        ),
        SizedBox(height: SizeConstants.spaceXS),
        TextFormField(
          controller: widget.controller.carNumberController,
          decoration: InputDecoration(
            hintText: 'Enter car number (e.g., ABC-123)',
            prefixIcon: Icon(
              Icons.confirmation_number_rounded,
              color: ColorConstants.textSecondary,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              borderSide: BorderSide(color: ColorConstants.primary),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              borderSide: const BorderSide(color: Colors.red),
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
            contentPadding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceBase,
              vertical: 12,
            ),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Car number is required';
            }
            if (value.trim().length < 3) {
              return 'Car number must be at least 3 characters';
            }
            return null;
          },
        ),
      ],
    );
  }

  /// Build notes field
  Widget _buildNotesField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes',
          style: TextStyle(
            fontSize: SizeConstants.fontSM,
            fontWeight: FontWeight.w500,
            color: ColorConstants.textPrimary,
          ),
        ),
        SizedBox(height: SizeConstants.spaceXS),
        TextFormField(
          controller: widget.controller.notesController,
          maxLines: 4,
          decoration: InputDecoration(
            hintText: 'Enter any additional notes about this bus (optional)',
            prefixIcon: Padding(
              padding: const EdgeInsets.only(bottom: 60),
              child: Icon(
                Icons.notes_rounded,
                color: ColorConstants.textSecondary,
              ),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              borderSide: BorderSide(color: ColorConstants.primary),
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
            contentPadding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceBase,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }

  /// Build form actions
  Widget _buildFormActions(bool isDesktop) {
    return Row(
      children: [
        // Cancel button
        Expanded(
          child: OutlinedButton(
            onPressed: () => Get.back(),
            style: OutlinedButton.styleFrom(
              foregroundColor: ColorConstants.textSecondary,
              side: BorderSide(color: Colors.grey.shade300),
              padding: EdgeInsets.symmetric(
                vertical: isDesktop ? 16 : 12,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              ),
            ),
            child: const Text('Cancel'),
          ),
        ),
        SizedBox(width: SizeConstants.spaceBase),

        // Submit button
        Expanded(
          flex: 2,
          child: Obx(() => ElevatedButton(
            onPressed: widget.controller.isLoading ? null : widget.onSubmit,
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorConstants.primary,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                vertical: isDesktop ? 16 : 12,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              ),
              elevation: 0,
            ),
            child: widget.controller.isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(widget.isEdit ? 'Update Bus' : 'Add Bus'),
          )),
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/bus.dart';
import '../../controllers/buses_controller.dart';
import '../../routes/app_routes.dart';
import 'bus_details_dialog.dart';
import '../../../core/constants/size_constants.dart';

/// Buses table widget
/// Following Single Responsibility Principle by focusing only on table UI
class BusesTable extends StatelessWidget {
  final BusesController controller;

  const BusesTable({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    if (isDesktop || isTablet) {
      return _buildDesktopTable(context);
    } else {
      return _buildMobileList(context);
    }
  }

  /// Build desktop table view
  Widget _buildDesktopTable(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Table header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceLG, vertical: 16),
            decoration: BoxDecoration(
              color: ColorConstants.primary.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    'Bus Name',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: ColorConstants.textPrimary,
                      fontSize: SizeConstants.fontSM,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Car Number',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: ColorConstants.textPrimary,
                      fontSize: SizeConstants.fontSM,
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'Notes',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: ColorConstants.textPrimary,
                      fontSize: SizeConstants.fontSM,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Created Date',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: ColorConstants.textPrimary,
                      fontSize: SizeConstants.fontSM,
                    ),
                  ),
                ),
                const SizedBox(width: 140), // Actions column
              ],
            ),
          ),

          // Table rows
          Obx(() => Column(
            children: controller.buses.asMap().entries.map((entry) {
              final index = entry.key;
              final bus = entry.value;
              return _buildTableRow(context, bus, index);
            }).toList(),
          )),
        ],
      ),
    );
  }

  /// Build table row
  Widget _buildTableRow(BuildContext context, Bus bus, int index) {
    final isEven = index % 2 == 0;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceLG, vertical: 16),
      decoration: BoxDecoration(
        color: isEven ? Colors.white : Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Bus name
          Expanded(
            flex: 2,
            child: Text(
              bus.name ?? 'N/A',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: ColorConstants.textPrimary,
                fontSize: SizeConstants.fontSM,
              ),
            ),
          ),

          // Car number
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceXS, vertical: 4),
              decoration: BoxDecoration(
                color: ColorConstants.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                bus.carNumber ?? 'N/A',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color: ColorConstants.primary,
                  fontSize: SizeConstants.fontXS,
                ),
              ),
            ),
          ),

          // Notes
          Expanded(
            flex: 3,
            child: Text(
              bus.notes ?? 'No notes',
              style: TextStyle(
                color: ColorConstants.textSecondary,
                fontSize: SizeConstants.fontSM,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Created date
          Expanded(
            flex: 2,
            child: Text(
              _formatDate(bus.createdAt),
              style: TextStyle(
                color: ColorConstants.textSecondary,
                fontSize: SizeConstants.fontSM,
              ),
            ),
          ),

          // Actions
          SizedBox(
            width: 140,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // View details button
                IconButton(
                  onPressed: () => _showBusDetails(context, bus),
                  icon: Icon(
                    Icons.visibility_outlined,
                    color: ColorConstants.primary,
                    size: 18,
                  ),
                  tooltip: 'View Details',
                  padding: EdgeInsets.all(SizeConstants.spaceXS),
                  constraints: const BoxConstraints(
                    minWidth: 36,
                    minHeight: 36,
                  ),
                ),

                // Edit button
                IconButton(
                  onPressed: () => _editBus(bus),
                  icon: Icon(
                    Icons.edit_outlined,
                    color: Colors.orange,
                    size: 18,
                  ),
                  tooltip: 'Edit Bus',
                  padding: EdgeInsets.all(SizeConstants.spaceXS),
                  constraints: const BoxConstraints(
                    minWidth: 36,
                    minHeight: 36,
                  ),
                ),

                // Delete button
                IconButton(
                  onPressed: () => _deleteBus(context, bus),
                  icon: Icon(
                    Icons.delete_outline_rounded,
                    color: Colors.red,
                    size: 18,
                  ),
                  tooltip: 'Delete Bus',
                  padding: EdgeInsets.all(SizeConstants.spaceXS),
                  constraints: const BoxConstraints(
                    minWidth: 36,
                    minHeight: 36,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build mobile list view
  Widget _buildMobileList(BuildContext context) {
    return Obx(() => Column(
      children: controller.buses.map((bus) => _buildMobileCard(context, bus)).toList(),
    ));
  }

  /// Build mobile card
  Widget _buildMobileCard(BuildContext context, Bus bus) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: EdgeInsets.all(SizeConstants.spaceBase),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header row with name and actions
          Row(
            children: [
              Expanded(
                child: Text(
                  bus.name ?? 'N/A',
                  style: TextStyle(
                    fontSize: SizeConstants.fontSM,
                    fontWeight: FontWeight.bold,
                    color: ColorConstants.textPrimary,
                  ),
                ),
              ),
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'view':
                      _showBusDetails(context, bus);
                      break;
                    case 'edit':
                      _editBus(bus);
                      break;
                    case 'delete':
                      _deleteBus(context, bus);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'view',
                    child: Row(
                      children: [
                        Icon(Icons.visibility_outlined, size: SizeConstants.iconSM),
                        SizedBox(width: SizeConstants.spaceSM),
                        Text('View Details'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit_outlined, size: SizeConstants.iconSM, color: Colors.orange),
                        SizedBox(width: SizeConstants.spaceSM),
                        Text('Edit Bus'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete_outline_rounded, size: SizeConstants.iconSM, color: Colors.red),
                        SizedBox(width: SizeConstants.spaceSM),
                        Text('Delete Bus'),
                      ],
                    ),
                  ),
                ],
                child: Icon(
                  Icons.more_vert_rounded,
                  color: ColorConstants.textSecondary,
                ),
              ),
            ],
          ),
          SizedBox(height: SizeConstants.spaceSM),

          // Car number
          Container(
            padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceSM, vertical: 6),
            decoration: BoxDecoration(
              color: ColorConstants.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
            ),
            child: Text(
              bus.carNumber ?? 'N/A',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: ColorConstants.primary,
                fontSize: SizeConstants.fontSM,
              ),
            ),
          ),
          SizedBox(height: SizeConstants.spaceSM),

          // Notes
          if (bus.notes != null && bus.notes!.isNotEmpty)
            Text(
              bus.notes!,
              style: TextStyle(
                color: ColorConstants.textSecondary,
                fontSize: SizeConstants.fontSM,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          SizedBox(height: SizeConstants.spaceXS),

          // Created date
          Text(
            'Created: ${_formatDate(bus.createdAt)}',
            style: TextStyle(
              color: ColorConstants.textSecondary,
              fontSize: SizeConstants.fontXS,
            ),
          ),
        ],
      ),
    );
  }

  /// Format date string
  String _formatDate(String? dateString) {
    if (dateString == null) return 'N/A';

    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'N/A';
    }
  }

  /// Show bus details dialog
  void _showBusDetails(BuildContext context, Bus bus) {
    showDialog(
      context: context,
      builder: (context) => BusDetailsDialog(bus: bus),
    );
  }

  /// Edit bus
  void _editBus(Bus bus) {
    controller.initializeEditForm(bus);
    Get.toNamed(AppRoutes.editBus, arguments: bus);
  }

  /// Delete bus with confirmation
  void _deleteBus(BuildContext context, Bus bus) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Bus'),
        content: Text('Are you sure you want to delete "${bus.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              controller.deleteBus(bus.id!);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

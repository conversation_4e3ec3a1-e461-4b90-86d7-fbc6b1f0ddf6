import 'package:flutter/material.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/constants/size_constants.dart';

/// CustomButton widget for consistent button styling
/// Following Single Responsibility Principle by focusing only on button UI
class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isOutlined;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double? height;
  final double borderRadius;
  final IconData? icon;
  final bool iconAfterText;

  const CustomButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.isOutlined = false,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.height = SizeConstants.buttonHeightLG,
    this.borderRadius = SizeConstants.radiusLG,
    this.icon,
    this.iconAfterText = false,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? double.infinity,
      height: height,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor:
              isOutlined
                  ? Colors.transparent
                  : backgroundColor ?? ColorConstants.primary,
          foregroundColor:
              isOutlined
                  ? textColor ?? ColorConstants.primary
                  : textColor ?? ColorConstants.white,
          elevation: isOutlined ? 0 : 8,
          shadowColor:
              isOutlined
                  ? Colors.transparent
                  : ColorConstants.primary.withValues(alpha: 0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            side:
                isOutlined
                    ? BorderSide(
                      color: backgroundColor ?? ColorConstants.primary,
                      width: 2,
                    )
                    : BorderSide.none,
          ),
          padding: EdgeInsets.symmetric(
            horizontal: SizeConstants.spaceMD,
            vertical: SizeConstants.spaceSM,
          ),
          disabledBackgroundColor:
              isOutlined
                  ? Colors.transparent
                  : ColorConstants.primary.withAlpha(150),
          disabledForegroundColor:
              isOutlined
                  ? ColorConstants.primary.withAlpha(150)
                  : ColorConstants.white.withAlpha(150),
        ),
        child:
            isLoading
                ? SizedBox(
                  width: SizeConstants.iconMD,
                  height: SizeConstants.iconMD,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      isOutlined
                          ? ColorConstants.primary
                          : ColorConstants.white,
                    ),
                  ),
                )
                : _buildButtonContent(),
      ),
    );
  }

  Widget _buildButtonContent() {
    if (icon == null) {
      return Text(
        text,
        style: TextStyle(
          fontSize: SizeConstants.fontMD,
          fontWeight: FontWeight.bold,
          letterSpacing: 0.3,
        ),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (!iconAfterText) ...[
          Icon(icon, size: SizeConstants.iconMD),
          SizedBox(width: SizeConstants.spaceXS),
        ],
        Text(
          text,
          style: TextStyle(
            fontSize: SizeConstants.fontBase,
            fontWeight: FontWeight.bold,
            letterSpacing: 0.3,
          ),
        ),
        if (iconAfterText) ...[
          SizedBox(width: SizeConstants.spaceXS),
          Icon(icon, size: SizeConstants.iconMD),
        ],
      ],
    );
  }
}

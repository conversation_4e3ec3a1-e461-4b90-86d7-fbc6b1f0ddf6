import 'package:flutter/material.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/constants/size_constants.dart';

/// Custom error widget
/// Following Single Responsibility Principle by focusing only on error UI
class CustomErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final IconData? icon;

  const CustomErrorWidget({
    super.key,
    required this.message,
    this.onRetry,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(SizeConstants.spaceLG),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Error icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(40),
              ),
              child: Icon(
                icon ?? Icons.error_outline_rounded,
                size: 40,
                color: Colors.red,
              ),
            ),
            SizedBox(height: SizeConstants.spaceLG),

            // Error message
            Text(
              'Something went wrong',
              style: TextStyle(
                fontSize: SizeConstants.fontBase,
                fontWeight: FontWeight.bold,
                color: ColorConstants.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: SizeConstants.spaceSM),

            Text(
              message,
              style: TextStyle(
                fontSize: SizeConstants.fontBase,
                color: ColorConstants.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),

            // Retry button
            if (onRetry != null) ...[
              SizedBox(height: SizeConstants.spaceXL),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh_rounded),
                label: const Text('Try Again'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorConstants.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceLG,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

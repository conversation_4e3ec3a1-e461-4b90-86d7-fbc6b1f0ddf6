import 'package:flutter/material.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/constants/size_constants.dart';

/// Empty state widget for displaying when no data is available
/// Following Single Responsibility Principle by focusing only on empty state display
class EmptyStateWidget extends StatelessWidget {
  final IconData icon;
  final String title;
  final String description;
  final String? actionText;
  final VoidCallback? onAction;
  final Color? iconColor;
  final double? iconSize;

  const EmptyStateWidget({
    super.key,
    required this.icon,
    required this.title,
    required this.description,
    this.actionText,
    this.onAction,
    this.iconColor,
    this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(SizeConstants.spaceXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: iconSize ?? 80,
              color: iconColor ?? Colors.grey.shade400,
            ),
            SizedBox(height: SizeConstants.spaceLG),
            Text(
              title,
              style: TextStyle(
                fontSize: SizeConstants.fontMD,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).brightness == Brightness.dark
                    ? ColorConstants.textPrimaryDark
                    : ColorConstants.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: SizeConstants.spaceSM),
            Text(
              description,
              style: TextStyle(
                fontSize: SizeConstants.fontBase,
                color: Theme.of(context).brightness == Brightness.dark
                    ? ColorConstants.textSecondaryDark
                    : ColorConstants.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            if (actionText != null && onAction != null) ...[
              SizedBox(height: SizeConstants.spaceXL),
              ElevatedButton.icon(
                onPressed: onAction,
                icon: const Icon(Icons.add, color: ColorConstants.white),
                label: Text(
                  actionText!,
                  style: const TextStyle(color: ColorConstants.white),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorConstants.primary,
                  padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceLG,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

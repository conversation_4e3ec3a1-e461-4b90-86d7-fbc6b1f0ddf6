import 'package:flutter/material.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/constants/size_constants.dart';

/// Custom error widget with retry functionality
/// Following Single Responsibility Principle by focusing only on error display
class CustomErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final IconData? icon;

  const CustomErrorWidget({
    super.key,
    required this.message,
    this.onRetry,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon ?? Icons.error_outline,
              size: SizeConstants.icon2XL,
              color: ColorConstants.error,
            ),
            SizedBox(height: SizeConstants.spaceBase),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).brightness == Brightness.dark
                    ? ColorConstants.textPrimaryDark
                    : ColorConstants.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: SizeConstants.spaceXS),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).brightness == Brightness.dark
                    ? ColorConstants.textSecondaryDark
                    : ColorConstants.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              SizedBox(height: SizeConstants.spaceLG),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh, color: ColorConstants.white),
                label: const Text(
                  'إعادة المحاولة',
                  style: TextStyle(
                    color: ColorConstants.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorConstants.primary,
                  padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceLG,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

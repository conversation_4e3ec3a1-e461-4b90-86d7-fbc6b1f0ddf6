import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/color_constants.dart';
import '../../controllers/students_controller.dart';
import '../../../core/constants/size_constants.dart';

/// Import progress dialog widget
class ImportProgressDialog extends StatelessWidget {
  const ImportProgressDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<StudentsController>();

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
      ),
      child: Container(
        padding: EdgeInsets.all(SizeConstants.spaceLG),
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(SizeConstants.spaceSM),
                  decoration: BoxDecoration(
                    color: ColorConstants.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
                  ),
                  child: Icon(
                    Icons.upload_file_rounded,
                    color: ColorConstants.primary,
                    size: SizeConstants.iconMD,
                  ),
                ),
                SizedBox(width: SizeConstants.spaceBase),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Importing Students',
                        style: TextStyle(
                          fontSize: SizeConstants.fontSM,
                          fontWeight: FontWeight.bold,
                          color: ColorConstants.primary,
                        ),
                      ),
                      SizedBox(height: SizeConstants.spaceXS),
                      Obx(() => Text(
                        controller.importProgress,
                        style: TextStyle(
                          fontSize: SizeConstants.fontSM,
                          color: Colors.grey.shade600,
                        ),
                      )),
                    ],
                  ),
                ),
              ],
            ),
            
            SizedBox(height: SizeConstants.spaceLG),
            
            // Progress Indicator
            Obx(() => LinearProgressIndicator(
              backgroundColor: Colors.grey.shade200,
              valueColor: AlwaysStoppedAnimation<Color>(ColorConstants.primary),
            )),
            
            SizedBox(height: SizeConstants.spaceBase),
            
            // Progress Stats
            Obx(() => Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  children: [
                    Text(
                      '${controller.importedCount}',
                      style: TextStyle(
                        fontSize: SizeConstants.fontBase,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    Text(
                      'Imported',
                      style: TextStyle(
                        fontSize: SizeConstants.fontXS,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
                if (controller.importErrorCount > 0)
                  Column(
                    children: [
                      Text(
                        '${controller.importErrorCount}',
                        style: TextStyle(
                          fontSize: SizeConstants.fontBase,
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                      Text(
                        'Errors',
                        style: TextStyle(
                          fontSize: SizeConstants.fontXS,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
              ],
            )),
            
            SizedBox(height: SizeConstants.spaceLG),
            
            // Cancel Button
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => Get.back(),
                child: const Text('Cancel'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

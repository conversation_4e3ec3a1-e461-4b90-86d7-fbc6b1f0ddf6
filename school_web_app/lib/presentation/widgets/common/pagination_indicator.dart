import 'package:flutter/material.dart';

/// A reusable pagination indicator widget that safely handles load more functionality
/// This widget prevents the "setState during build" error by using WidgetsBinding.instance.addPostFrameCallback
class PaginationIndicator extends StatefulWidget {
  /// Whether there is more data to load
  final bool hasMoreData;

  /// Whether a load operation is currently in progress
  final bool isLoading;

  /// Callback function to trigger when more data should be loaded
  final VoidCallback onLoadMore;

  /// Custom loading widget (optional)
  final Widget? loadingWidget;

  /// Padding around the indicator
  final EdgeInsetsGeometry padding;

  const PaginationIndicator({
    super.key,
    required this.hasMoreData,
    required this.isLoading,
    required this.onLoadMore,
    this.loadingWidget,
    this.padding = const EdgeInsets.all(12),
  });

  @override
  State<PaginationIndicator> createState() => _PaginationIndicatorState();
}

class _PaginationIndicatorState extends State<PaginationIndicator> {
  bool _hasTriggeredLoad = false;

  @override
  void didUpdateWidget(PaginationIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Reset trigger flag when loading state changes
    if (oldWidget.isLoading != widget.isLoading) {
      _hasTriggeredLoad = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Don't show anything if there's no more data
    if (!widget.hasMoreData) {
      return const SizedBox.shrink();
    }

    // Trigger load more only once per build cycle and only if not already loading
    if (!_hasTriggeredLoad && !widget.isLoading) {
      _hasTriggeredLoad = true;

      // Use addPostFrameCallback to defer the call until after the build phase
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && !widget.isLoading && widget.hasMoreData) {
          widget.onLoadMore();
        }
      });
    }

    return Padding(
      padding: widget.padding,
      child: Center(
        child: widget.loadingWidget ?? const CircularProgressIndicator(),
      ),
    );
  }
}

/// Extension to make it easier to use with GetX controllers
extension PaginationIndicatorExtension on Widget {
  /// Wrap a ListView.builder with automatic pagination support
  static Widget buildPaginatedList<T>({
    required List<T> items,
    required bool hasMoreData,
    required bool isLoading,
    required VoidCallback onLoadMore,
    required Widget Function(BuildContext context, int index, T item)
    itemBuilder,
    EdgeInsetsGeometry? padding,
    ScrollController? controller,
    RefreshCallback? onRefresh,
    Widget? loadingWidget,
  }) {
    final listView = ListView.builder(
      controller: controller,
      padding: padding,
      itemCount: items.length + (hasMoreData ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == items.length) {
          return PaginationIndicator(
            hasMoreData: hasMoreData,
            isLoading: isLoading,
            onLoadMore: onLoadMore,
            loadingWidget: loadingWidget,
          );
        }

        return itemBuilder(context, index, items[index]);
      },
    );

    if (onRefresh != null) {
      return RefreshIndicator(onRefresh: onRefresh, child: listView);
    }

    return listView;
  }
}

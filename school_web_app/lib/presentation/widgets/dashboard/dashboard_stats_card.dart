import 'package:flutter/material.dart';
import '../../../core/constants/size_constants.dart';
import '../../../core/utils/responsive_utils.dart';

/// Enhanced DashboardStatsCard widget with modern design and animations
/// Following Single Responsibility Principle by focusing only on stats card UI
class DashboardStatsCard extends StatefulWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color iconColor;
  final Color iconBackgroundColor;
  final VoidCallback? onTap;
  final String? subtitle;
  final Widget? trailing;
  final String? trend;
  final bool? isPositiveTrend;

  const DashboardStatsCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.iconColor,
    required this.iconBackgroundColor,
    this.onTap,
    this.subtitle,
    this.trailing,
    this.trend,
    this.isPositiveTrend,
  });

  @override
  State<DashboardStatsCard> createState() => _DashboardStatsCardState();
}

class _DashboardStatsCardState extends State<DashboardStatsCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: MouseRegion(
            onEnter: (_) {
              setState(() => _isHovered = true);
              _animationController.forward();
            },
            onExit: (_) {
              setState(() => _isHovered = false);
              _animationController.reverse();
            },
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(SizeConstants.radiusXL),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Colors.white, Colors.grey.shade50],
                ),
                boxShadow: [
                  BoxShadow(
                    color: widget.iconColor.withValues(alpha: 0.1),
                    blurRadius: _isHovered ? 20 : 10,
                    offset: const Offset(0, 5),
                    spreadRadius: _isHovered ? 2 : 0,
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color:
                      _isHovered
                          ? widget.iconColor.withValues(alpha: 0.2)
                          : Colors.grey.shade200,
                  width: 1,
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: widget.onTap,
                  borderRadius: BorderRadius.circular(SizeConstants.radiusXL),
                  child: Padding(
                    padding: EdgeInsets.all(SizeConstants.spaceMD),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Icon and trend row
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Enhanced icon with gradient background
                            Container(
                              padding: EdgeInsets.all(SizeConstants.spaceSM),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    widget.iconBackgroundColor,
                                    widget.iconBackgroundColor.withValues(
                                      alpha: 0.8,
                                    ),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
                                boxShadow: [
                                  BoxShadow(
                                    color: widget.iconColor.withValues(
                                      alpha: 0.3,
                                    ),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Icon(
                                widget.icon,
                                color: widget.iconColor,
                                size: ResponsiveUtils.getResponsiveIconSize(
                                  context,
                                  mobile: SizeConstants.iconMD,
                                  tablet: SizeConstants.iconLG,
                                  desktop: SizeConstants.iconXL,
                                ),
                              ),
                            ),

                            // Trend indicator
                            if (widget.trend != null) _buildTrendIndicator(),

                            // Trailing widget (if provided)
                            if (widget.trailing != null) widget.trailing!,
                          ],
                        ),
                        SizedBox(height: SizeConstants.spaceMD),

                        // Value with enhanced styling
                        Text(
                          widget.value,
                          style: TextStyle(
                            fontSize:
                                ResponsiveUtils.getResponsiveFontSizeByDevice(
                                  context,
                                  mobile: SizeConstants.fontXL,
                                  tablet: SizeConstants.font2XL,
                                  desktop: SizeConstants.font3XL,
                                ),
                            fontWeight: FontWeight.w800,
                            color: Colors.black87,
                            letterSpacing: -0.5,
                          ),
                        ),
                        SizedBox(height: SizeConstants.spaceXS),

                        // Title with improved typography
                        Text(
                          widget.title,
                          style: TextStyle(
                            fontSize:
                                ResponsiveUtils.getResponsiveFontSizeByDevice(
                                  context,
                                  mobile: 14,
                                  tablet: 16,
                                  desktop: 18,
                                ),
                            fontWeight: FontWeight.w600,
                            color: Colors.black54,
                            letterSpacing: 0.2,
                          ),
                        ),

                        // Subtitle (if provided)
                        if (widget.subtitle != null) ...[
                          const SizedBox(height: 6),
                          Text(
                            widget.subtitle!,
                            style: TextStyle(
                              fontSize:
                                  ResponsiveUtils.getResponsiveFontSizeByDevice(
                                    context,
                                    mobile: 12,
                                    tablet: 14,
                                    desktop: 16,
                                  ),
                              color: Colors.black38,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTrendIndicator() {
    if (widget.trend == null) return const SizedBox();

    final isPositive = widget.isPositiveTrend ?? true;
    final color = isPositive ? Colors.green : Colors.red;
    final icon = isPositive ? Icons.trending_up : Icons.trending_down;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceXS, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: SizeConstants.iconXS, color: color),
          SizedBox(width: SizeConstants.spaceXS),
          Text(
            widget.trend!,
            style: TextStyle(
              fontSize: SizeConstants.fontXS,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}

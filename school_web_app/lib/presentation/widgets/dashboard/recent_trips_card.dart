import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/trip.dart';
import '../../../core/constants/size_constants.dart';

/// RecentTripsCard widget for displaying recent trips
/// Following Single Responsibility Principle by focusing only on recent trips UI
class RecentTripsCard extends StatelessWidget {
  final List<Trip> recentTrips;
  final VoidCallback? onViewAll;

  const RecentTripsCard({super.key, required this.recentTrips, this.onViewAll});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
        side: BorderSide(color: Colors.grey.shade200, width: 1),
      ),
      color: Colors.white,
      child: Padding(
        padding: EdgeInsets.all(SizeConstants.spaceBase),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الرحلات الأخيرة',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                      context,
                      mobile: 16,
                      tablet: 18,
                      desktop: 20,
                    ),
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),

                // View all button
                if (onViewAll != null)
                  TextButton(
                    onPressed: onViewAll,
                    child: Text(
                      'عرض الكل',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                          context,
                          mobile: 12,
                          tablet: 14,
                          desktop: 16,
                        ),
                        color: ColorConstants.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            SizedBox(height: SizeConstants.spaceBase),

            // Trips list
            recentTrips.isEmpty ? _buildEmptyState() : _buildTripsList(context),
          ],
        ),
      ),
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return SizedBox(
      height: 200,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.history_rounded, size: SizeConstants.iconXL, color: Colors.black38),
            SizedBox(height: SizeConstants.spaceBase),
            Text(
              'لا توجد رحلات سابقة',
              style: TextStyle(fontSize: SizeConstants.fontBase, color: Colors.black38),
            ),
          ],
        ),
      ),
    );
  }

  /// Build trips list
  Widget _buildTripsList(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: recentTrips.length > 5 ? 5 : recentTrips.length,
      separatorBuilder:
          (context, index) => Divider(color: Colors.grey.shade200, height: 24),
      itemBuilder: (context, index) {
        final trip = recentTrips[index];
        return _buildTripItem(context, trip);
      },
    );
  }

  /// Build trip item
  Widget _buildTripItem(BuildContext context, Trip trip) {
    // Format date and time
    final dateFormat = DateFormat('yyyy-MM-dd');
    final timeFormat = DateFormat('HH:mm');
    final date = dateFormat.format(trip.endTime ?? trip.startTime);
    final time = timeFormat.format(trip.endTime ?? trip.startTime);

    // Get status color
    Color statusColor;
    String statusText;

    if (trip.status == 'completed') {
      statusColor = Colors.green;
      statusText = 'مكتملة';
    } else if (trip.status == 'cancelled') {
      statusColor = Colors.red;
      statusText = 'ملغاة';
    } else if (trip.status == 'delayed') {
      statusColor = Colors.orange;
      statusText = 'متأخرة';
    } else {
      statusColor = Colors.blue;
      statusText = 'قيد التنفيذ';
    }

    return InkWell(
      onTap: () {
        // Navigate to trip details
      },
      borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: SizeConstants.spaceXS),
        child: Row(
          children: [
            // Trip icon with status
            Stack(
              children: [
                // Trip icon
                Container(
                  padding: EdgeInsets.all(SizeConstants.spaceXS),
                  decoration: BoxDecoration(
                    color: ColorConstants.primary.withAlpha(26),
                    borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                  ),
                  child: Icon(
                    Icons.directions_bus_rounded,
                    color: ColorConstants.primary,
                    size: ResponsiveUtils.getResponsiveIconSize(
                      context,
                      mobile: 20,
                      tablet: 24,
                      desktop: 28,
                    ),
                  ),
                ),

                // Status indicator
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: statusColor,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(width: SizeConstants.spaceSM),

            // Trip details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Trip name
                  Text(
                    trip.name,
                    style: TextStyle(
                      fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                        context,
                        mobile: 14,
                        tablet: 16,
                        desktop: 18,
                      ),
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  SizedBox(height: SizeConstants.spaceXS),

                  // Trip details
                  Text(
                    '${trip.schoolName} - ${trip.busNumber}',
                    style: TextStyle(
                      fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                        context,
                        mobile: 12,
                        tablet: 14,
                        desktop: 16,
                      ),
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
            ),

            // Trip status, date and time
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // Status
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceXS,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withAlpha(26),
                    borderRadius: BorderRadius.circular(SizeConstants.radiusXS),
                  ),
                  child: Text(
                    statusText,
                    style: TextStyle(
                      fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                        context,
                        mobile: 10,
                        tablet: 12,
                        desktop: 14,
                      ),
                      fontWeight: FontWeight.bold,
                      color: statusColor,
                    ),
                  ),
                ),
                SizedBox(height: SizeConstants.spaceXS),

                // Date and time
                Text(
                  '$date - $time',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                      context,
                      mobile: 10,
                      tablet: 12,
                      desktop: 14,
                    ),
                    color: Colors.black54,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

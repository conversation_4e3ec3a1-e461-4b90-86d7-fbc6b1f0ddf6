import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/drivers_controller.dart';
import '../../routes/app_routes.dart';
import '../../../core/constants/size_constants.dart';

/// DriversHeader widget for drivers page header
/// Following Single Responsibility Principle by focusing only on header UI
class DriversHeader extends StatelessWidget {
  const DriversHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DriversController>();
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceLG),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            ColorConstants.primary.withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(SizeConstants.radiusXL),
        boxShadow: [
          BoxShadow(
            color: ColorConstants.primary.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: ColorConstants.primary.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header row with title and add button
          _buildHeaderRow(context, isDesktop, isTablet),
          
          if (!isDesktop) SizedBox(height: SizeConstants.spaceBase),
          
          // Search bar
          _buildSearchBar(context, controller, isDesktop),
        ],
      ),
    );
  }

  /// Build header row with title and add button
  Widget _buildHeaderRow(BuildContext context, bool isDesktop, bool isTablet) {
    if (isDesktop || isTablet) {
      return Row(
        children: [
          // Title and subtitle
          Expanded(child: _buildTitleSection(context, isDesktop)),

          // Add driver button
          _buildAddDriverButton(isDesktop),
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and subtitle
          _buildTitleSection(context, isDesktop),
          SizedBox(height: SizeConstants.spaceBase),

          // Add driver button (full width on mobile)
          SizedBox(
            width: double.infinity,
            child: _buildAddDriverButton(isDesktop),
          ),
        ],
      );
    }
  }

  /// Build title section
  Widget _buildTitleSection(BuildContext context, bool isDesktop) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(SizeConstants.spaceSM),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    ColorConstants.primary.withValues(alpha: 0.15),
                    ColorConstants.primary.withValues(alpha: 0.08),
                  ],
                ),
                borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
                boxShadow: [
                  BoxShadow(
                    color: ColorConstants.primary.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                Icons.person_pin_rounded,
                color: ColorConstants.primary,
                size: isDesktop ? 32 : 28,
              ),
            ),
            SizedBox(width: SizeConstants.spaceBase),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'إدارة السائقين',
                    style: TextStyle(
                      fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                        context,
                        mobile: 24,
                        tablet: 28,
                        desktop: 32,
                      ),
                      fontWeight: FontWeight.w800,
                      color: ColorConstants.textPrimary,
                      letterSpacing: -0.5,
                    ),
                  ),
                  SizedBox(height: SizeConstants.spaceXS),
                  Text(
                    'إدارة وتنظيم سائقي المدرسة',
                    style: TextStyle(
                      fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                        context,
                        mobile: 14,
                        tablet: 16,
                        desktop: 18,
                      ),
                      color: ColorConstants.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build search bar
  Widget _buildSearchBar(BuildContext context, DriversController controller, bool isDesktop) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: isDesktop ? 400 : double.infinity,
      ),
      child: TextField(
        onChanged: controller.searchDrivers,
        decoration: InputDecoration(
          hintText: 'البحث عن السائقين...',
          hintStyle: TextStyle(
            color: ColorConstants.textSecondary,
            fontSize: SizeConstants.fontSM,
          ),
          prefixIcon: Icon(
            Icons.search_rounded,
            color: ColorConstants.textSecondary,
            size: SizeConstants.iconSM,
          ),
          filled: true,
          fillColor: Colors.grey.shade50,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            borderSide: BorderSide(
              color: Colors.grey.shade200,
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            borderSide: BorderSide(
              color: Colors.grey.shade200,
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            borderSide: BorderSide(
              color: ColorConstants.primary,
              width: 2,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceBase,
            vertical: 12,
          ),
        ),
        style: const TextStyle(
          fontSize: SizeConstants.fontSM,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// Build add driver button
  Widget _buildAddDriverButton(bool isDesktop) {
    return ElevatedButton.icon(
      onPressed: () => Get.toNamed(AppRoutes.addDriver),
      icon: const Icon(Icons.add_rounded),
      label: const Text('إضافة سائق'),
      style: ElevatedButton.styleFrom(
        backgroundColor: ColorConstants.primary,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(
          horizontal: isDesktop ? 24 : 20,
          vertical: isDesktop ? 16 : 12,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        ),
        elevation: 0,
        shadowColor: ColorConstants.primary.withValues(alpha: 0.3),
      ),
    );
  }
}

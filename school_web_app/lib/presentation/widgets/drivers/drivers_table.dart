import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/driver.dart';
import '../../controllers/drivers_controller.dart';
import '../../routes/app_routes.dart';
import '../../../core/constants/size_constants.dart';

/// DriversTable widget for displaying drivers in a table format
/// Following Single Responsibility Principle by focusing only on table UI
class DriversTable extends StatefulWidget {
  final double height;

  const DriversTable({
    super.key,
    required this.height,
  });

  @override
  State<DriversTable> createState() => _DriversTableState();
}

class _DriversTableState extends State<DriversTable> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final controller = Get.find<DriversController>();
      controller.loadMoreDrivers();
    }
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DriversController>();
    final isDesktop = ResponsiveUtils.isDesktop(context);

    return Obx(() {
      if (controller.isLoading && controller.drivers.isEmpty) {
        return _buildLoadingState();
      }

      if (controller.errorMessage.isNotEmpty && controller.drivers.isEmpty) {
        return _buildErrorState(controller);
      }

      if (controller.drivers.isEmpty) {
        return _buildEmptyState();
      }

      return Column(
        children: [
          // Table header
          _buildTableHeader(context, isDesktop),
          
          // Divider
          Divider(
            height: 1,
            color: Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.textSecondaryDark.withAlpha(80)
                : Colors.grey.shade200,
          ),
          
          // Table content
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: EdgeInsets.zero,
              itemCount: controller.drivers.length + (controller.isLoadingMore ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == controller.drivers.length) {
                  return _buildLoadingMoreIndicator();
                }
                
                final driver = controller.drivers[index];
                return _buildDriverRow(context, driver, index, isDesktop);
              },
            ),
          ),
        ],
      );
    });
  }

  /// Build table header
  Widget _buildTableHeader(BuildContext context, bool isDesktop) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceBase, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          // Name column
          Expanded(
            flex: 3,
            child: Text(
              'الاسم',
              style: TextStyle(
                fontSize: SizeConstants.fontSM,
                fontWeight: FontWeight.w600,
                color: ColorConstants.textPrimary,
              ),
            ),
          ),
          
          if (isDesktop) ...[
            // Username column
            Expanded(
              flex: 2,
              child: Text(
                'اسم المستخدم',
                style: TextStyle(
                  fontSize: SizeConstants.fontSM,
                  fontWeight: FontWeight.w600,
                  color: ColorConstants.textPrimary,
                ),
              ),
            ),
            
            // Phone column
            Expanded(
              flex: 2,
              child: Text(
                'رقم الهاتف',
                style: TextStyle(
                  fontSize: SizeConstants.fontSM,
                  fontWeight: FontWeight.w600,
                  color: ColorConstants.textPrimary,
                ),
              ),
            ),
            
            // Bus column
            Expanded(
              flex: 2,
              child: Text(
                'الحافلة',
                style: TextStyle(
                  fontSize: SizeConstants.fontSM,
                  fontWeight: FontWeight.w600,
                  color: ColorConstants.textPrimary,
                ),
              ),
            ),
          ],
          
          // Actions column
          const SizedBox(width: 140), // Actions column
        ],
      ),
    );
  }

  /// Build driver row
  Widget _buildDriverRow(BuildContext context, Driver driver, int index, bool isDesktop) {
    final isEven = index % 2 == 0;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceBase, vertical: 12),
      decoration: BoxDecoration(
        color: isEven ? Colors.white : Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade100,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // Name column with avatar
          Expanded(
            flex: 3,
            child: Row(
              children: [
                // Avatar
                Container(
                  width: 40,
                  height: SizeConstants.buttonHeightMD,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        ColorConstants.primary,
                        ColorConstants.primary.withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(SizeConstants.radiusXL),
                  ),
                  child: Center(
                    child: Text(
                      driver.name?.isNotEmpty == true
                          ? driver.name![0].toUpperCase()
                          : 'S',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: SizeConstants.fontBase,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: SizeConstants.spaceSM),
                
                // Name and details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        driver.name ?? 'غير محدد',
                        style: TextStyle(
                          fontSize: SizeConstants.fontSM,
                          fontWeight: FontWeight.w600,
                          color: ColorConstants.textPrimary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (!isDesktop && driver.phone != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          driver.phone!,
                          style: TextStyle(
                            fontSize: SizeConstants.fontXS,
                            color: ColorConstants.textSecondary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          if (isDesktop) ...[
            // Username column
            Expanded(
              flex: 2,
              child: Text(
                driver.username ?? 'غير محدد',
                style: TextStyle(
                  fontSize: SizeConstants.fontSM,
                  color: ColorConstants.textSecondary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            
            // Phone column
            Expanded(
              flex: 2,
              child: Text(
                driver.phone ?? 'غير محدد',
                style: TextStyle(
                  fontSize: SizeConstants.fontSM,
                  color: ColorConstants.textSecondary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            
            // Bus column
            Expanded(
              flex: 2,
              child: driver.busName != null
                  ? Container(
                      padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceXS, vertical: 4),
                      decoration: BoxDecoration(
                        color: ColorConstants.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                        border: Border.all(
                          color: ColorConstants.primary.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        driver.busName!,
                        style: TextStyle(
                          fontSize: SizeConstants.fontXS,
                          fontWeight: FontWeight.w600,
                          color: ColorConstants.primary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    )
                  : Text(
                      'غير مخصص',
                      style: TextStyle(
                        fontSize: SizeConstants.fontSM,
                        color: ColorConstants.textSecondary,
                      ),
                    ),
            ),
          ],
          
          // Actions
          SizedBox(
            width: 140,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // View details button
                IconButton(
                  onPressed: () => _showDriverDetails(context, driver),
                  icon: Icon(
                    Icons.visibility_outlined,
                    color: ColorConstants.primary,
                    size: 18,
                  ),
                  tooltip: 'عرض التفاصيل',
                  padding: EdgeInsets.all(SizeConstants.spaceXS),
                  constraints: const BoxConstraints(
                    minWidth: 36,
                    minHeight: 36,
                  ),
                ),

                // Edit button
                IconButton(
                  onPressed: () => _editDriver(driver),
                  icon: Icon(
                    Icons.edit_outlined,
                    color: Colors.orange,
                    size: 18,
                  ),
                  tooltip: 'تعديل السائق',
                  padding: EdgeInsets.all(SizeConstants.spaceXS),
                  constraints: const BoxConstraints(
                    minWidth: 36,
                    minHeight: 36,
                  ),
                ),

                // Delete button
                IconButton(
                  onPressed: () => _deleteDriver(context, driver),
                  icon: Icon(
                    Icons.delete_outline_rounded,
                    color: Colors.red,
                    size: 18,
                  ),
                  tooltip: 'حذف السائق',
                  padding: EdgeInsets.all(SizeConstants.spaceXS),
                  constraints: const BoxConstraints(
                    minWidth: 36,
                    minHeight: 36,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build loading state
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(ColorConstants.primary),
          ),
          SizedBox(height: SizeConstants.spaceBase),
          Text(
            'جاري تحميل السائقين...',
            style: TextStyle(
              fontSize: SizeConstants.fontBase,
              color: ColorConstants.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// Build error state
  Widget _buildErrorState(DriversController controller) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline_rounded,
            size: SizeConstants.icon2XL,
            color: Colors.red.shade300,
          ),
          SizedBox(height: SizeConstants.spaceBase),
          Text(
            'حدث خطأ في تحميل السائقين',
            style: TextStyle(
              fontSize: SizeConstants.fontSM,
              fontWeight: FontWeight.w600,
              color: ColorConstants.textPrimary,
            ),
          ),
          SizedBox(height: SizeConstants.spaceXS),
          Text(
            controller.errorMessage,
            style: TextStyle(
              fontSize: SizeConstants.fontSM,
              color: ColorConstants.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: SizeConstants.spaceLG),
          ElevatedButton.icon(
            onPressed: () => controller.refreshDrivers(),
            icon: const Icon(Icons.refresh_rounded),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorConstants.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_off_rounded,
            size: SizeConstants.icon2XL,
            color: Colors.grey.shade400,
          ),
          SizedBox(height: SizeConstants.spaceBase),
          Text(
            'لا توجد سائقين',
            style: TextStyle(
              fontSize: SizeConstants.fontSM,
              fontWeight: FontWeight.w600,
              color: ColorConstants.textPrimary,
            ),
          ),
          SizedBox(height: SizeConstants.spaceXS),
          Text(
            'لم يتم العثور على أي سائقين في النظام',
            style: TextStyle(
              fontSize: SizeConstants.fontSM,
              color: ColorConstants.textSecondary,
            ),
          ),
          SizedBox(height: SizeConstants.spaceLG),
          ElevatedButton.icon(
            onPressed: () => Get.toNamed(AppRoutes.addDriver),
            icon: const Icon(Icons.add_rounded),
            label: const Text('إضافة سائق جديد'),
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorConstants.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// Build loading more indicator
  Widget _buildLoadingMoreIndicator() {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceBase),
      child: Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(ColorConstants.primary),
        ),
      ),
    );
  }

  /// Show driver details
  void _showDriverDetails(BuildContext context, Driver driver) {
    Get.toNamed(AppRoutes.driverDetails, arguments: driver);
  }

  /// Edit driver
  void _editDriver(Driver driver) {
    Get.toNamed(AppRoutes.editDriver, arguments: driver);
  }

  /// Delete driver with enhanced confirmation dialog
  void _deleteDriver(BuildContext context, Driver driver) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
          ),
          title: Row(
            children: [
              Icon(
                Icons.warning_rounded,
                color: Colors.red,
                size: 28,
              ),
              SizedBox(width: SizeConstants.spaceSM),
              const Text(
                'تأكيد الحذف',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: SizeConstants.fontSM,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'هل أنت متأكد من حذف السائق التالي؟',
                style: TextStyle(
                  fontSize: SizeConstants.fontBase,
                  color: ColorConstants.textPrimary,
                ),
              ),
              SizedBox(height: SizeConstants.spaceBase),
              Container(
                padding: EdgeInsets.all(SizeConstants.spaceSM),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.person,
                          color: ColorConstants.primary,
                          size: SizeConstants.iconSM,
                        ),
                        SizedBox(width: SizeConstants.spaceXS),
                        Text(
                          'الاسم: ${driver.name ?? "غير محدد"}',
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: SizeConstants.fontSM,
                          ),
                        ),
                      ],
                    ),
                    if (driver.phone != null) ...[
                      SizedBox(height: SizeConstants.spaceXS),
                      Row(
                        children: [
                          Icon(
                            Icons.phone,
                            color: ColorConstants.primary,
                            size: SizeConstants.iconSM,
                          ),
                          SizedBox(width: SizeConstants.spaceXS),
                          Text(
                            'الهاتف: ${driver.phone}',
                            style: const TextStyle(fontSize: SizeConstants.fontSM),
                          ),
                        ],
                      ),
                    ],
                    if (driver.busName != null) ...[
                      SizedBox(height: SizeConstants.spaceXS),
                      Row(
                        children: [
                          Icon(
                            Icons.directions_bus,
                            color: ColorConstants.primary,
                            size: SizeConstants.iconSM,
                          ),
                          SizedBox(width: SizeConstants.spaceXS),
                          Text(
                            'الحافلة: ${driver.busName}',
                            style: const TextStyle(fontSize: SizeConstants.fontSM),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              SizedBox(height: SizeConstants.spaceBase),
              Container(
                padding: EdgeInsets.all(SizeConstants.spaceSM),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.red.shade600,
                      size: SizeConstants.iconSM,
                    ),
                    SizedBox(width: SizeConstants.spaceXS),
                    Expanded(
                      child: Text(
                        'تحذير: لا يمكن التراجع عن هذا الإجراء',
                        style: TextStyle(
                          color: Colors.red.shade700,
                          fontSize: SizeConstants.fontXS,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceMD, vertical: 12),
              ),
              child: const Text(
                'إلغاء',
                style: TextStyle(
                  fontSize: SizeConstants.fontSM,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton.icon(
              onPressed: () async {
                Navigator.of(context).pop();
                final controller = Get.find<DriversController>();
                if (driver.id != null) {
                  await controller.deleteDriver(driver.id!);
                }
              },
              icon: const Icon(Icons.delete_forever, size: 18),
              label: const Text(
                'حذف نهائياً',
                style: TextStyle(
                  fontSize: SizeConstants.fontSM,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceMD, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/trip.dart';
import '../../../core/constants/size_constants.dart';

/// CurrentTripCard widget for displaying current trip
/// Following Single Responsibility Principle by focusing only on current trip UI
class CurrentTripCard extends StatelessWidget {
  final Trip trip;
  final VoidCallback onTap;

  const CurrentTripCard({super.key, required this.trip, required this.onTap});

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('hh:mm a');
    final startTime = dateFormat.format(trip.startTime);
    final endTime =
        trip.endTime != null ? dateFormat.format(trip.endTime!) : 'جاري';

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
        child: Padding(
          padding: EdgeInsets.all(SizeConstants.spaceBase),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Title
                  Text(
                    'الرحلة الحالية',
                    style: TextStyle(
                      fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                        context,
                        mobile: 16,
                        tablet: 18,
                        desktop: 20,
                      ),
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),

                  // Status
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceSM,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(trip.status).withAlpha(26),
                      borderRadius: BorderRadius.circular(SizeConstants.radiusXL),
                    ),
                    child: Text(
                      _getStatusText(trip.status),
                      style: TextStyle(
                        fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                          context,
                          mobile: 12,
                          tablet: 14,
                          desktop: 16,
                        ),
                        fontWeight: FontWeight.bold,
                        color: _getStatusColor(trip.status),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: SizeConstants.spaceBase),

              // Trip details
              Row(
                children: [
                  // Bus info
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      Icons.directions_bus_rounded,
                      'الحافلة',
                      trip.busNumber,
                      ColorConstants.primary,
                    ),
                  ),

                  // Driver info
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      Icons.drive_eta_rounded,
                      'السائق',
                      trip.driverName,
                      ColorConstants.secondary,
                    ),
                  ),

                  // Supervisor info
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      Icons.supervisor_account_rounded,
                      'المشرف',
                      trip.supervisorName,
                      ColorConstants.accent1,
                    ),
                  ),
                ],
              ),
              SizedBox(height: SizeConstants.spaceBase),

              // Time and location
              Row(
                children: [
                  // Start time
                  Expanded(
                    child: _buildTimeItem(
                      context,
                      Icons.access_time_rounded,
                      'وقت البداية',
                      startTime,
                    ),
                  ),

                  // End time
                  Expanded(
                    child: _buildTimeItem(
                      context,
                      Icons.access_time_filled_rounded,
                      'وقت النهاية',
                      endTime,
                    ),
                  ),
                ],
              ),

              // View details button
              Align(
                alignment: Alignment.centerLeft,
                child: TextButton(
                  onPressed: onTap,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'عرض التفاصيل',
                        style: TextStyle(
                          fontSize:
                              ResponsiveUtils.getResponsiveFontSizeByDevice(
                                context,
                                mobile: 14,
                                tablet: 16,
                                desktop: 18,
                              ),
                          color: ColorConstants.primary,
                        ),
                      ),
                      SizedBox(width: SizeConstants.spaceXS),
                      Icon(
                        Icons.arrow_forward_ios_rounded,
                        size: ResponsiveUtils.getResponsiveIconSize(
                          context,
                          mobile: 14,
                          tablet: 16,
                          desktop: 18,
                        ),
                        color: ColorConstants.primary,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(
    BuildContext context,
    IconData icon,
    String title,
    String value,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(SizeConstants.spaceXS),
          decoration: BoxDecoration(
            color: color.withAlpha(26),
            borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
          ),
          child: Icon(
            icon,
            color: color,
            size: ResponsiveUtils.getResponsiveIconSize(
              context,
              mobile: 20,
              tablet: 24,
              desktop: 28,
            ),
          ),
        ),
        SizedBox(width: SizeConstants.spaceXS),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                    context,
                    mobile: 12,
                    tablet: 14,
                    desktop: 16,
                  ),
                  color: Colors.black54,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                    context,
                    mobile: 14,
                    tablet: 16,
                    desktop: 18,
                  ),
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTimeItem(
    BuildContext context,
    IconData icon,
    String title,
    String value,
  ) {
    return Row(
      children: [
        Icon(
          icon,
          color: ColorConstants.primary,
          size: ResponsiveUtils.getResponsiveIconSize(
            context,
            mobile: 20,
            tablet: 24,
            desktop: 28,
          ),
        ),
        SizedBox(width: SizeConstants.spaceXS),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                  context,
                  mobile: 12,
                  tablet: 14,
                  desktop: 16,
                ),
                color: Colors.black54,
              ),
            ),
            Text(
              value,
              style: TextStyle(
                fontSize: ResponsiveUtils.getResponsiveFontSizeByDevice(
                  context,
                  mobile: 14,
                  tablet: 16,
                  desktop: 18,
                ),
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'in_progress':
        return 'جارية';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      case 'scheduled':
        return 'مجدولة';
      default:
        return 'غير معروف';
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'in_progress':
        return ColorConstants.primary;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      case 'scheduled':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
}

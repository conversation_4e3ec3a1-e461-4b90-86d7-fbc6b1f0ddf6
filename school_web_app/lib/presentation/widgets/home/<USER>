import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart' as launcher;

import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/ad.dart';
import '../../../core/constants/size_constants.dart';

/// CarouselSliderWidget for displaying ads
/// Following Single Responsibility Principle by focusing only on carousel UI
class CarouselSliderWidget extends StatefulWidget {
  final List<Ad> ads;

  const CarouselSliderWidget({super.key, required this.ads});

  @override
  State<CarouselSliderWidget> createState() => _CarouselSliderWidgetState();
}

class _CarouselSliderWidgetState extends State<CarouselSliderWidget> {
  int _currentIndex = 0;
  final PageController _pageController = PageController();
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startAutoPlay();
  }

  @override
  void dispose() {
    _stopAutoPlay();
    _pageController.dispose();
    super.dispose();
  }

  void _startAutoPlay() {
    _timer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (_currentIndex < widget.ads.length - 1) {
        _pageController.animateToPage(
          _currentIndex + 1,
          duration: const Duration(milliseconds: 800),
          curve: Curves.fastOutSlowIn,
        );
      } else {
        _pageController.animateToPage(
          0,
          duration: const Duration(milliseconds: 800),
          curve: Curves.fastOutSlowIn,
        );
      }
    });
  }

  void _stopAutoPlay() {
    _timer?.cancel();
    _timer = null;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.ads.isEmpty) {
      return const SizedBox();
    }

    return Column(
      children: [
        // Carousel
        SizedBox(
          height: ResponsiveUtils.getResponsiveValue<double>(
            mobile: 180,
            tablet: 220,
            desktop: 250,
            context: context,
          ),
          child: PageView.builder(
            controller: _pageController,
            itemCount: widget.ads.length,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemBuilder: (context, index) {
              final ad = widget.ads[index];
              return GestureDetector(
                onTap: () => _openAdLink(ad.link),
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 5.0),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(26),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
                    child: Stack(
                      children: [
                        // Ad image with local placeholder
                        _buildAdImage(ad),

                        // Gradient overlay
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withAlpha(204),
                              ],
                            ),
                          ),
                        ),

                        // Ad title and description
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: Padding(
                            padding: EdgeInsets.all(SizeConstants.spaceBase),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  ad.title,
                                  style: TextStyle(
                                    fontSize:
                                        ResponsiveUtils.getResponsiveFontSizeByDevice(
                                          context,
                                          mobile: 16,
                                          tablet: 18,
                                          desktop: 20,
                                        ),
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                SizedBox(height: SizeConstants.spaceXS),
                                Text(
                                  ad.description,
                                  style: TextStyle(
                                    fontSize:
                                        ResponsiveUtils.getResponsiveFontSizeByDevice(
                                          context,
                                          mobile: 12,
                                          tablet: 14,
                                          desktop: 16,
                                        ),
                                    color: Colors.white.withAlpha(204),
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        // Indicators
        SizedBox(height: SizeConstants.spaceSM),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children:
              widget.ads.asMap().entries.map((entry) {
                return GestureDetector(
                  onTap: () {
                    _pageController.animateToPage(
                      entry.key,
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.easeInOut,
                    );
                  },
                  child: Container(
                    width: 8.0,
                    height: 8.0,
                    margin: const EdgeInsets.symmetric(horizontal: 4.0),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color:
                          _currentIndex == entry.key
                              ? ColorConstants.primary
                              : Colors.grey.withAlpha(77),
                    ),
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }

  Future<void> _openAdLink(String url) async {
    if (url.isEmpty) return;

    try {
      final Uri uri = Uri.parse(url);
      if (await launcher.canLaunchUrl(uri)) {
        await launcher.launchUrl(
          uri,
          mode: launcher.LaunchMode.externalApplication,
        );
      } else {
        Get.snackbar(
          'خطأ',
          'لا يمكن فتح الرابط',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء فتح الرابط',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Build ad image with proper placeholder handling
  Widget _buildAdImage(Ad ad) {
    // If imageUrl is empty or null, show local placeholder
    if (ad.imageUrl.isEmpty) {
      return _buildLocalPlaceholder(ad);
    }

    // Try to load network image with fallback
    return Image.network(
      ad.imageUrl,
      fit: BoxFit.cover,
      width: double.infinity,
      height: double.infinity,
      errorBuilder: (context, error, stackTrace) {
        return _buildLocalPlaceholder(ad);
      },
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return Container(
          color: Colors.grey[300],
          child: Center(
            child: CircularProgressIndicator(
              value: loadingProgress.expectedTotalBytes != null
                  ? loadingProgress.cumulativeBytesLoaded /
                      loadingProgress.expectedTotalBytes!
                  : null,
              color: ColorConstants.primary,
            ),
          ),
        );
      },
    );
  }

  /// Build local placeholder for ads
  Widget _buildLocalPlaceholder(Ad ad) {
    // Create different colored placeholders based on ad ID
    final colors = [
      ColorConstants.primary,
      const Color(0xFF2E8B57), // Sea Green
      const Color(0xFFFF7F50), // Coral
    ];

    final color = colors[(ad.id - 1) % colors.length];

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color,
            color.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getAdIcon(ad.id),
              size: 60,
              color: Colors.white,
            ),
            SizedBox(height: SizeConstants.spaceBase),
            Text(
              ad.title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: SizeConstants.fontSM,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: SizeConstants.spaceXS),
            Text(
              ad.description,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: SizeConstants.fontSM,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// Get appropriate icon for ad based on ID
  IconData _getAdIcon(int adId) {
    switch (adId) {
      case 1:
        return Icons.school_rounded;
      case 2:
        return Icons.location_on_rounded;
      case 3:
        return Icons.directions_bus_rounded;
      default:
        return Icons.info_rounded;
    }
  }
}

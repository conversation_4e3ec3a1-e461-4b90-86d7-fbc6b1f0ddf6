import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../controllers/theme_controller.dart';
import '../../../core/constants/size_constants.dart';

/// Enhanced SidebarMenuItem widget with modern design and animations
/// Following Single Responsibility Principle by focusing only on menu item UI
class SidebarMenuItem extends StatefulWidget {
  final IconData icon;
  final String title;
  final bool isActive;
  final bool isSelected;
  final VoidCallback onTap;
  final Color? textColor;
  final Color? iconColor;
  final Widget? trailing;

  const SidebarMenuItem({
    super.key,
    required this.icon,
    required this.title,
    required this.isActive,
    this.isSelected = false,
    required this.onTap,
    this.textColor,
    this.iconColor,
    this.trailing,
  });

  @override
  State<SidebarMenuItem> createState() => _SidebarMenuItemState();
}

class _SidebarMenuItemState extends State<SidebarMenuItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeController = Get.find<ThemeController>();

    return Obx(() {
      // Determine colors based on active state and theme
      final isDarkMode = themeController.isDarkMode;
      final activeColor = ColorConstants.primary;
      final inactiveTextColor =
          widget.textColor ??
          (isDarkMode
              ? ColorConstants.textPrimaryDark
              : ColorConstants.textPrimary);
      final inactiveIconColor =
          widget.iconColor ??
          (isDarkMode
              ? ColorConstants.textSecondaryDark
              : ColorConstants.textSecondary);

      return AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceSM, vertical: 4),
              child: MouseRegion(
                onEnter: (_) {
                  setState(() => _isHovered = true);
                  if (!widget.isActive) _animationController.forward();
                },
                onExit: (_) {
                  setState(() => _isHovered = false);
                  if (!widget.isActive) _animationController.reverse();
                },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  decoration: BoxDecoration(
                    gradient:
                        widget.isActive
                            ? LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: [
                                activeColor.withValues(alpha: 0.15),
                                activeColor.withValues(alpha: 0.08),
                              ],
                            )
                            : _isHovered
                            ? LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: [
                                activeColor.withValues(alpha: 0.08),
                                activeColor.withValues(alpha: 0.04),
                              ],
                            )
                            : null,
                    borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
                    border:
                        widget.isActive
                            ? Border.all(
                              color: activeColor.withValues(alpha: 0.3),
                              width: 1,
                            )
                            : _isHovered
                            ? Border.all(
                              color: activeColor.withValues(alpha: 0.2),
                              width: 1,
                            )
                            : null,
                    boxShadow:
                        widget.isActive
                            ? [
                              BoxShadow(
                                color: activeColor.withValues(alpha: 0.2),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ]
                            : null,
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: widget.onTap,
                      borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceBase,
                          vertical: 12,
                        ),
                        child: Row(
                          children: [
                            // Enhanced icon with background
                            Container(
                              padding: EdgeInsets.all(SizeConstants.spaceXS),
                              decoration: BoxDecoration(
                                color:
                                    widget.isActive
                                        ? activeColor.withValues(alpha: 0.2)
                                        : _isHovered
                                        ? activeColor.withValues(alpha: 0.1)
                                        : Colors.transparent,
                                borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                              ),
                              child: Icon(
                                widget.icon,
                                color:
                                    widget.isActive
                                        ? activeColor
                                        : _isHovered
                                        ? activeColor
                                        : inactiveIconColor,
                                size: 18,
                              ),
                            ),
                            SizedBox(width: SizeConstants.spaceSM),

                            // Enhanced title
                            Expanded(
                              child: Text(
                                widget.title,
                                style: TextStyle(
                                  color:
                                      widget.isActive
                                          ? activeColor
                                          : _isHovered
                                          ? activeColor
                                          : inactiveTextColor,
                                  fontWeight:
                                      widget.isActive
                                          ? FontWeight.w700
                                          : _isHovered
                                          ? FontWeight.w600
                                          : FontWeight.w500,
                                  fontSize: 13,
                                  letterSpacing: 0.2,
                                ),
                              ),
                            ),

                            // Trailing widget or active indicator
                            if (widget.trailing != null)
                              widget.trailing!
                            else if (widget.isActive)
                              Container(
                                width: 6,
                                height: 6,
                                decoration: BoxDecoration(
                                  color: activeColor,
                                  borderRadius: BorderRadius.circular(3),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      );
    });
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../domain/entities/notification.dart';
import '../../controllers/notification_controller.dart';
import '../../../core/constants/size_constants.dart';

/// Card widget for displaying notification
class NotificationCard extends StatelessWidget {
  final NotificationEntity notification;
  final VoidCallback onTap;
  final VoidCallback onMarkAsRead;
  final VoidCallback onDelete;

  const NotificationCard({
    super.key,
    required this.notification,
    required this.onTap,
    required this.onMarkAsRead,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<NotificationController>();
    final isUnread = notification.isRead != true;

    return Card(
      elevation: isUnread ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        side:
            isUnread
                ? BorderSide(
                  color: ColorConstants.primary.withValues(alpha: 0.3),
                )
                : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        child: Container(
          padding: EdgeInsets.all(SizeConstants.spaceBase),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            color:
                isUnread
                    ? ColorConstants.primary.withValues(alpha: 0.05)
                    : null,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with icon, title, and actions
              Row(
                children: [
                  // Notification icon
                  Container(
                    width: 40,
                    height: SizeConstants.buttonHeightMD,
                    decoration: BoxDecoration(
                      color: controller
                          .getNotificationColor(notification.type)
                          .withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(SizeConstants.radiusXL),
                    ),
                    child: Icon(
                      controller.getNotificationIcon(notification.type),
                      color: controller.getNotificationColor(notification.type),
                      size: SizeConstants.iconSM,
                    ),
                  ),
                  SizedBox(width: SizeConstants.spaceSM),
                  // Title and time
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          notification.title ?? 'إشعار',
                          style: TextStyle(
                            fontSize: SizeConstants.fontBase,
                            fontWeight:
                                isUnread ? FontWeight.bold : FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 2),
                        Text(
                          controller.formatNotificationDate(
                            notification.createdAt,
                          ),
                          style: TextStyle(
                            fontSize: SizeConstants.fontXS,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Unread indicator
                  if (isUnread)
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: ColorConstants.primary,
                        borderRadius: BorderRadius.circular(SizeConstants.radiusXS),
                      ),
                    ),
                  SizedBox(width: SizeConstants.spaceXS),
                  // Actions menu
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'mark_read':
                          onMarkAsRead();
                          break;
                        case 'delete':
                          onDelete();
                          break;
                      }
                    },
                    itemBuilder:
                        (context) => [
                          if (isUnread)
                            const PopupMenuItem(
                              value: 'mark_read',
                              child: Row(
                                children: [
                                  Icon(Icons.done, size: SizeConstants.iconXS),
                                  SizedBox(width: SizeConstants.spaceXS),
                                  Text('تحديد كمقروء'),
                                ],
                              ),
                            ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, size: SizeConstants.iconXS, color: Colors.red),
                                SizedBox(width: SizeConstants.spaceXS),
                                Text(
                                  'حذف',
                                  style: TextStyle(color: Colors.red),
                                ),
                              ],
                            ),
                          ),
                        ],
                    child: Icon(
                      Icons.more_vert,
                      color: Colors.grey[600],
                      size: SizeConstants.iconSM,
                    ),
                  ),
                ],
              ),
              SizedBox(height: SizeConstants.spaceSM),
              // Notification body
              if (notification.body != null && notification.body!.isNotEmpty)
                Text(
                  notification.body!,
                  style: TextStyle(
                    fontSize: SizeConstants.fontSM,
                    color:
                        Theme.of(context).brightness == Brightness.dark
                            ? ColorConstants.textSecondaryDark
                            : ColorConstants.textSecondary,
                    height: 1.4,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              // Notification image
              if (notification.image != null && notification.image!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                    child: Image.network(
                      notification.image!,
                      height: 120,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          height: 120,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                          ),
                          child: const Icon(
                            Icons.image_not_supported,
                            color: Colors.grey,
                          ),
                        );
                      },
                    ),
                  ),
                ),
              // Recent indicator
              if (controller.isRecentNotification(notification.createdAt))
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceXS,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
                      border: Border.all(
                        color: Colors.orange.withValues(alpha: 0.3),
                      ),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.fiber_new, size: 14, color: Colors.orange),
                        SizedBox(width: SizeConstants.spaceXS),
                        Text(
                          'جديد',
                          style: TextStyle(
                            fontSize: SizeConstants.fontXS,
                            color: Colors.orange,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

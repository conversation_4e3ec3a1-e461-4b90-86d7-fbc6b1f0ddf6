import 'package:flutter/material.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/constants/size_constants.dart';

/// Widget for notification filters
class NotificationFilters extends StatelessWidget {
  final String selectedFilter;
  final Function(String) onFilterChanged;

  const NotificationFilters({
    super.key,
    required this.selectedFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    final filters = [
      {'key': 'all', 'label': 'الكل', 'icon': Icons.notifications},
      {'key': 'unread', 'label': 'غير مقروء', 'icon': Icons.mark_email_unread},
      {'key': 'read', 'label': 'مقروء', 'icon': Icons.mark_email_read},
      {'key': 'trip_started', 'label': 'بدء الرحلة', 'icon': Icons.play_arrow},
      {'key': 'trip_ended', 'label': 'انتهاء الرحلة', 'icon': Icons.stop},
      {
        'key': 'student_picked_up',
        'label': 'استلام طالب',
        'icon': Icons.person_add,
      },
      {
        'key': 'student_dropped_off',
        'label': 'توصيل طالب',
        'icon': Icons.person_remove,
      },
      {'key': 'bus_breakdown', 'label': 'عطل حافلة', 'icon': Icons.warning},
      {'key': 'route_changed', 'label': 'تغيير مسار', 'icon': Icons.route},
    ];

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children:
            filters.map((filter) {
              final isSelected = selectedFilter == filter['key'];

              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: FilterChip(
                  label: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        filter['icon'] as IconData,
                        size: SizeConstants.iconXS,
                        color:
                            isSelected ? Colors.white : ColorConstants.primary,
                      ),
                      SizedBox(width: SizeConstants.spaceXS),
                      Flexible(
                        child: Text(
                          filter['label'] as String,
                          style: TextStyle(
                            color:
                                isSelected
                                    ? Colors.white
                                    : ColorConstants.primary,
                            fontWeight:
                                isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                          ),
                        ),
                      ),
                    ],
                  ),
                  selected: isSelected,
                  onSelected: (selected) {
                    onFilterChanged(filter['key'] as String);
                  },
                  backgroundColor: Colors.transparent,
                  selectedColor: ColorConstants.primary,
                  checkmarkColor: Colors.white,
                  side: BorderSide(
                    color:
                        isSelected
                            ? ColorConstants.primary
                            : ColorConstants.primary.withValues(alpha: 0.3),
                  ),
                ),
              );
            }).toList(),
      ),
    );
  }
}

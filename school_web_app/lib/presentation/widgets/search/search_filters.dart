import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../controllers/search_controller.dart' as app_search;
import '../../../core/constants/size_constants.dart';

/// Widget for displaying active search filters
class SearchFilters extends StatelessWidget {
  final Map<String, dynamic> filters;
  final Function(String) onFilterRemoved;
  final VoidCallback onClearAll;

  const SearchFilters({
    super.key,
    required this.filters,
    required this.onFilterRemoved,
    required this.onClearAll,
  });

  @override
  Widget build(BuildContext context) {
    if (filters.isEmpty) return const SizedBox.shrink();

    final searchController = Get.find<app_search.SearchController>();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'الفلاتر النشطة:',
              style: TextStyle(
                fontSize: SizeConstants.fontSM,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const Spacer(),
            TextButton(
              onPressed: onClearAll,
              child: const Text('مسح الكل', style: TextStyle(fontSize: SizeConstants.fontXS)),
            ),
          ],
        ),
        SizedBox(height: SizeConstants.spaceXS),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children:
              filters.entries.map((entry) {
                return Chip(
                  label: Text(
                    '${searchController.getFilterDisplayName(entry.key)}: ${searchController.formatFilterValue(entry.value)}',
                    style: const TextStyle(fontSize: SizeConstants.fontXS),
                  ),
                  deleteIcon: const Icon(Icons.close, size: SizeConstants.iconXS),
                  onDeleted: () => onFilterRemoved(entry.key),
                  backgroundColor: ColorConstants.primary.withValues(
                    alpha: 0.1,
                  ),
                  deleteIconColor: ColorConstants.primary,
                  side: BorderSide(
                    color: ColorConstants.primary.withValues(alpha: 0.3),
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }
}

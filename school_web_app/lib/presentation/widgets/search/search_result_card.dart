import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/search_service.dart';
import '../../controllers/search_controller.dart' as app_search;
import '../../../core/constants/size_constants.dart';

/// Card widget for displaying search results
class SearchResultCard extends StatelessWidget {
  final SearchResult result;
  final VoidCallback onTap;

  const SearchResultCard({
    super.key,
    required this.result,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final searchController = Get.find<app_search.SearchController>();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(SizeConstants.radiusMD)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        child: Padding(
          padding: EdgeInsets.all(SizeConstants.spaceBase),
          child: Row(
            children: [
              // Category icon
              Container(
                width: 48,
                height: SizeConstants.buttonHeightLG,
                decoration: BoxDecoration(
                  color: searchController
                      .getCategoryColor(result.category)
                      .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(SizeConstants.radius2XL),
                ),
                child: Icon(
                  searchController.getCategoryIcon(result.category),
                  color: searchController.getCategoryColor(result.category),
                  size: SizeConstants.iconMD,
                ),
              ),
              SizedBox(width: SizeConstants.spaceBase),
              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      result.title,
                      style: const TextStyle(
                        fontSize: SizeConstants.fontBase,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: SizeConstants.spaceXS),
                    // Subtitle
                    Text(
                      result.subtitle,
                      style: TextStyle(fontSize: SizeConstants.fontSM, color: Colors.grey[600]),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: SizeConstants.spaceXS),
                    // Category badge
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceXS,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: searchController
                            .getCategoryColor(result.category)
                            .withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
                        border: Border.all(
                          color: searchController
                              .getCategoryColor(result.category)
                              .withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        result.category.displayName,
                        style: TextStyle(
                          fontSize: SizeConstants.fontXS,
                          color: searchController.getCategoryColor(
                            result.category,
                          ),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Relevance indicator
              Column(
                children: [
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey[400],
                    size: SizeConstants.iconXS,
                  ),
                  SizedBox(height: SizeConstants.spaceXS),
                  // Relevance score
                  Container(
                    width: 40,
                    height: 6,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(3),
                    ),
                    child: FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: result.relevance,
                      child: Container(
                        decoration: BoxDecoration(
                          color: _getRelevanceColor(result.relevance),
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Get color based on relevance score
  Color _getRelevanceColor(double relevance) {
    if (relevance >= 0.8) return Colors.green;
    if (relevance >= 0.6) return Colors.orange;
    return Colors.red;
  }
}

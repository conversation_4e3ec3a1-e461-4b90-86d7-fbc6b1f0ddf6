import 'package:flutter/material.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/constants/size_constants.dart';

/// Widget for displaying search suggestions
class SearchSuggestions extends StatelessWidget {
  final List<String> suggestions;
  final Function(String) onSuggestionSelected;

  const SearchSuggestions({
    super.key,
    required this.suggestions,
    required this.onSuggestionSelected,
  });

  @override
  Widget build(BuildContext context) {
    if (suggestions.isEmpty) return const SizedBox.shrink();

    return Material(
      elevation: 8,
      borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
      child: Container(
        constraints: const BoxConstraints(maxHeight: 200),
        decoration: BoxDecoration(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? ColorConstants.cardDark
                  : Colors.white,
          borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
          border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        ),
        child: ListView.separated(
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          itemCount: suggestions.length,
          separatorBuilder:
              (context, index) =>
                  Divider(height: 1, color: Colors.grey.withValues(alpha: 0.2)),
          itemBuilder: (context, index) {
            final suggestion = suggestions[index];
            return ListTile(
              dense: true,
              leading: Icon(Icons.search, color: Colors.grey[600], size: SizeConstants.iconSM),
              title: Text(suggestion, style: const TextStyle(fontSize: SizeConstants.fontSM)),
              onTap: () => onSuggestionSelected(suggestion),
              hoverColor: ColorConstants.primary.withValues(alpha: 0.1),
            );
          },
        ),
      ),
    );
  }
}

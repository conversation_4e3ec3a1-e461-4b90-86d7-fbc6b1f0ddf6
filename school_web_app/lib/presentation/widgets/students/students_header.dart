import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/students_controller.dart';
import '../../routes/app_routes.dart';
import '../../../core/constants/size_constants.dart';

/// StudentsHeader widget for students page header
/// Following Single Responsibility Principle by focusing only on header UI
class StudentsHeader extends StatelessWidget {
  const StudentsHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    return Container(
      padding: EdgeInsets.all(isDesktop ? 32 : 24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            ColorConstants.primary,
            ColorConstants.primary.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
        boxShadow: [
          BoxShadow(
            color: ColorConstants.primary.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(isDesktop ? 20 : 16),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Icon(
                  Icons.school_rounded,
                  color: Colors.white,
                  size: isDesktop ? 40 : 32,
                ),
              ),
              SizedBox(width: isDesktop ? 32 : 24),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'students'.tr,
                      style: TextStyle(
                        fontSize: isDesktop ? 32 : 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: isDesktop ? 12 : 8),
                    Text(
                      'Manage student information, enrollment, and bus assignments',
                      style: TextStyle(
                        fontSize: isDesktop ? 18 : 16,
                        color: Colors.white.withValues(alpha: 0.9),
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
              if (isTablet || isDesktop) ...[
                SizedBox(width: SizeConstants.spaceLG),
                _buildActionButtons(context, isDesktop),
              ],
            ],
          ),
          if (!isTablet && !isDesktop) ...[
            SizedBox(height: SizeConstants.spaceLG),
            _buildActionButtons(context, false),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, bool isDesktop) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildActionButton(
          context: context,
          icon: Icons.person_add_rounded,
          label: 'addStudent'.tr,
          onTap: () => Get.toNamed(AppRoutes.addStudent),
          isDesktop: isDesktop,
        ),
        SizedBox(width: isDesktop ? 16 : 12),
        _buildActionButton(
          context: context,
          icon: Icons.upload_file_rounded,
          label: 'Import Excel',
          onTap: () => _showImportDialog(context),
          isDesktop: isDesktop,
          isSecondary: true,
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required bool isDesktop,
    bool isSecondary = false,
  }) {
    return ElevatedButton.icon(
      onPressed: onTap,
      icon: Icon(
        icon,
        size: isDesktop ? 20 : 18,
        color: isSecondary ? ColorConstants.primary : Colors.white,
      ),
      label: Text(
        label,
        style: TextStyle(
          fontSize: isDesktop ? 16 : 14,
          fontWeight: FontWeight.w600,
          color: isSecondary ? ColorConstants.primary : Colors.white,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: isSecondary ? Colors.white : Colors.white.withValues(alpha: 0.2),
        foregroundColor: isSecondary ? ColorConstants.primary : Colors.white,
        elevation: isSecondary ? 2 : 0,
        padding: EdgeInsets.symmetric(
          horizontal: isDesktop ? 24 : 20,
          vertical: isDesktop ? 16 : 14,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
          side: BorderSide(
            color: isSecondary ? Colors.transparent : Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
    );
  }

  void _showImportDialog(BuildContext context) {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
        ),
        child: Container(
          padding: EdgeInsets.all(SizeConstants.spaceLG),
          constraints: const BoxConstraints(maxWidth: 500),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(SizeConstants.spaceSM),
                    decoration: BoxDecoration(
                      color: ColorConstants.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
                    ),
                    child: Icon(
                      Icons.upload_file_rounded,
                      color: ColorConstants.primary,
                      size: SizeConstants.iconMD,
                    ),
                  ),
                  SizedBox(width: SizeConstants.spaceBase),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Import Students',
                          style: TextStyle(
                            fontSize: SizeConstants.fontSM,
                            fontWeight: FontWeight.bold,
                            color: ColorConstants.primary,
                          ),
                        ),
                        SizedBox(height: SizeConstants.spaceXS),
                        Text(
                          'Upload an Excel file to import multiple students',
                          style: TextStyle(
                            fontSize: SizeConstants.fontSM,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close_rounded),
                  ),
                ],
              ),

              SizedBox(height: SizeConstants.spaceLG),

              // Upload Area
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(SizeConstants.spaceXL),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Colors.grey.shade300,
                    style: BorderStyle.solid,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
                  color: Colors.grey.shade50,
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.cloud_upload_rounded,
                      size: SizeConstants.iconXL,
                      color: Colors.grey.shade400,
                    ),
                    SizedBox(height: SizeConstants.spaceBase),
                    Text(
                      'Drag and drop your Excel file here',
                      style: TextStyle(
                        fontSize: SizeConstants.fontBase,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey.shade700,
                      ),
                    ),
                    SizedBox(height: SizeConstants.spaceXS),
                    Text(
                      'or click to browse files',
                      style: TextStyle(
                        fontSize: SizeConstants.fontSM,
                        color: Colors.grey.shade500,
                      ),
                    ),
                    SizedBox(height: SizeConstants.spaceBase),
                    ElevatedButton.icon(
                      onPressed: () {
                        final controller = Get.find<StudentsController>();
                        Get.back(); // Close dialog first
                        controller.importFromExcel();
                      },
                      icon: const Icon(Icons.folder_open_rounded),
                      label: const Text('Browse Files'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorConstants.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceLG,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: SizeConstants.spaceBase),

              // Download Template
              Row(
                children: [
                  Icon(
                    Icons.info_outline_rounded,
                    size: SizeConstants.iconXS,
                    color: Colors.blue.shade600,
                  ),
                  SizedBox(width: SizeConstants.spaceXS),
                  Expanded(
                    child: Text(
                      'Need a template? Download the sample Excel file',
                      style: TextStyle(
                        fontSize: SizeConstants.fontXS,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      final controller = Get.find<StudentsController>();
                      controller.downloadTemplate();
                    },
                    child: const Text('Download Template'),
                  ),
                ],
              ),

              SizedBox(height: SizeConstants.spaceLG),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('Cancel'),
                    ),
                  ),
                  SizedBox(width: SizeConstants.spaceBase),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        final controller = Get.find<StudentsController>();
                        Get.back(); // Close dialog first
                        controller.importFromExcel();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorConstants.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: SizeConstants.spaceSM),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                        ),
                      ),
                      child: const Text('Import Students'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

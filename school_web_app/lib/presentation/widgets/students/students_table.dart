import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/student.dart';
import '../../routes/app_routes.dart';
import '../../../core/constants/size_constants.dart';

/// Students table
class StudentsTable extends StatelessWidget {
  final List<Student> students;
  final bool isLoading;
  final Function(Student) onRowTap;
  final VoidCallback onLoadMore;

  const StudentsTable({
    super.key,
    required this.students,
    required this.isLoading,
    required this.onRowTap,
    required this.onLoadMore,
  });

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);

    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        if (scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent) {
          onLoadMore();
        }
        return true;
      },
      child:
          isDesktop ? _buildDesktopTable(context) : _buildMobileTable(context),
    );
  }

  Widget _buildDesktopTable(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Enhanced table header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceLG, vertical: 20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  ColorConstants.primary.withValues(alpha: 0.1),
                  ColorConstants.primary.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: _buildHeaderCell('Student Information'),
                ),
                Expanded(
                  flex: 2,
                  child: _buildHeaderCell('Grade'),
                ),
                Expanded(
                  flex: 2,
                  child: _buildHeaderCell('Bus Assignment'),
                ),
                Expanded(
                  flex: 2,
                  child: _buildHeaderCell('Contact'),
                ),
                Expanded(
                  flex: 1,
                  child: _buildHeaderCell('Actions'),
                ),
              ],
            ),
          ),

          // Enhanced table body
          Expanded(
            child: ListView.separated(
              padding: const EdgeInsets.all(0),
              itemCount: students.length + (isLoading ? 1 : 0),
              separatorBuilder: (context, index) => Divider(
                height: 1,
                color: Colors.grey.withValues(alpha: 0.2),
                indent: 24,
                endIndent: 24,
              ),
              itemBuilder: (context, index) {
                if (index == students.length) {
                  return const Padding(
                    padding: EdgeInsets.all(32.0),
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                final student = students[index];
                return _buildEnhancedDesktopRow(context, student, index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: SizeConstants.fontSM,
        fontWeight: FontWeight.w600,
        color: ColorConstants.primary,
        letterSpacing: 0.5,
      ),
    );
  }

  Widget _buildEnhancedDesktopRow(BuildContext context, Student student, int index) {
    return InkWell(
      onTap: () => onRowTap(student),
      hoverColor: ColorConstants.primary.withValues(alpha: 0.05),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceLG, vertical: 20),
        child: Row(
          children: [
            // Student Information Column
            Expanded(
              flex: 3,
              child: Row(
                children: [
                  // Student Avatar
                  Container(
                    width: 48,
                    height: SizeConstants.buttonHeightLG,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          ColorConstants.primary,
                          ColorConstants.primary.withValues(alpha: 0.8),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
                    ),
                    child: Center(
                      child: Text(
                        (student.name?.isNotEmpty == true)
                            ? student.name![0].toUpperCase()
                            : 'S',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: SizeConstants.fontSM,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: SizeConstants.spaceBase),
                  // Student Details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          student.name ?? 'Unknown Student',
                          style: const TextStyle(
                            fontSize: SizeConstants.fontBase,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        SizedBox(height: SizeConstants.spaceXS),
                        Text(
                          'ID: ${student.id ?? 'N/A'}',
                          style: TextStyle(
                            fontSize: SizeConstants.fontXS,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Grade Column
            Expanded(
              flex: 2,
              child: _buildStatusChip(
                student.grade?.name ?? 'Not Assigned',
                student.grade != null ? Colors.blue : Colors.grey,
                Icons.school_rounded,
              ),
            ),

            // Bus Assignment Column
            Expanded(
              flex: 2,
              child: _buildStatusChip(
                student.bus?.name ?? 'Not Assigned',
                student.bus != null ? Colors.green : Colors.orange,
                Icons.directions_bus_rounded,
              ),
            ),

            // Contact Column
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (student.phone?.isNotEmpty == true) ...[
                    Row(
                      children: [
                        Icon(
                          Icons.phone_rounded,
                          size: SizeConstants.iconXS,
                          color: Colors.grey.shade600,
                        ),
                        SizedBox(width: SizeConstants.spaceXS),
                        Expanded(
                          child: Text(
                            student.phone!,
                            style: const TextStyle(
                              fontSize: SizeConstants.fontSM,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ] else ...[
                    Text(
                      'No contact',
                      style: TextStyle(
                        fontSize: SizeConstants.fontSM,
                        color: Colors.grey.shade500,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // Actions Column
            Expanded(
              flex: 1,
              child: _buildEnhancedActions(context, student),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String text, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceSM, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(SizeConstants.radiusXL),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: SizeConstants.iconXS,
            color: color,
          ),
          SizedBox(width: SizeConstants.spaceXS),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: SizeConstants.fontXS,
                fontWeight: FontWeight.w500,
                color: color,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedActions(BuildContext context, Student student) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildActionButton(
          icon: Icons.visibility_rounded,
          color: Colors.blue,
          onTap: () => onRowTap(student),
          tooltip: 'View Details',
        ),
        SizedBox(width: SizeConstants.spaceXS),
        _buildActionButton(
          icon: Icons.edit_rounded,
          color: Colors.orange,
          onTap: () {
            Get.toNamed(AppRoutes.editStudent, arguments: student);
          },
          tooltip: 'Edit Student',
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required String tooltip,
  }) {
    return Container(
      width: 32,
      height: SizeConstants.buttonHeightSM,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: IconButton(
        icon: Icon(
          icon,
          color: color,
          size: SizeConstants.iconXS,
        ),
        onPressed: onTap,
        tooltip: tooltip,
        padding: EdgeInsets.zero,
        constraints: const BoxConstraints(),
      ),
    );
  }

  Widget _buildMobileTable(BuildContext context) {
    return ListView.builder(
      itemCount: students.length + (isLoading ? 1 : 0),
      padding: const EdgeInsets.symmetric(vertical: SizeConstants.spaceXS),
      itemBuilder: (context, index) {
        if (index == students.length) {
          return const Padding(
            padding: EdgeInsets.all(32.0),
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final student = students[index];
        return _buildEnhancedMobileCard(context, student, index);
      },
    );
  }

  Widget _buildEnhancedMobileCard(BuildContext context, Student student, int index) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceBase, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => onRowTap(student),
        borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with student info and avatar
              Row(
                children: [
                  // Student Avatar
                  Container(
                    width: 56,
                    height: SizeConstants.buttonHeightXL,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          ColorConstants.primary,
                          ColorConstants.primary.withValues(alpha: 0.8),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
                    ),
                    child: Center(
                      child: Text(
                        (student.name?.isNotEmpty == true)
                            ? student.name![0].toUpperCase()
                            : 'S',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: SizeConstants.fontBase,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: SizeConstants.spaceBase),
                  // Student Details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          student.name ?? 'Unknown Student',
                          style: const TextStyle(
                            fontSize: SizeConstants.fontSM,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        SizedBox(height: SizeConstants.spaceXS),
                        Text(
                          'ID: ${student.id ?? 'N/A'}',
                          style: TextStyle(
                            fontSize: SizeConstants.fontSM,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Actions
                  _buildEnhancedActions(context, student),
                ],
              ),

              SizedBox(height: SizeConstants.spaceMD),

              // Status chips row
              Row(
                children: [
                  Expanded(
                    child: _buildStatusChip(
                      student.grade?.name ?? 'Not Assigned',
                      student.grade != null ? Colors.blue : Colors.grey,
                      Icons.school_rounded,
                    ),
                  ),
                  SizedBox(width: SizeConstants.spaceSM),
                  Expanded(
                    child: _buildStatusChip(
                      student.bus?.name ?? 'Not Assigned',
                      student.bus != null ? Colors.green : Colors.orange,
                      Icons.directions_bus_rounded,
                    ),
                  ),
                ],
              ),

              SizedBox(height: SizeConstants.spaceBase),

              // Contact information
              if (student.phone?.isNotEmpty == true) ...[
                _buildEnhancedInfoRow(
                  Icons.phone_rounded,
                  'Phone',
                  student.phone!,
                  Colors.blue,
                ),
              ] else ...[
                _buildEnhancedInfoRow(
                  Icons.phone_disabled_rounded,
                  'Phone',
                  'No contact information',
                  Colors.grey,
                ),
              ],

              if (student.address?.isNotEmpty == true) ...[
                SizedBox(height: SizeConstants.spaceSM),
                _buildEnhancedInfoRow(
                  Icons.location_on_rounded,
                  'Address',
                  student.address!,
                  Colors.green,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedInfoRow(IconData icon, String label, String value, Color color) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(SizeConstants.spaceXS),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
          ),
          child: Icon(
            icon,
            size: SizeConstants.iconXS,
            color: color,
          ),
        ),
        SizedBox(width: SizeConstants.spaceSM),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: SizeConstants.fontXS,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(
                  fontSize: SizeConstants.fontSM,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

}

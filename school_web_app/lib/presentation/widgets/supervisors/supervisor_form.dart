import 'package:flutter/material.dart';
import '../../../domain/entities/supervisor.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import 'package:get/get.dart';
import '../../../core/constants/size_constants.dart';

/// Supervisor form widget with responsive design
/// Following Single Responsibility Principle by focusing only on supervisor form
class SupervisorForm extends StatefulWidget {
  final Supervisor? supervisor;
  final List<Map<String, dynamic>> availableBuses;
  final List<Map<String, dynamic>> genderOptions;
  final List<Map<String, dynamic>> religionOptions;
  final Function(Supervisor) onSubmit;
  final bool isLoading;

  const SupervisorForm({
    super.key,
    this.supervisor,
    required this.availableBuses,
    required this.genderOptions,
    required this.religionOptions,
    required this.onSubmit,
    required this.isLoading,
  });

  @override
  State<SupervisorForm> createState() => _SupervisorFormState();
}

class _SupervisorFormState extends State<SupervisorForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();

  DateTime? _birthDate;
  DateTime? _joiningDate;
  int? _selectedGenderId;
  int? _selectedReligionId;
  int? _selectedBusId;
  int _status = 1;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.supervisor != null) {
      final supervisor = widget.supervisor!;
      _nameController.text = supervisor.name ?? '';
      _usernameController.text = supervisor.username ?? '';
      _emailController.text = supervisor.email ?? '';
      _phoneController.text = supervisor.phone ?? '';
      _addressController.text = supervisor.address ?? '';
      _cityController.text = supervisor.cityName ?? '';
      
      if (supervisor.birthDate != null) {
        _birthDate = DateTime.tryParse(supervisor.birthDate!);
      }
      if (supervisor.joiningDate != null) {
        _joiningDate = DateTime.tryParse(supervisor.joiningDate!);
      }
      
      _selectedGenderId = supervisor.genderId;
      _selectedReligionId = supervisor.religionId;
      _selectedBusId = supervisor.busId;
      _status = supervisor.status ?? 1;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _usernameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final isMobile = deviceType == DeviceScreenType.mobile;

    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(SizeConstants.spaceLG),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('المعلومات الأساسية'),
            SizedBox(height: SizeConstants.spaceBase),
            _buildBasicInfoSection(isMobile),
            
            SizedBox(height: SizeConstants.spaceXL),
            _buildSectionTitle('معلومات الاتصال'),
            SizedBox(height: SizeConstants.spaceBase),
            _buildContactInfoSection(isMobile),
            
            SizedBox(height: SizeConstants.spaceXL),
            _buildSectionTitle('المعلومات الإضافية'),
            SizedBox(height: SizeConstants.spaceBase),
            _buildAdditionalInfoSection(isMobile),
            
            SizedBox(height: SizeConstants.spaceXL),
            _buildSectionTitle('تخصيص الحافلة'),
            SizedBox(height: SizeConstants.spaceBase),
            _buildBusAssignmentSection(isMobile),
            
            SizedBox(height: SizeConstants.spaceXL),
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
        fontWeight: FontWeight.bold,
        color: Theme.of(context).brightness == Brightness.dark
            ? ColorConstants.textPrimaryDark
            : ColorConstants.textPrimary,
      ),
    );
  }

  Widget _buildBasicInfoSection(bool isMobile) {
    return Column(
      children: [
        TextFormField(
          controller: _nameController,
          decoration: InputDecoration(
            labelText: 'الاسم الكامل',
            hintText: 'أدخل الاسم الكامل',
            prefixIcon: const Icon(Icons.person),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'الاسم مطلوب';
            }
            return null;
          },
        ),
        SizedBox(height: SizeConstants.spaceBase),
        TextFormField(
          controller: _usernameController,
          decoration: InputDecoration(
            labelText: 'اسم المستخدم',
            hintText: 'أدخل اسم المستخدم',
            prefixIcon: const Icon(Icons.account_circle),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'اسم المستخدم مطلوب';
            }
            return null;
          },
        ),
        SizedBox(height: SizeConstants.spaceBase),
        DropdownButtonFormField<int>(
          value: _selectedGenderId,
          decoration: InputDecoration(
            labelText: 'الجنس',
            hintText: 'اختر الجنس',
            prefixIcon: const Icon(Icons.wc),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            ),
          ),
          items: widget.genderOptions.map((gender) {
            return DropdownMenuItem<int>(
              value: gender['id'] as int,
              child: Text(gender['name'] as String),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedGenderId = value;
            });
          },
          validator: (value) {
            if (value == null) {
              return 'الجنس مطلوب';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildContactInfoSection(bool isMobile) {
    return Column(
      children: [
        TextFormField(
          controller: _emailController,
          decoration: InputDecoration(
            labelText: 'البريد الإلكتروني',
            hintText: 'أدخل البريد الإلكتروني',
            prefixIcon: const Icon(Icons.email),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            ),
          ),
          keyboardType: TextInputType.emailAddress,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'البريد الإلكتروني مطلوب';
            }
            if (!GetUtils.isEmail(value)) {
              return 'البريد الإلكتروني غير صحيح';
            }
            return null;
          },
        ),
        SizedBox(height: SizeConstants.spaceBase),
        TextFormField(
          controller: _phoneController,
          decoration: InputDecoration(
            labelText: 'رقم الهاتف',
            hintText: 'أدخل رقم الهاتف',
            prefixIcon: const Icon(Icons.phone),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            ),
          ),
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'رقم الهاتف مطلوب';
            }
            return null;
          },
        ),
        SizedBox(height: SizeConstants.spaceBase),
        TextFormField(
          controller: _addressController,
          decoration: InputDecoration(
            labelText: 'العنوان',
            hintText: 'أدخل العنوان',
            prefixIcon: const Icon(Icons.location_on),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            ),
          ),
          maxLines: 2,
        ),
        SizedBox(height: SizeConstants.spaceBase),
        TextFormField(
          controller: _cityController,
          decoration: InputDecoration(
            labelText: 'المدينة',
            hintText: 'أدخل المدينة',
            prefixIcon: const Icon(Icons.location_city),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAdditionalInfoSection(bool isMobile) {
    return Column(
      children: [
        // Birth Date
        InkWell(
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: _birthDate ?? DateTime.now(),
              firstDate: DateTime(1950),
              lastDate: DateTime.now(),
            );
            if (date != null) {
              setState(() {
                _birthDate = date;
              });
            }
          },
          child: InputDecorator(
            decoration: InputDecoration(
              labelText: 'تاريخ الميلاد',
              hintText: 'اختر تاريخ الميلاد',
              prefixIcon: const Icon(Icons.cake),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              ),
            ),
            child: Text(
              _birthDate != null
                  ? '${_birthDate!.day}/${_birthDate!.month}/${_birthDate!.year}'
                  : 'اختر تاريخ الميلاد',
            ),
          ),
        ),
        SizedBox(height: SizeConstants.spaceBase),

        // Joining Date
        InkWell(
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: _joiningDate ?? DateTime.now(),
              firstDate: DateTime(2000),
              lastDate: DateTime.now().add(const Duration(days: 365)),
            );
            if (date != null) {
              setState(() {
                _joiningDate = date;
              });
            }
          },
          child: InputDecorator(
            decoration: InputDecoration(
              labelText: 'تاريخ الانضمام',
              hintText: 'اختر تاريخ الانضمام',
              prefixIcon: const Icon(Icons.work),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
              ),
            ),
            child: Text(
              _joiningDate != null
                  ? '${_joiningDate!.day}/${_joiningDate!.month}/${_joiningDate!.year}'
                  : 'اختر تاريخ الانضمام',
            ),
          ),
        ),
        SizedBox(height: SizeConstants.spaceBase),

        // Religion
        DropdownButtonFormField<int>(
          value: _selectedReligionId,
          decoration: InputDecoration(
            labelText: 'الديانة',
            hintText: 'اختر الديانة',
            prefixIcon: const Icon(Icons.mosque),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            ),
          ),
          items: widget.religionOptions.map((religion) {
            return DropdownMenuItem<int>(
              value: religion['id'] as int,
              child: Text(religion['name'] as String),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedReligionId = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildBusAssignmentSection(bool isMobile) {
    return Column(
      children: [
        DropdownButtonFormField<int>(
          value: _selectedBusId,
          decoration: InputDecoration(
            labelText: 'الحافلة المخصصة',
            hintText: 'اختر الحافلة',
            prefixIcon: const Icon(Icons.directions_bus),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
            ),
            filled: true,
            fillColor: Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.surfaceDark
                : ColorConstants.surface,
          ),
          items: widget.availableBuses.map((bus) {
            return DropdownMenuItem<int>(
              value: bus['id'] as int,
              child: Text('${bus['name']} - ${bus['car_number'] ?? ''}'),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedBusId = value;
            });
          },
        ),
        SizedBox(height: SizeConstants.spaceBase),
        Row(
          children: [
            Text(
              'الحالة:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).brightness == Brightness.dark
                    ? ColorConstants.textPrimaryDark
                    : ColorConstants.textPrimary,
              ),
            ),
            SizedBox(width: SizeConstants.spaceBase),
            Expanded(
              child: Row(
                children: [
                  Radio<int>(
                    value: 1,
                    groupValue: _status,
                    onChanged: (value) {
                      setState(() {
                        _status = value!;
                      });
                    },
                    activeColor: ColorConstants.primary,
                  ),
                  Text(
                    'نشط',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? ColorConstants.textPrimaryDark
                          : ColorConstants.textPrimary,
                    ),
                  ),
                  SizedBox(width: SizeConstants.spaceBase),
                  Radio<int>(
                    value: 0,
                    groupValue: _status,
                    onChanged: (value) {
                      setState(() {
                        _status = value!;
                      });
                    },
                    activeColor: ColorConstants.primary,
                  ),
                  Text(
                    'غير نشط',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? ColorConstants.textPrimaryDark
                          : ColorConstants.textPrimary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: widget.isLoading ? null : _handleSubmit,
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorConstants.primary,
          padding: const EdgeInsets.symmetric(vertical: SizeConstants.spaceBase),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
          ),
          elevation: 2,
        ),
        child: widget.isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(ColorConstants.white),
                ),
              )
            : Text(
                widget.supervisor == null ? 'إضافة المشرف' : 'تحديث المشرف',
                style: const TextStyle(
                  color: ColorConstants.white,
                  fontWeight: FontWeight.w600,
                  fontSize: SizeConstants.fontBase,
                ),
              ),
      ),
    );
  }

  void _handleSubmit() {
    if (_formKey.currentState!.validate()) {
      final supervisor = Supervisor(
        id: widget.supervisor?.id,
        name: _nameController.text.trim(),
        username: _usernameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim(),
        address: _addressController.text.trim().isEmpty ? null : _addressController.text.trim(),
        cityName: _cityController.text.trim().isEmpty ? null : _cityController.text.trim(),
        birthDate: _birthDate?.toIso8601String().split('T')[0],
        joiningDate: _joiningDate?.toIso8601String().split('T')[0],
        genderId: _selectedGenderId,
        religionId: _selectedReligionId,
        busId: _selectedBusId,
        status: _status,
      );

      widget.onSubmit(supervisor);
    }
  }
}

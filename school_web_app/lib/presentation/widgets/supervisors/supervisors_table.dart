import 'package:flutter/material.dart';
import '../../../domain/entities/supervisor.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../common/loading_widget.dart';
import '../../../core/constants/size_constants.dart';

/// Supervisors table widget with responsive design
/// Following Single Responsibility Principle by focusing only on supervisors table display
class SupervisorsTable extends StatelessWidget {
  final List<Supervisor> supervisors;
  final bool isLoading;
  final bool isLoadingMore;
  final bool hasMoreData;
  final VoidCallback onLoadMore;
  final Function(Supervisor) onEdit;
  final Function(Supervisor) onDelete;
  final Function(Supervisor) onView;

  const SupervisorsTable({
    super.key,
    required this.supervisors,
    required this.isLoading,
    required this.isLoadingMore,
    required this.hasMoreData,
    required this.onLoadMore,
    required this.onEdit,
    required this.onDelete,
    required this.onView,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final isMobile = deviceType == DeviceScreenType.mobile;

    return Container(
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.cardDark
                : ColorConstants.white,
        borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Table header
          _buildTableHeader(context, isMobile),

          // Divider
          Divider(
            height: 1,
            color:
                Theme.of(context).brightness == Brightness.dark
                    ? ColorConstants.textSecondaryDark.withAlpha(80)
                    : Colors.grey.shade200,
          ),

          // Table content
          Expanded(child: _buildTableContent(context, isMobile)),
        ],
      ),
    );
  }

  Widget _buildTableHeader(BuildContext context, bool isMobile) {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceBase),
      child: Row(
        children: [
          Icon(
            Icons.supervisor_account_rounded,
            color: ColorConstants.primary,
            size: SizeConstants.iconMD,
          ),
          SizedBox(width: SizeConstants.spaceSM),
          Text(
            'قائمة المشرفين (${supervisors.length})',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? ColorConstants.textPrimaryDark
                      : ColorConstants.textPrimary,
            ),
          ),
          const Spacer(),
          if (!isMobile) ...[
            Text(
              'إجمالي: ${supervisors.length} مشرف',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? ColorConstants.textSecondaryDark
                        : ColorConstants.textSecondary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTableContent(BuildContext context, bool isMobile) {
    if (supervisors.isEmpty) {
      return const Center(child: Text('لا توجد مشرفين'));
    }

    return ListView.builder(
      itemCount: supervisors.length + (hasMoreData ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == supervisors.length) {
          // Load more indicator
          return _buildLoadMoreIndicator();
        }

        final supervisor = supervisors[index];
        return _buildSupervisorRow(context, supervisor, index, isMobile);
      },
    );
  }

  Widget _buildSupervisorRow(
    BuildContext context,
    Supervisor supervisor,
    int index,
    bool isMobile,
  ) {
    return InkWell(
      onTap: () => onView(supervisor),
      child: Container(
        padding: EdgeInsets.all(SizeConstants.spaceBase),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? ColorConstants.textSecondaryDark.withAlpha(80)
                      : Colors.grey.shade100,
              width: 1,
            ),
          ),
        ),
        child:
            isMobile
                ? _buildMobileRow(context, supervisor)
                : _buildDesktopRow(context, supervisor),
      ),
    );
  }

  Widget _buildMobileRow(BuildContext context, Supervisor supervisor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            // Avatar
            CircleAvatar(
              radius: 20,
              backgroundColor: ColorConstants.primary.withValues(alpha: 0.1),
              child: Text(
                supervisor.name?.isNotEmpty == true
                    ? supervisor.name![0].toUpperCase()
                    : 'M',
                style: TextStyle(
                  color: ColorConstants.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(width: SizeConstants.spaceSM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    supervisor.name ?? 'غير محدد',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? ColorConstants.textPrimaryDark
                              : ColorConstants.textPrimary,
                    ),
                  ),
                  if (supervisor.email?.isNotEmpty == true) ...[
                    SizedBox(height: SizeConstants.spaceXS),
                    Text(
                      supervisor.email!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? ColorConstants.textSecondaryDark
                                : ColorConstants.textSecondary,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            _buildActionButtons(context, supervisor, true),
          ],
        ),
        SizedBox(height: SizeConstants.spaceSM),
        Row(
          children: [
            _buildInfoChip(
              context,
              Icons.phone,
              supervisor.phone ?? 'غير محدد',
            ),
            SizedBox(width: SizeConstants.spaceXS),
            if (supervisor.busName?.isNotEmpty == true)
              _buildInfoChip(
                context,
                Icons.directions_bus,
                supervisor.busName!,
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildDesktopRow(BuildContext context, Supervisor supervisor) {
    return Row(
      children: [
        // Avatar and name
        Expanded(
          flex: 3,
          child: Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: ColorConstants.primary.withValues(alpha: 0.1),
                child: Text(
                  supervisor.name?.isNotEmpty == true
                      ? supervisor.name![0].toUpperCase()
                      : 'M',
                  style: TextStyle(
                    color: ColorConstants.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              SizedBox(width: SizeConstants.spaceSM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      supervisor.name ?? 'غير محدد',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? ColorConstants.textPrimaryDark
                                : ColorConstants.textPrimary,
                      ),
                    ),
                    if (supervisor.email?.isNotEmpty == true) ...[
                      SizedBox(height: SizeConstants.spaceXS),
                      Text(
                        supervisor.email!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color:
                              Theme.of(context).brightness == Brightness.dark
                                  ? ColorConstants.textSecondaryDark
                                  : ColorConstants.textSecondary,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),

        // Phone
        Expanded(
          flex: 2,
          child: Text(
            supervisor.phone ?? 'غير محدد',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? ColorConstants.textSecondaryDark
                      : ColorConstants.textSecondary,
            ),
          ),
        ),

        // Bus
        Expanded(
          flex: 2,
          child: Text(
            supervisor.busName ?? 'غير مخصص',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? ColorConstants.textSecondaryDark
                      : ColorConstants.textSecondary,
            ),
          ),
        ),

        // Status
        Expanded(flex: 1, child: _buildStatusChip(context, supervisor.status)),

        // Actions
        _buildActionButtons(context, supervisor, false),
      ],
    );
  }

  Widget _buildInfoChip(BuildContext context, IconData icon, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceXS, vertical: 4),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.surfaceDark
                : ColorConstants.surface,
        borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color:
                Theme.of(context).brightness == Brightness.dark
                    ? ColorConstants.textSecondaryDark
                    : ColorConstants.textSecondary,
          ),
          SizedBox(width: SizeConstants.spaceXS),
          Text(
            text,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? ColorConstants.textSecondaryDark
                      : ColorConstants.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context, int? status) {
    final isActive = status == 1;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceXS, vertical: 4),
      decoration: BoxDecoration(
        color:
            isActive
                ? ColorConstants.success.withValues(alpha: 0.1)
                : ColorConstants.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
      ),
      child: Text(
        isActive ? 'نشط' : 'غير نشط',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: isActive ? ColorConstants.success : ColorConstants.error,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    Supervisor supervisor,
    bool isMobile,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: () => onView(supervisor),
          icon: Icon(
            Icons.visibility_outlined,
            size: isMobile ? 20 : 18,
            color: ColorConstants.primary,
          ),
          tooltip: 'عرض',
        ),
        IconButton(
          onPressed: () => onEdit(supervisor),
          icon: Icon(
            Icons.edit_outlined,
            size: isMobile ? 20 : 18,
            color: ColorConstants.warning,
          ),
          tooltip: 'تعديل',
        ),
        IconButton(
          onPressed: () => onDelete(supervisor),
          icon: Icon(
            Icons.delete_outline,
            size: isMobile ? 20 : 18,
            color: ColorConstants.error,
          ),
          tooltip: 'حذف',
        ),
      ],
    );
  }

  Widget _buildLoadMoreIndicator() {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceBase),
      child: Center(
        child:
            isLoadingMore
                ? const LoadingWidget()
                : TextButton(
                  onPressed: onLoadMore,
                  child: const Text('تحميل المزيد'),
                ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import '../../../data/models/student_attendance_model.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../common/loading_widget.dart';
import '../../../core/constants/size_constants.dart';

/// Attendance Tab Widget
/// Displays list of present or absent students
/// Based on the original SchoolX project structure
class AttendanceTab extends StatelessWidget {
  final List<StudentAttendanceModel> students;
  final bool isLoading;
  final bool isPresent;
  final VoidCallback onRefresh;

  const AttendanceTab({
    super.key,
    required this.students,
    required this.isLoading,
    required this.isPresent,
    required this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return LoadingWidget(
        message:
            isPresent
                ? 'جاري تحميل الطلاب الحاضرين...'
                : 'جاري تحميل الطلاب الغائبين...',
      );
    }

    if (students.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () async => onRefresh(),
      child: ListView.builder(
        padding: EdgeInsets.all(SizeConstants.spaceBase),
        itemCount: students.length,
        itemBuilder: (context, index) {
          final student = students[index];
          return StudentAttendanceItem(student: student, isPresent: isPresent);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(SizeConstants.spaceXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isPresent ? Icons.check_circle_outline : Icons.cancel_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            SizedBox(height: SizeConstants.spaceLG),
            Text(
              isPresent ? 'لا يوجد طلاب حاضرين' : 'لا يوجد طلاب غائبين',
              style: TextStyle(
                fontSize: ResponsiveUtils.getFontSize(18),
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: SizeConstants.spaceXS),
            Text(
              isPresent
                  ? 'لم يتم تسجيل حضور أي طالب في هذه الرحلة'
                  : 'جميع الطلاب كانوا حاضرين في هذه الرحلة',
              style: TextStyle(
                fontSize: ResponsiveUtils.getFontSize(14),
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: SizeConstants.spaceXL),
            ElevatedButton.icon(
              onPressed: onRefresh,
              icon: const Icon(Icons.refresh_rounded, color: Colors.white),
              label: Text(
                'إعادة المحاولة',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: ResponsiveUtils.getFontSize(14),
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorConstants.primary,
                padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceLG,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Student Attendance Item Widget
/// Displays individual student attendance information
class StudentAttendanceItem extends StatelessWidget {
  final StudentAttendanceModel student;
  final bool isPresent;

  const StudentAttendanceItem({
    super.key,
    required this.student,
    required this.isPresent,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(SizeConstants.spaceBase),
        child: Row(
          children: [
            // Student avatar
            _buildAvatar(),

            SizedBox(width: SizeConstants.spaceBase),

            // Student info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Student name
                  Text(
                    student.name ?? 'غير محدد',
                    style: TextStyle(
                      fontSize: ResponsiveUtils.getFontSize(16),
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),

                  SizedBox(height: SizeConstants.spaceXS),

                  // Grade and classroom
                  Text(
                    '${student.grade ?? 'غير محدد'} - ${student.classroom ?? 'غير محدد'}',
                    style: TextStyle(
                      fontSize: ResponsiveUtils.getFontSize(14),
                      color: Colors.grey[600],
                    ),
                  ),

                  // Attendance time (for present students)
                  if (isPresent && student.attendanceTime != null) ...[
                    SizedBox(height: SizeConstants.spaceXS),
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: SizeConstants.iconXS,
                          color: Colors.grey[500],
                        ),
                        SizedBox(width: SizeConstants.spaceXS),
                        Text(
                          _formatAttendanceTime(student.attendanceTime!),
                          style: TextStyle(
                            fontSize: ResponsiveUtils.getFontSize(12),
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),

            // Status indicator
            _buildStatusIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isPresent ? Colors.green[50] : Colors.red[50],
        border: Border.all(
          color: isPresent ? Colors.green[200]! : Colors.red[200]!,
          width: 2,
        ),
      ),
      child:
          student.profileImage != null && student.profileImage!.isNotEmpty
              ? ClipOval(
                child: Image.network(
                  student.profileImage!,
                  width: 50,
                  height: 50,
                  fit: BoxFit.cover,
                  errorBuilder:
                      (context, error, stackTrace) => _buildDefaultAvatar(),
                ),
              )
              : _buildDefaultAvatar(),
    );
  }

  Widget _buildDefaultAvatar() {
    return Icon(
      Icons.person,
      size: 30,
      color: isPresent ? Colors.green[400] : Colors.red[400],
    );
  }

  Widget _buildStatusIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceXS, vertical: 4),
      decoration: BoxDecoration(
        color: isPresent ? Colors.green[100] : Colors.red[100],
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isPresent ? Icons.check_circle : Icons.cancel,
            size: SizeConstants.iconXS,
            color: isPresent ? Colors.green[700] : Colors.red[700],
          ),
          SizedBox(width: SizeConstants.spaceXS),
          Text(
            isPresent ? 'حاضر' : 'غائب',
            style: TextStyle(
              fontSize: ResponsiveUtils.getFontSize(12),
              fontWeight: FontWeight.w600,
              color: isPresent ? Colors.green[700] : Colors.red[700],
            ),
          ),
        ],
      ),
    );
  }

  String _formatAttendanceTime(String attendanceTime) {
    try {
      final dateTime = DateTime.parse(attendanceTime);
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return attendanceTime;
    }
  }
}

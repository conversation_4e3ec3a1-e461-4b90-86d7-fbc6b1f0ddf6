import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../domain/entities/trip.dart';
import '../../../core/constants/size_constants.dart';

/// CurrentTripCard widget for displaying current trip information
/// Following Single Responsibility Principle by focusing only on current trip display
class CurrentTripCard extends StatelessWidget {
  final Trip trip;
  final VoidCallback? onViewDetails;
  final VoidCallback? onTrackLocation;

  const CurrentTripCard({
    super.key,
    required this.trip,
    this.onViewDetails,
    this.onTrackLocation,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(SizeConstants.radiusLG),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(26), // 0.1 * 255 = 26
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: <PERSON>umn(
        children: [
          // Header with status
          _buildHeader(),

          // Trip details
          _buildTripDetails(),

          // Action buttons
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(SizeConstants.spaceMD),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _getStatusColor(),
            _getStatusColor().withAlpha(204),
          ], // 0.8 * 255 = 204
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(SizeConstants.spaceXS),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
              borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
            ),
            child: Icon(_getStatusIcon(), color: Colors.white, size: SizeConstants.iconMD),
          ),
          SizedBox(width: SizeConstants.spaceSM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  trip.name,
                  style: Get.textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: SizeConstants.spaceXS),
                Text(
                  _getStatusText(),
                  style: Get.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withAlpha(230), // 0.9 * 255 = 230
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceSM, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
              borderRadius: BorderRadius.circular(SizeConstants.radiusXL),
            ),
            child: Text(
              _getStatusText(),
              style: Get.textTheme.bodySmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTripDetails() {
    return Padding(
      padding: EdgeInsets.all(SizeConstants.spaceMD),
      child: Column(
        children: [
          // Bus and driver info
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.directions_bus_rounded,
                  label: 'الباص',
                  value:
                      trip.busNumber.isNotEmpty ? trip.busNumber : 'غير محدد',
                ),
              ),
              SizedBox(width: SizeConstants.spaceBase),
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.person_rounded,
                  label: 'السائق',
                  value:
                      trip.driverName.isNotEmpty ? trip.driverName : 'غير محدد',
                ),
              ),
            ],
          ),

          SizedBox(height: SizeConstants.spaceBase),

          // Supervisor and time info
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.supervisor_account_rounded,
                  label: 'المشرف',
                  value:
                      trip.supervisorName.isNotEmpty
                          ? trip.supervisorName
                          : 'غير محدد',
                ),
              ),
              SizedBox(width: SizeConstants.spaceBase),
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.access_time_rounded,
                  label: 'وقت البدء',
                  value: _formatTime(trip.startTime),
                ),
              ),
            ],
          ),

          SizedBox(height: SizeConstants.spaceBase),

          // Students info
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.people_rounded,
                  label: 'عدد الطلاب',
                  value: '${trip.studentIds.length}',
                ),
              ),
              SizedBox(width: SizeConstants.spaceBase),
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.check_circle_rounded,
                  label: 'الحاضرون',
                  value: '${trip.attendedStudentIds.length}',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceSM),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: SizeConstants.iconXS, color: Colors.grey[600]),
              const SizedBox(width: 6),
              Text(
                label,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          SizedBox(height: SizeConstants.spaceXS),
          Text(
            value,
            style: Get.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceMD),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: onTrackLocation,
              icon: const Icon(Icons.location_on_rounded),
              label: const Text('تتبع الموقع'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Get.theme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: SizeConstants.spaceSM),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                ),
              ),
            ),
          ),
          SizedBox(width: SizeConstants.spaceSM),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: onViewDetails,
              icon: const Icon(Icons.info_outline_rounded),
              label: const Text('التفاصيل'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Get.theme.primaryColor,
                padding: const EdgeInsets.symmetric(vertical: SizeConstants.spaceSM),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (trip.status.toLowerCase()) {
      case 'active':
      case 'in_progress':
        return Colors.green;
      case 'completed':
        return Colors.blue;
      case 'cancelled':
        return Colors.red;
      case 'scheduled':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon() {
    switch (trip.status.toLowerCase()) {
      case 'active':
      case 'in_progress':
        return Icons.play_circle_filled_rounded;
      case 'completed':
        return Icons.check_circle_rounded;
      case 'cancelled':
        return Icons.cancel_rounded;
      case 'scheduled':
        return Icons.schedule_rounded;
      default:
        return Icons.help_rounded;
    }
  }

  String _getStatusText() {
    switch (trip.status.toLowerCase()) {
      case 'active':
      case 'in_progress':
        return 'جارية';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      case 'scheduled':
        return 'مجدولة';
      default:
        return 'غير معروف';
    }
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

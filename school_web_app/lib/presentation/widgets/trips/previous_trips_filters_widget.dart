import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/trip_controller.dart';
import '../../../core/constants/size_constants.dart';

/// Previous Trips Filters Widget
/// Simplified filtering for previous trips using the original API endpoints
class PreviousTripsFiltersWidget extends StatefulWidget {
  const PreviousTripsFiltersWidget({super.key});

  @override
  State<PreviousTripsFiltersWidget> createState() =>
      _PreviousTripsFiltersWidgetState();
}

class _PreviousTripsFiltersWidgetState
    extends State<PreviousTripsFiltersWidget> {
  late TripController _controller;
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _busController = TextEditingController();
  final TextEditingController _dateController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _controller = Get.find<TripController>();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _busController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceBase),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(26), // 0.1 * 255 = 26
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.filter_list_rounded,
                color: ColorConstants.primary,
                size: SizeConstants.iconSM,
              ),
              SizedBox(width: SizeConstants.spaceXS),
              Text(
                'فلترة الرحلات',
                style: Get.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ColorConstants.primary,
                ),
              ),
            ],
          ),
          SizedBox(height: SizeConstants.spaceBase),

          // Filters
          if (ResponsiveUtils.isDesktop(context))
            _buildDesktopFilters()
          else
            _buildMobileFilters(),

          SizedBox(height: SizeConstants.spaceBase),

          // Action buttons
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildDesktopFilters() {
    return Row(
      children: [
        Expanded(child: _buildSearchFilter()),
        SizedBox(width: SizeConstants.spaceBase),
        Expanded(child: _buildBusFilter()),
        SizedBox(width: SizeConstants.spaceBase),
        Expanded(child: _buildDateFilter()),
      ],
    );
  }

  Widget _buildMobileFilters() {
    return Column(
      children: [
        _buildSearchFilter(),
        SizedBox(height: SizeConstants.spaceSM),
        Row(
          children: [
            Expanded(child: _buildBusFilter()),
            SizedBox(width: SizeConstants.spaceSM),
            Expanded(child: _buildDateFilter()),
          ],
        ),
      ],
    );
  }

  Widget _buildSearchFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'البحث',
          style: Get.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        SizedBox(height: SizeConstants.spaceXS),
        TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'ابحث في الرحلات...',
            prefixIcon: Icon(Icons.search, color: Colors.grey[500]),
            suffixIcon:
                _searchController.text.isNotEmpty
                    ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        _controller.clearRecentTripsFilters();
                      },
                      icon: Icon(Icons.clear, color: Colors.grey[500]),
                    )
                    : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
              borderSide: BorderSide(color: ColorConstants.primary),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceSM,
              vertical: 12,
            ),
          ),
          onChanged: (value) {
            setState(() {});
          },
        ),
      ],
    );
  }

  Widget _buildBusFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الباص',
          style: Get.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        SizedBox(height: SizeConstants.spaceXS),
        TextField(
          controller: _busController,
          decoration: InputDecoration(
            hintText: 'رقم الباص',
            prefixIcon: Icon(Icons.directions_bus, color: Colors.grey[500]),
            suffixIcon:
                _busController.text.isNotEmpty
                    ? IconButton(
                      onPressed: () {
                        _busController.clear();
                        setState(() {});
                      },
                      icon: Icon(Icons.clear, color: Colors.grey[500]),
                    )
                    : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
              borderSide: BorderSide(color: ColorConstants.primary),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceSM,
              vertical: 12,
            ),
          ),
          onChanged: (value) {
            setState(() {});
          },
        ),
      ],
    );
  }

  Widget _buildDateFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التاريخ',
          style: Get.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        SizedBox(height: SizeConstants.spaceXS),
        TextField(
          controller: _dateController,
          decoration: InputDecoration(
            hintText: 'اختر التاريخ',
            prefixIcon: Icon(Icons.calendar_today, color: Colors.grey[500]),
            suffixIcon:
                _dateController.text.isNotEmpty
                    ? IconButton(
                      onPressed: () {
                        _dateController.clear();
                        setState(() {});
                      },
                      icon: Icon(Icons.clear, color: Colors.grey[500]),
                    )
                    : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
              borderSide: BorderSide(color: ColorConstants.primary),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceSM,
              vertical: 12,
            ),
          ),
          readOnly: true,
          onTap: _selectDate,
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _applyFilters,
            icon: const Icon(Icons.filter_list, color: Colors.white),
            label: const Text(
              'تطبيق الفلاتر',
              style: TextStyle(color: Colors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorConstants.primary,
              padding: const EdgeInsets.symmetric(vertical: SizeConstants.spaceSM),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
              ),
            ),
          ),
        ),
        SizedBox(width: SizeConstants.spaceSM),
        OutlinedButton.icon(
          onPressed: _clearFilters,
          icon: Icon(Icons.clear, color: Colors.grey[600]),
          label: Text('مسح', style: TextStyle(color: Colors.grey[600])),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: SizeConstants.spaceSM, horizontal: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
            ),
            side: BorderSide(color: Colors.grey[300]!),
          ),
        ),
      ],
    );
  }

  void _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: ColorConstants.primary),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      final formattedDate =
          '${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}';
      _dateController.text = formattedDate;
      setState(() {});
    }
  }

  void _applyFilters() {
    _controller.applyRecentTripsFilters(
      search: _searchController.text.isEmpty ? null : _searchController.text,
      busId: _busController.text.isEmpty ? null : _busController.text,
      date: _dateController.text.isEmpty ? null : _dateController.text,
    );
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _busController.clear();
      _dateController.clear();
    });
    _controller.clearRecentTripsFilters();
  }
}

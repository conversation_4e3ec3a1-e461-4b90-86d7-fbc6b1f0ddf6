import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/previous_trip_model.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../core/constants/size_constants.dart';

/// PreviousTripsTable widget for displaying previous trips in a table format
/// Following Single Responsibility Principle by focusing only on trips table display
class PreviousTripsTable extends StatelessWidget {
  final List<PreviousTripModel> trips;
  final bool isLoading;
  final Function(PreviousTripModel) onTripTap;
  final VoidCallback onLoadMore;

  const PreviousTripsTable({
    super.key,
    required this.trips,
    required this.isLoading,
    required this.onTripTap,
    required this.onLoadMore,
  });

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);

    if (isDesktop) {
      return _buildDesktopTable();
    } else {
      return _buildMobileList();
    }
  }

  Widget _buildDesktopTable() {
    return Column(
      children: [
        // Table header
        Container(
          padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceBase, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
          ),
          child: Row(
            children: [
              const Expanded(
                flex: 2,
                child: Text(
                  'اسم الرحلة',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              const Expanded(
                flex: 2,
                child: Text(
                  'الباص',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              const Expanded(
                flex: 2,
                child: Text(
                  'السائق',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              const Expanded(
                flex: 2,
                child: Text(
                  'التاريخ',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              const Expanded(
                flex: 1,
                child: Text(
                  'الحالة',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              const Expanded(
                flex: 1,
                child: Text(
                  'إجمالي الطلاب',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              const Expanded(
                flex: 1,
                child: Text(
                  'الحاضرين',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              const Expanded(
                flex: 1,
                child: Text(
                  'الغائبين',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(width: 60), // Actions column
            ],
          ),
        ),

        // Table rows
        Expanded(
          child: ListView.builder(
            itemCount: trips.length + (isLoading ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == trips.length) {
                // Loading indicator at the end
                return const Padding(
                  padding: EdgeInsets.all(SizeConstants.spaceBase),
                  child: Center(child: CircularProgressIndicator()),
                );
              }

              final trip = trips[index];
              return _buildDesktopTableRow(trip, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopTableRow(PreviousTripModel trip, int index) {
    return InkWell(
      onTap: () => onTripTap(trip),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceBase, vertical: 12),
        decoration: BoxDecoration(
          color: index.isEven ? Colors.white : Colors.grey[50],
          border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
        ),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                trip.tripType == 'start_day' ? 'رحلة الصباح' : 'رحلة المساء',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ),
            Expanded(flex: 2, child: Text(trip.busName ?? 'غير محدد')),
            Expanded(flex: 2, child: Text(trip.supervisorName ?? 'غير محدد')),
            Expanded(flex: 2, child: Text(trip.date ?? 'غير محدد')),
            Expanded(flex: 1, child: _buildStatusChip(trip.status ?? 0)),
            Expanded(
              flex: 1,
              child: _buildAttendanceCount(
                (trip.presentStudents?.length ?? 0) +
                    (trip.absentStudents?.length ?? 0),
                Colors.blue,
                Icons.people,
              ),
            ),
            Expanded(
              flex: 1,
              child: _buildAttendanceCount(
                trip.presentStudents?.length ?? 0,
                Colors.green,
                Icons.check_circle,
              ),
            ),
            Expanded(
              flex: 1,
              child: _buildAttendanceCount(
                trip.absentStudents?.length ?? 0,
                Colors.red,
                Icons.cancel,
              ),
            ),
            SizedBox(
              width: 60,
              child: IconButton(
                onPressed: () => onTripTap(trip),
                icon: const Icon(Icons.visibility_rounded),
                tooltip: 'عرض التفاصيل',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileList() {
    return ListView.builder(
      itemCount: trips.length + (isLoading ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == trips.length) {
          // Loading indicator at the end
          return const Padding(
            padding: EdgeInsets.all(SizeConstants.spaceBase),
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final trip = trips[index];
        return _buildMobileCard(trip);
      },
    );
  }

  Widget _buildMobileCard(PreviousTripModel trip) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceBase, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(SizeConstants.radiusMD)),
      child: InkWell(
        onTap: () => onTripTap(trip),
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        child: Padding(
          padding: EdgeInsets.all(SizeConstants.spaceBase),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Trip name and status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      trip.tripType == 'start_day'
                          ? 'رحلة الصباح'
                          : 'رحلة المساء',
                      style: Get.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildStatusChip(trip.status ?? 0),
                ],
              ),

              SizedBox(height: SizeConstants.spaceSM),

              // Trip details
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.directions_bus_rounded,
                      label: 'الباص',
                      value: trip.busName ?? 'غير محدد',
                    ),
                  ),
                  SizedBox(width: SizeConstants.spaceBase),
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.person_rounded,
                      label: 'المشرف',
                      value: trip.supervisorName ?? 'غير محدد',
                    ),
                  ),
                ],
              ),

              SizedBox(height: SizeConstants.spaceSM),

              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.calendar_today_rounded,
                      label: 'التاريخ',
                      value: trip.date ?? 'غير محدد',
                    ),
                  ),
                  SizedBox(width: SizeConstants.spaceBase),
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.people_rounded,
                      label: 'المرافقين',
                      value: '${trip.attendants?.length ?? 0}',
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(icon, size: SizeConstants.iconXS, color: Colors.grey[600]),
        const SizedBox(width: 6),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: Get.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAttendanceCount(int count, Color color, IconData icon) {
    String tooltip = '';
    switch (icon) {
      case Icons.people:
        tooltip = 'إجمالي الطلاب';
        break;
      case Icons.check_circle:
        tooltip = 'الطلاب الحاضرين';
        break;
      case Icons.cancel:
        tooltip = 'الطلاب الغائبين';
        break;
    }

    return Tooltip(
      message: tooltip,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: SizeConstants.iconXS, color: color),
          SizedBox(width: SizeConstants.spaceXS),
          Text(
            count.toString(),
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: SizeConstants.fontSM,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(int status) {
    Color backgroundColor;
    Color textColor;
    String statusText;

    switch (status) {
      case 1:
        backgroundColor = Colors.green[100]!;
        textColor = Colors.green[800]!;
        statusText = 'مكتملة';
        break;
      case 0:
        backgroundColor = Colors.red[100]!;
        textColor = Colors.red[800]!;
        statusText = 'ملغية';
        break;
      default:
        backgroundColor = Colors.grey[100]!;
        textColor = Colors.grey[800]!;
        statusText = 'غير معروف';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceXS, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: textColor,
          fontSize: SizeConstants.fontXS,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

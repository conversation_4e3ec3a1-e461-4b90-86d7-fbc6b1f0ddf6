import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../../data/models/student_attendance_model.dart';

/// Student Map Markers Widget
/// Creates custom markers for students on the map
/// Based on the original SchoolX project structure
class StudentMapMarkers {
  /// Create markers for present students
  static Set<Marker> createPresentStudentMarkers(
    List<StudentAttendanceModel> presentStudents,
  ) {
    final markers = <Marker>{};

    for (int i = 0; i < presentStudents.length; i++) {
      final student = presentStudents[i];
      if (student.latitude != null && student.longitude != null) {
        try {
          final lat = double.parse(student.latitude!);
          final lng = double.parse(student.longitude!);

          markers.add(
            Marker(
              markerId: MarkerId('present_student_${student.id}'),
              position: LatLng(lat, lng),
              icon: BitmapDescriptor.defaultMarkerWithHue(
                BitmapDescriptor.hueGreen,
              ),
              infoWindow: InfoWindow(
                title: '✅ ${student.name ?? 'طالب'}',
                snippet: _buildStudentSnippet(student, true),
              ),
              onTap: () => _onStudentMarkerTap(student, true),
            ),
          );
        } catch (e) {
          // Skip invalid coordinates
          continue;
        }
      }
    }

    return markers;
  }

  /// Create markers for absent students
  static Set<Marker> createAbsentStudentMarkers(
    List<StudentAttendanceModel> absentStudents,
  ) {
    final markers = <Marker>{};

    for (int i = 0; i < absentStudents.length; i++) {
      final student = absentStudents[i];
      if (student.latitude != null && student.longitude != null) {
        try {
          final lat = double.parse(student.latitude!);
          final lng = double.parse(student.longitude!);

          markers.add(
            Marker(
              markerId: MarkerId('absent_student_${student.id}'),
              position: LatLng(lat, lng),
              icon: BitmapDescriptor.defaultMarkerWithHue(
                BitmapDescriptor.hueOrange,
              ),
              infoWindow: InfoWindow(
                title: '❌ ${student.name ?? 'طالب'}',
                snippet: _buildStudentSnippet(student, false),
              ),
              onTap: () => _onStudentMarkerTap(student, false),
            ),
          );
        } catch (e) {
          // Skip invalid coordinates
          continue;
        }
      }
    }

    return markers;
  }

  /// Build student snippet for info window
  static String _buildStudentSnippet(
    StudentAttendanceModel student,
    bool isPresent,
  ) {
    final parts = <String>[];

    // Add attendance status
    parts.add(isPresent ? 'حاضر' : 'غائب');

    // Add grade if available
    if (student.grade != null && student.grade!.isNotEmpty) {
      parts.add(student.grade!);
    }

    // Add classroom if available
    if (student.classroom != null && student.classroom!.isNotEmpty) {
      parts.add('فصل ${student.classroom}');
    }

    // Add attendance time for present students
    if (isPresent && student.attendanceTime != null) {
      final time = _formatTime(student.attendanceTime!);
      if (time.isNotEmpty) {
        parts.add('وقت الحضور: $time');
      }
    }

    return parts.join(' • ');
  }

  /// Format time string
  static String _formatTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return '';
    }
  }

  /// Handle student marker tap
  static void _onStudentMarkerTap(
    StudentAttendanceModel student,
    bool isPresent,
  ) {
    // This could be extended to show a detailed student info dialog
    // For now, it's just a placeholder
    debugPrint(
      'Student marker tapped: ${student.name} (${isPresent ? 'Present' : 'Absent'})',
    );
  }

  /// Create custom marker icon for students
  static Future<BitmapDescriptor> createCustomStudentMarker({
    required bool isPresent,
    required Color color,
    String? text,
  }) async {
    // This is a placeholder for creating custom marker icons
    // In a real implementation, you would create a custom icon
    // with the student's photo or initials
    return isPresent
        ? BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen)
        : BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange);
  }

  /// Get marker color based on attendance status
  static Color getMarkerColor(bool isPresent) {
    return isPresent ? Colors.green : Colors.orange;
  }

  /// Create cluster of markers for students in the same location
  static Set<Marker> createClusteredMarkers(
    List<StudentAttendanceModel> students,
    bool isPresent,
  ) {
    final markers = <Marker>{};
    final locationGroups = <String, List<StudentAttendanceModel>>{};

    // Group students by location
    for (final student in students) {
      if (student.latitude != null && student.longitude != null) {
        final locationKey = '${student.latitude}_${student.longitude}';
        locationGroups.putIfAbsent(locationKey, () => []).add(student);
      }
    }

    // Create markers for each location group
    locationGroups.forEach((locationKey, studentsAtLocation) {
      if (studentsAtLocation.isNotEmpty) {
        final firstStudent = studentsAtLocation.first;
        try {
          final lat = double.parse(firstStudent.latitude!);
          final lng = double.parse(firstStudent.longitude!);

          String title;
          String snippet;

          if (studentsAtLocation.length == 1) {
            // Single student
            title = '${isPresent ? '✅' : '❌'} ${firstStudent.name ?? 'طالب'}';
            snippet = _buildStudentSnippet(firstStudent, isPresent);
          } else {
            // Multiple students at same location
            title =
                '${isPresent ? '✅' : '❌'} ${studentsAtLocation.length} طلاب';
            snippet = studentsAtLocation
                .map((s) => s.name ?? 'طالب')
                .take(3)
                .join('، ');
            if (studentsAtLocation.length > 3) {
              snippet += '...';
            }
          }

          markers.add(
            Marker(
              markerId: MarkerId(
                '${isPresent ? 'present' : 'absent'}_$locationKey',
              ),
              position: LatLng(lat, lng),
              icon: BitmapDescriptor.defaultMarkerWithHue(
                isPresent
                    ? BitmapDescriptor.hueGreen
                    : BitmapDescriptor.hueOrange,
              ),
              infoWindow: InfoWindow(title: title, snippet: snippet),
            ),
          );
        } catch (e) {
          // Skip invalid coordinates
          return;
        }
      }
    });

    return markers;
  }

  /// Calculate bounds for all student markers
  static LatLngBounds? calculateStudentBounds(
    List<StudentAttendanceModel> allStudents,
  ) {
    final validCoordinates = <LatLng>[];

    for (final student in allStudents) {
      if (student.latitude != null && student.longitude != null) {
        try {
          final lat = double.parse(student.latitude!);
          final lng = double.parse(student.longitude!);
          validCoordinates.add(LatLng(lat, lng));
        } catch (e) {
          // Skip invalid coordinates
          continue;
        }
      }
    }

    if (validCoordinates.isEmpty) return null;

    double minLat = validCoordinates.first.latitude;
    double maxLat = validCoordinates.first.latitude;
    double minLng = validCoordinates.first.longitude;
    double maxLng = validCoordinates.first.longitude;

    for (final coord in validCoordinates) {
      minLat = minLat < coord.latitude ? minLat : coord.latitude;
      maxLat = maxLat > coord.latitude ? maxLat : coord.latitude;
      minLng = minLng < coord.longitude ? minLng : coord.longitude;
      maxLng = maxLng > coord.longitude ? maxLng : coord.longitude;
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  /// Get statistics for student locations
  static Map<String, dynamic> getStudentLocationStats(
    List<StudentAttendanceModel> presentStudents,
    List<StudentAttendanceModel> absentStudents,
  ) {
    int presentWithLocation = 0;
    int absentWithLocation = 0;

    for (final student in presentStudents) {
      if (student.latitude != null && student.longitude != null) {
        try {
          double.parse(student.latitude!);
          double.parse(student.longitude!);
          presentWithLocation++;
        } catch (e) {
          // Invalid coordinates
        }
      }
    }

    for (final student in absentStudents) {
      if (student.latitude != null && student.longitude != null) {
        try {
          double.parse(student.latitude!);
          double.parse(student.longitude!);
          absentWithLocation++;
        } catch (e) {
          // Invalid coordinates
        }
      }
    }

    return {
      'presentWithLocation': presentWithLocation,
      'absentWithLocation': absentWithLocation,
      'totalWithLocation': presentWithLocation + absentWithLocation,
      'presentTotal': presentStudents.length,
      'absentTotal': absentStudents.length,
      'totalStudents': presentStudents.length + absentStudents.length,
    };
  }
}

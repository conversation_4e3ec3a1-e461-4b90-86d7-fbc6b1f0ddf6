import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/trip.dart';
import '../../../core/constants/size_constants.dart';

/// Trip card widget for displaying trip information
/// Following Single Responsibility Principle by focusing only on trip display
class Trip<PERSON>ard extends StatelessWidget {
  final Trip trip;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onStart;
  final VoidCallback? onEnd;
  final VoidCallback? onTrack;

  const TripCard({
    super.key,
    required this.trip,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onStart,
    this.onEnd,
    this.onTrack,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(SizeConstants.radiusMD)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
        child: Padding(
          padding: EdgeInsets.all(SizeConstants.spaceBase),
          child:
              ResponsiveUtils.isMobile(context)
                  ? _buildMobileLayout()
                  : _buildDesktopLayout(),
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        SizedBox(height: SizeConstants.spaceSM),
        _buildTripInfo(),
        SizedBox(height: SizeConstants.spaceSM),
        _buildStatusAndProgress(),
        SizedBox(height: SizeConstants.spaceSM),
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              SizedBox(height: SizeConstants.spaceXS),
              _buildTripInfo(),
            ],
          ),
        ),
        SizedBox(width: SizeConstants.spaceBase),
        Expanded(flex: 2, child: _buildStatusAndProgress()),
        SizedBox(width: SizeConstants.spaceBase),
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(SizeConstants.spaceXS),
          decoration: BoxDecoration(
            color: _getStatusColor().withAlpha(51),
            borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
          ),
          child: Icon(_getStatusIcon(), color: _getStatusColor(), size: SizeConstants.iconSM),
        ),
        SizedBox(width: SizeConstants.spaceSM),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                trip.name,
                style: Get.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),
              Text(
                'باص ${trip.busNumber} - ${trip.driverName}',
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        _buildStatusChip(),
      ],
    );
  }

  Widget _buildTripInfo() {
    return Column(
      children: [
        Row(
          children: [
            Icon(Icons.schedule, size: SizeConstants.iconXS, color: Colors.grey[600]),
            SizedBox(width: SizeConstants.spaceXS),
            Text(
              _formatTime(trip.startTime),
              style: Get.textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
            if (trip.endTime != null) ...[
              SizedBox(width: SizeConstants.spaceBase),
              Icon(Icons.flag, size: SizeConstants.iconXS, color: Colors.grey[600]),
              SizedBox(width: SizeConstants.spaceXS),
              Text(
                _formatTime(trip.endTime!),
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ],
        ),
        SizedBox(height: SizeConstants.spaceXS),
        Row(
          children: [
            Icon(Icons.people, size: SizeConstants.iconXS, color: Colors.grey[600]),
            SizedBox(width: SizeConstants.spaceXS),
            Text(
              '${trip.studentIds.length} طالب',
              style: Get.textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
            SizedBox(width: SizeConstants.spaceBase),
            Icon(Icons.person, size: SizeConstants.iconXS, color: Colors.grey[600]),
            SizedBox(width: SizeConstants.spaceXS),
            Text(
              trip.supervisorName,
              style: Get.textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusAndProgress() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (trip.status == 'in_progress') ...[
          Text(
            'التقدم',
            style: Get.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          SizedBox(height: SizeConstants.spaceXS),
          LinearProgressIndicator(
            value: trip.progressPercentage / 100,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(_getStatusColor()),
          ),
          SizedBox(height: SizeConstants.spaceXS),
          Text(
            '${trip.progressPercentage.toStringAsFixed(0)}%',
            style: Get.textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
          ),
        ],
        if (trip.attendedStudentIds.isNotEmpty) ...[
          SizedBox(height: SizeConstants.spaceXS),
          Row(
            children: [
              Icon(Icons.check_circle, size: SizeConstants.iconXS, color: Colors.green[600]),
              SizedBox(width: SizeConstants.spaceXS),
              Text(
                '${trip.attendedStudentIds.length} حاضر',
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Colors.green[600],
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildStatusChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceXS, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(),
        borderRadius: BorderRadius.circular(SizeConstants.radiusMD),
      ),
      child: Text(
        _getStatusText(),
        style: const TextStyle(
          color: Colors.white,
          fontSize: SizeConstants.fontXS,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        if (onTrack != null)
          _buildActionButton(
            icon: Icons.location_on,
            label: 'تتبع',
            color: Colors.blue,
            onPressed: onTrack!,
          ),
        if (onStart != null)
          _buildActionButton(
            icon: Icons.play_arrow,
            label: 'بدء',
            color: Colors.green,
            onPressed: onStart!,
          ),
        if (onEnd != null)
          _buildActionButton(
            icon: Icons.stop,
            label: 'إنهاء',
            color: Colors.orange,
            onPressed: onEnd!,
          ),
        if (onEdit != null)
          _buildActionButton(
            icon: Icons.edit,
            label: 'تحرير',
            color: Colors.blue,
            onPressed: onEdit!,
          ),
        if (onDelete != null)
          _buildActionButton(
            icon: Icons.delete,
            label: 'حذف',
            color: Colors.red,
            onPressed: onDelete!,
          ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceSM, vertical: 8),
        decoration: BoxDecoration(
          color: color.withAlpha(26),
          borderRadius: BorderRadius.circular(SizeConstants.radiusSM),
          border: Border.all(color: color.withAlpha(77)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: SizeConstants.iconXS, color: color),
            SizedBox(width: SizeConstants.spaceXS),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: SizeConstants.fontXS,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (trip.status) {
      case 'scheduled':
        return Colors.blue;
      case 'in_progress':
        return Colors.green;
      case 'completed':
        return Colors.grey;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon() {
    switch (trip.status) {
      case 'scheduled':
        return Icons.schedule;
      case 'in_progress':
        return Icons.directions_bus;
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  String _getStatusText() {
    switch (trip.status) {
      case 'scheduled':
        return 'مجدولة';
      case 'in_progress':
        return 'جارية';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      default:
        return 'غير معروف';
    }
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

import 'package:flutter/material.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../core/constants/size_constants.dart';

/// Trip Details Header Widget
/// Displays trip information header with statistics
/// Based on the original SchoolX project structure
class TripDetailsHeader extends StatelessWidget {
  final String tripId;
  final String tripType;
  final String busName;
  final String supervisorName;
  final String driverName;
  final String date;
  final int totalStudents;
  final int presentCount;
  final int absentCount;
  final Color statusColor;
  final String statusText;

  const TripDetailsHeader({
    super.key,
    required this.tripId,
    required this.tripType,
    required this.busName,
    required this.supervisorName,
    required this.driverName,
    required this.date,
    required this.totalStudents,
    required this.presentCount,
    required this.absentCount,
    required this.statusColor,
    required this.statusText,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        minHeight: ResponsiveUtils.isMobile(context) ? 200 : 180,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            ColorConstants.primary,
            ColorConstants.primary.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(30),
          bottomRight: Radius.circular(30),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(SizeConstants.spaceMD),
        child: Column(
          children: [
            // Trip type and status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    tripType,
                    style: TextStyle(
                      fontSize: ResponsiveUtils.getFontSize(20),
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: SizeConstants.spaceSM,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(SizeConstants.radiusXL),
                  ),
                  child: Text(
                    statusText,
                    style: TextStyle(
                      fontSize: ResponsiveUtils.getFontSize(12),
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(height: SizeConstants.spaceBase),

            // Trip details
            if (ResponsiveUtils.isMobile(context))
              _buildMobileLayout()
            else
              _buildDesktopLayout(),

            SizedBox(height: SizeConstants.spaceBase),

            // Statistics
            _buildStatistics(),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      children: [
        _buildInfoRow('رقم الرحلة', tripId),
        SizedBox(height: SizeConstants.spaceXS),
        _buildInfoRow('اسم الباص', busName),
        SizedBox(height: SizeConstants.spaceXS),
        _buildInfoRow('المشرف', supervisorName),
        SizedBox(height: SizeConstants.spaceXS),
        _buildInfoRow('التاريخ', date),
      ],
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        Expanded(
          child: Column(
            children: [
              _buildInfoRow('رقم الرحلة', tripId),
              SizedBox(height: SizeConstants.spaceXS),
              _buildInfoRow('اسم الباص', busName),
            ],
          ),
        ),
        SizedBox(width: SizeConstants.spaceMD),
        Expanded(
          child: Column(
            children: [
              _buildInfoRow('المشرف', supervisorName),
              SizedBox(height: SizeConstants.spaceXS),
              _buildInfoRow('التاريخ', date),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            '$label:',
            style: TextStyle(
              fontSize: ResponsiveUtils.getFontSize(14),
              color: Colors.white70,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value.isNotEmpty ? value : 'غير محدد',
            style: TextStyle(
              fontSize: ResponsiveUtils.getFontSize(14),
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatistics() {
    return Container(
      padding: EdgeInsets.all(SizeConstants.spaceBase),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              icon: Icons.people_rounded,
              label: 'إجمالي الطلاب',
              value: totalStudents.toString(),
              color: Colors.white,
            ),
          ),
          Container(
            width: 1,
            height: SizeConstants.buttonHeightMD,
            color: Colors.white.withValues(alpha: 0.3),
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.check_circle_rounded,
              label: 'الحاضرين',
              value: presentCount.toString(),
              color: Colors.green[300]!,
            ),
          ),
          Container(
            width: 1,
            height: SizeConstants.buttonHeightMD,
            color: Colors.white.withValues(alpha: 0.3),
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.cancel_rounded,
              label: 'الغائبين',
              value: absentCount.toString(),
              color: Colors.red[300]!,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: ResponsiveUtils.getFontSize(24)),
        SizedBox(height: SizeConstants.spaceXS),
        Text(
          value,
          style: TextStyle(
            fontSize: ResponsiveUtils.getFontSize(18),
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: ResponsiveUtils.getFontSize(12),
            color: Colors.white70,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

/* Enhanced CSS for Language Selection Page - Web Optimization */

/* Performance optimizations */
* {
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Smooth scrolling for better UX */
html {
  scroll-behavior: smooth;
}

/* Body optimizations */
body {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Container optimizations */
.language-container {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Animation optimizations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translate3d(-30px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translate3d(30px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Language option hover effects */
.language-option {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  will-change: transform, box-shadow;
}

.language-option:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.language-option:active {
  transform: translateY(-2px) scale(1.01);
}

/* Button hover effects */
.continue-button {
  transition: all 0.3s ease;
  cursor: pointer;
  will-change: transform, box-shadow;
}

.continue-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(74, 92, 208, 0.3);
}

.continue-button:active {
  transform: translateY(0);
}

/* Flag image optimizations */
.flag-image {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  will-change: transform;
}

/* Logo optimizations */
.logo-container {
  will-change: transform;
  transform: translateZ(0);
}

/* Responsive design */
@media (max-width: 768px) {
  .language-container {
    padding: 16px;
  }
  
  .language-option {
    margin-bottom: 16px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .language-container {
    padding: 24px;
  }
}

@media (min-width: 1025px) {
  .language-container {
    padding: 32px;
  }
  
  .language-option:hover {
    transform: translateY(-8px) scale(1.05);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    color: #e0e0e0;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast support */
@media (prefers-contrast: high) {
  .language-option {
    border-width: 3px;
  }
  
  .continue-button {
    border: 2px solid #000;
  }
}

/* Print styles */
@media print {
  .language-container {
    background: white !important;
    color: black !important;
  }
  
  .continue-button {
    border: 2px solid black;
    background: white !important;
    color: black !important;
  }
}

/* Performance optimizations for animations */
.animate-fade-in {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-slide-left {
  animation: slideInLeft 0.6s ease-out forwards;
}

.animate-slide-right {
  animation: slideInRight 0.6s ease-out forwards;
}

/* GPU acceleration for better performance */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Loading state optimizations */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Focus styles for accessibility */
.language-option:focus,
.continue-button:focus {
  outline: 3px solid #4A5CD0;
  outline-offset: 2px;
}

/* Selection indicator animation */
.selection-indicator {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Optimized gradients */
.gradient-background {
  background: linear-gradient(
    135deg,
    rgba(74, 92, 208, 0.1) 0%,
    rgba(255, 255, 255, 1) 50%,
    rgba(108, 99, 255, 0.05) 100%
  );
}

/* Container queries support (future-proofing) */
@container (min-width: 768px) {
  .language-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
  }
}

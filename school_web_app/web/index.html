<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

  <!-- SEO Meta Tags -->
  <meta name="description" content="نظام باصاتي الشامل لإدارة النقل المدرسي - تتبع الباصات، إدارة الطلاب، والمزيد">
  <meta name="keywords" content="باصاتي, نقل مدرسي, إدارة باصات, تتبع طلاب, مدرسة, نقل">
  <meta name="author" content="Busaty Team">
  <meta name="robots" content="index, follow">

  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="باصاتي - نظام إدارة النقل المدرسي">
  <meta property="og:description" content="نظام شامل لإدارة النقل المدرسي مع تتبع الباصات في الوقت الفعلي">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://busaty.com">
  <meta property="og:image" content="icons/Icon-512.png">

  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="باصاتي - نظام إدارة النقل المدرسي">
  <meta name="twitter:description" content="نظام شامل لإدارة النقل المدرسي مع تتبع الباصات في الوقت الفعلي">
  <meta name="twitter:image" content="icons/Icon-512.png">

  <!-- Performance Optimization -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://maps.googleapis.com">
  <link rel="dns-prefetch" href="https://stage.busatyapp.com">

  <!-- Resource Hints -->
  <link rel="preload" href="flutter_bootstrap.js" as="script">
  <link rel="prefetch" href="icons/Icon-192.png">
  <link rel="prefetch" href="icons/Icon-512.png">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="apple-mobile-web-app-title" content="باصاتي">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <link rel="apple-touch-icon" sizes="152x152" href="icons/Icon-192.png">
  <link rel="apple-touch-icon" sizes="180x180" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="favicon.png"/>
  <link rel="icon" type="image/png" sizes="16x16" href="favicon.png"/>

  <title>باصاتي - نظام إدارة النقل المدرسي</title>
  <link rel="manifest" href="manifest.json">

  <!-- Google Maps API with async loading -->
  <script>
    function initGoogleMaps() {
      const script = document.createElement('script');
      script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyDmpwqnRpySPiDVht0Rg6sPh1ZP3cBF8fc&libraries=geometry,places&callback=onGoogleMapsLoaded';
      script.async = true;
      script.defer = true;
      document.head.appendChild(script);
    }

    function onGoogleMapsLoaded() {
      window.googleMapsLoaded = true;
    }

    // Load Google Maps after page load
    window.addEventListener('load', initGoogleMaps);
  </script>

  <!-- Loading Screen -->
  <style>
    .loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #4A5CD0 0%, #6366F1 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .loading-logo {
      width: 80px;
      height: 80px;
      margin-bottom: 24px;
      animation: pulse 2s infinite;
    }

    .loading-text {
      color: white;
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 16px;
      text-align: center;
    }

    .loading-subtitle {
      color: rgba(255, 255, 255, 0.8);
      font-size: 16px;
      text-align: center;
      margin-bottom: 32px;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.1); }
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .fade-out {
      opacity: 0;
      transition: opacity 0.5s ease-out;
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div id="loading-screen" class="loading-screen">
    <div class="loading-logo">
      <svg viewBox="0 0 24 24" fill="white">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
      </svg>
    </div>
    <div class="loading-text">باصاتي</div>
    <div class="loading-subtitle">نظام إدارة النقل المدرسي</div>
    <div class="loading-spinner"></div>
  </div>

  <!-- Flutter App -->
  <script>
    // Register Service Worker
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
          .then(function(registration) {
            console.log('Service Worker registered successfully:', registration.scope);
          })
          .catch(function(error) {
            console.log('Service Worker registration failed:', error);
          });
      });
    }

    // Hide loading screen when Flutter is ready
    window.addEventListener('flutter-first-frame', function() {
      const loadingScreen = document.getElementById('loading-screen');
      loadingScreen.classList.add('fade-out');
      setTimeout(() => {
        loadingScreen.style.display = 'none';
      }, 500);
    });

    // Performance monitoring
    window.addEventListener('load', function() {
      // Log page load time
      const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
      console.log('Page load time:', loadTime + 'ms');

      // Monitor largest contentful paint
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'largest-contentful-paint') {
              console.log('Largest Contentful Paint:', entry.startTime + 'ms');
            }
          }
        });
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
      }
    });
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
